{"version": 3, "file": "forum.js", "sources": ["../src/forum/index.ts"], "sourcesContent": ["import app from 'flarum/forum/app';\nimport { extend } from 'flarum/extend';\nimport TagsPage from 'flarum/tags/components/TagsPage';\n\nfunction applyTagBackgrounds(root: Element | null) {\n  if (!root) return;\n\n  const tiles = root.querySelectorAll('li.TagTile');\n  tiles.forEach((tile) => {\n    const li = tile as HTMLElement;\n    const link = li.querySelector('a.TagTile-info') as HTMLAnchorElement | null;\n    const nameEl = li.querySelector('.TagTile-name') as HTMLElement | null;\n\n    if (!link) return;\n\n    let slug: string | null = null;\n\n    const url = new URL(link.href, window.location.origin);\n    // Flarum tags routes are typically /t/:slug or /tags/:slug\n    const parts = url.pathname.split('/').filter(Boolean);\n    const tIndex = parts.indexOf('t');\n    const tagsIndex = parts.indexOf('tags');\n    if (tIndex !== -1 && parts[tIndex + 1]) slug = parts[tIndex + 1];\n    else if (tagsIndex !== -1 && parts[tagsIndex + 1]) slug = parts[tagsIndex + 1];\n    else if (parts.length > 0) slug = parts[parts.length - 1];\n\n    if (!slug) return;\n\n    // Find tag by slug in the store\n    // @ts-ignore types provided by flarum/tags\n    const tags = app.store.all('tags') as any[];\n    const model = tags.find((t) => (typeof t.slug === 'function' ? t.slug() : t.attribute && t.attribute('slug')) === slug);\n\n    if (!model) return;\n\n    const bgUrl = model.attribute ? model.attribute('wusong8899BackgroundURL') : null;\n\n    if (bgUrl) {\n      li.style.background = `url(${bgUrl})`;\n      li.style.backgroundSize = 'cover';\n      li.style.backgroundPosition = 'center';\n      li.style.backgroundRepeat = 'no-repeat';\n      if (nameEl) {\n        // Maintain existing behavior: hide name when a background image is set\n        nameEl.style.display = 'none';\n      }\n    } else {\n      // Reset to default behavior that uses --tag-bg (set by Flarum core)\n      li.style.background = '';\n      li.style.backgroundSize = '';\n      li.style.backgroundPosition = '';\n      li.style.backgroundRepeat = '';\n      if (nameEl) {\n        nameEl.style.display = '';\n      }\n    }\n  });\n}\n\napp.initializers.add('wusong8899-tag-background', () => {\n  extend(TagsPage.prototype, 'oncreate', function (this: any, vnode: any) {\n    applyTagBackgrounds(vnode.dom as Element);\n  });\n\n  extend(TagsPage.prototype, 'onupdate', function (this: any, vnode: any) {\n    applyTagBackgrounds(vnode.dom as Element);\n  });\n});\n\n"], "names": ["applyTagBackgrounds", "root", "tile", "li", "link", "nameEl", "slug", "parts", "tIndex", "tagsIndex", "model", "app", "t", "bgUrl", "extend", "TagsPage", "vnode"], "mappings": "8BAIA,SAASA,EAAoBC,EAAsB,CACjD,GAAI,CAACA,EAAM,OAEGA,EAAK,iBAAiB,YAAY,EAC1C,QAASC,GAAS,CACtB,MAAMC,EAAKD,EACLE,EAAOD,EAAG,cAAc,gBAAgB,EACxCE,EAASF,EAAG,cAAc,eAAe,EAE/C,GAAI,CAACC,EAAM,OAEX,IAAIE,EAAsB,KAI1B,MAAMC,EAFM,IAAI,IAAIH,EAAK,KAAM,OAAO,SAAS,MAAM,EAEnC,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO,EAC9CI,EAASD,EAAM,QAAQ,GAAG,EAC1BE,EAAYF,EAAM,QAAQ,MAAM,EAKtC,GAJIC,IAAW,IAAMD,EAAMC,EAAS,CAAC,EAAGF,EAAOC,EAAMC,EAAS,CAAC,EACtDC,IAAc,IAAMF,EAAME,EAAY,CAAC,EAAGH,EAAOC,EAAME,EAAY,CAAC,EACpEF,EAAM,OAAS,MAAUA,EAAMA,EAAM,OAAS,CAAC,GAEpD,CAACD,EAAM,OAKX,MAAMI,EADOC,EAAI,MAAM,IAAI,MAAM,EACd,KAAMC,IAAO,OAAOA,EAAE,MAAS,WAAaA,EAAE,KAAA,EAASA,EAAE,WAAaA,EAAE,UAAU,MAAM,KAAON,CAAI,EAEtH,GAAI,CAACI,EAAO,OAEZ,MAAMG,EAAQH,EAAM,UAAYA,EAAM,UAAU,yBAAyB,EAAI,KAEzEG,GACFV,EAAG,MAAM,WAAa,OAAOU,CAAK,IAClCV,EAAG,MAAM,eAAiB,QAC1BA,EAAG,MAAM,mBAAqB,SAC9BA,EAAG,MAAM,iBAAmB,YACxBE,IAEFA,EAAO,MAAM,QAAU,UAIzBF,EAAG,MAAM,WAAa,GACtBA,EAAG,MAAM,eAAiB,GAC1BA,EAAG,MAAM,mBAAqB,GAC9BA,EAAG,MAAM,iBAAmB,GACxBE,IACFA,EAAO,MAAM,QAAU,IAG7B,CAAC,CACH,CAEAM,EAAI,aAAa,IAAI,4BAA6B,IAAM,CACtDG,EAAAA,OAAOC,EAAS,UAAW,WAAY,SAAqBC,EAAY,CACtEhB,EAAoBgB,EAAM,GAAc,CAC1C,CAAC,EAEDF,EAAAA,OAAOC,EAAS,UAAW,WAAY,SAAqBC,EAAY,CACtEhB,EAAoBgB,EAAM,GAAc,CAC1C,CAAC,CACH,CAAC"}