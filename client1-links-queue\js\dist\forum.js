(()=>{var t={n:e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return t.d(r,{a:r}),r},d:(e,r)=>{for(var o in r)t.o(r,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:r[o]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};(()=>{"use strict";const e=flarum.core.compat["forum/app"];var r=t.n(e);function o(t,e){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},o(t,e)}flarum.core.compat.extend;const n=flarum.core.compat.Model;var a=t.n(n),u=function(t){function e(){return t.apply(this,arguments)||this}var r,n;return n=t,(r=e).prototype=Object.create(n.prototype),r.prototype.constructor=r,o(r,n),e}(a());Object.assign(u.prototype,{id:a().attribute("id"),name:a().attribute("name"),links:a().attribute("links"),sort:a().attribute("sort")}),r().initializers.add("wusong8899-client1-links-queue",function(){r().store.models.linksQueueList=u})})(),module.exports={}})();
//# sourceMappingURL=forum.js.map