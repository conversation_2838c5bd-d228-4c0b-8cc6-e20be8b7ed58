{"version": 3, "file": "forum.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFf,EAAyBM,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,M,+BCLvD,MAAM,EAA+BC,OAAOC,KAAKC,OAAe,OCA1D,EAA+BF,OAAOC,KAAKC,OAAO,sB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,+B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,oC,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,qB,aCAzC,SAASC,EAAgBhB,EAAGiB,GAMzC,OALAD,EAAkBf,OAAOiB,gBAAkB,SAAyBlB,EAAGiB,GAErE,OADAjB,EAAEmB,UAAYF,EACPjB,GAGFgB,EAAgBhB,EAAGiB,GCLb,SAASG,EAAeC,EAAUC,GAC/CD,EAASd,UAAYN,OAAOsB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCH,EAAeG,EAAUC,GCJ3B,MAAM,EAA+BT,OAAOC,KAAKC,OAAc,M,aCE1CU,EAAAA,SAAAA,G,kEAAAA,CAAsBC,KAC3CzB,OAAO0B,OAAOF,EAAclB,UAAW,CACrCqB,GAAIF,IAAAA,UAAgB,MACpBG,cAAeH,IAAAA,UAAgB,wBAC/BI,MAAOJ,IAAAA,UAAgB,SACvBK,WAAYL,IAAAA,UAAgB,eAC5BM,SAAUN,IAAAA,OAAa,YACvBO,WAAYP,IAAAA,OAAa,gBCT3B,MAAM,EAA+Bb,OAAOC,KAAKC,OAAO,a,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,oB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,yB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,uB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,2B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,2B,aCAzC,SAASmB,IAetB,OAdAA,EAAWjC,OAAO0B,QAAU,SAAUQ,GACpC,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CACzC,IAAIG,EAASF,UAAUD,GAEvB,IAAK,IAAIrC,KAAOwC,EACVtC,OAAOM,UAAUC,eAAeC,KAAK8B,EAAQxC,KAC/CoC,EAAOpC,GAAOwC,EAAOxC,IAK3B,OAAOoC,GAGFD,EAASM,MAAMC,KAAMJ,WCf9B,MAAM,EAA+BxB,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,yB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,2B,aCKnC2B,EAAAA,W,yCACnBC,KAAA,SAAKC,GAAO,WACV,KAAGA,EAAMN,OAAO,GAAKG,KAAKI,SAA1B,CAIIC,IAAAA,MAAAA,oBACFA,IAAAA,MAAAA,kBAA8B,IAGhCL,KAAKG,MAAQA,EACb,IAAMG,EAAgBD,IAAAA,QAAAA,KAAAA,KAEtB,GAAKA,IAAAA,MAAAA,kBAA4BL,KAAKG,OAWpC,MAAO,CACL,QAAII,UAAU,mBAAmBF,IAAAA,WAAAA,MAAqB,kDACtDA,IAAAA,MAAAA,kBAA4BL,KAAKG,OAAOK,KAAI,SAACC,GAC3C,GAAGH,GAAeG,EAAKtB,KAAK,CAC1B,IAAMuB,EAAOC,GAAAA,CAASF,GAChBG,EAAW,CAACC,GAAAA,CAAUH,EAAKI,KAAM,EAAKX,QAE5C,OACE,QAAII,UAAU,eAAe,aAAY,SAAWE,EAAKtB,MACvD,OAAG,aAAY,SAAWsB,EAAKtB,MAC5B4B,GAAAA,CAAON,GADV,KAEQC,EAFR,CAEcI,UAAME,EAAWJ,SAAAA,WArBzCZ,KAAKI,SAAU,EAEfC,IAAAA,MAAAA,kBAA4BL,KAAKG,OAAS,GAC1CE,IAAAA,MAAAA,KACQ,QAAS,CACbY,OAAQ,CAAEC,EAAGlB,KAAKG,MAAQ,cAC1BgB,KAAM,CAAEC,MAAO,KAEhBC,KAAKrB,KAAKsB,YAAYC,KAAKvB,S,EAsBlCsB,YAAA,SAAYE,GAAS,WACnBA,EAAQC,QAAQC,KAAKlB,KAAI,SAACmB,GACxB,IAAIlB,EAAOJ,IAAAA,MAAAA,QAAkB,QAASsB,EAAOxC,IAC7CkB,IAAAA,MAAAA,kBAA4B,EAAKF,OAAOyB,KAAKnB,MAE/CT,KAAKI,SAAU,EACfyB,EAAEC,U,EAlDe7B,GCLrB,MAAM,EAA+B7B,OAAOC,KAAKC,OAAO,0B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,sC,aCAnBF,OAAOC,KAAKC,OAAO,sB,ICWnCyD,EAAAA,SAAAA,G,wIAEnBC,eAAAA,E,oCAEAC,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACblC,KAAKgC,UAAYG,KAAKC,SAASC,SAAS,IAAIC,UAAU,I,EAGxDC,SAAA,SAASL,GAAO,WACd,YAAMK,SAAN,UAAeL,GAEf,IAAMM,EAAUxC,KAEhBA,KAAKyC,EAAE,mBAAmBC,GAAG,SAAS,SAACC,GACrC,IAAMjD,EAAS,EAAK+C,EAAE,wBAEtBD,EAAQI,aAAalD,EAAOgC,KAAK,UACjCc,EAAQC,EAAE,oBAAoBI,WAGhC7C,KAAKyC,EAAE,mBAAmBC,GAAG,cAAc,SAACC,GAC1C,IAAMjD,EAAS,EAAK+C,EAAEE,EAAEjD,OAAOoD,YAE/BN,EAAQI,aAAalD,EAAOgC,KAAK,UACjCc,EAAQC,EAAE,oBAAoBI,WAGhCJ,EAAE,oBACCC,GAAG,SAAS,WACXK,aAAa,EAAKC,aAClB,EAAKC,UAAW,EAChB,EAAKD,YAAcE,YAAW,WAC5B,EAAKD,UAAW,EAChBpB,EAAEC,WACD,QAEJY,GAAG,WAAW,WACbK,aAAa,EAAKC,gBAGtB,YAAMT,SAAN,UAAeL,I,EAGjBhC,KAAA,WAAO,gBACsC,IAAhCF,KAAKmD,YAAYC,YAC1BpD,KAAKmD,YAAYE,SAAS,IAG5B,IAAMjD,EAAUJ,KAAKmD,YAAYC,YAAcpD,KAAKmD,YAAYC,WAAWvD,QAAU,EAEhFG,KAAKsD,UACRtD,KAAKsD,QAAUtD,KAAKuD,cAAcC,WAGpC,IAAMC,EAAoBzD,KAAK0D,MAAMC,WAAWH,UAEhD,OACE,SAAKI,KAAK,SAASrD,UAAU,UAC3B,SAAKA,UAAU,2CAA2C,YAAU,UAClE,SAAKsD,MAAM,kFAAkFxD,IAAAA,WAAAA,MAAqB,sDAEtF,IAA3BoD,EAAkB5D,QACjB,SAAKgE,MAAM,0CAA0CC,MAAM,+BAA+BzD,IAAAA,WAAAA,MAAqB,+DAGhHL,KAAK0D,MACHC,WACAH,UACAhD,KAAI,SAACuD,GACJ,IAAMC,EAAWrD,GAAAA,CAASoD,GACpBE,EAAalD,GAAAA,CAAOgD,GACpBG,EAASH,EAAUrC,KAAKvC,GAG9B,OAFA,EAAKuE,MAAMS,cAAcD,GAAU,EAGjC,SAAKJ,MAAM,8BAA8BM,QAAS,SAACzB,GAAD,OAAO,EAAK0B,gBAAgBN,EAAWpB,KAAI,UAAMmB,MAAM,sBAAsBG,GAA/H,IAAmJD,OAK3J,SAAKzD,UAAU,cACb,WAAO+D,IAAA,oCAAyCtE,KAAKgC,WAAc3B,IAAAA,WAAAA,MAAqB,0DAExF,SAAKE,UAAU,6CACb,WACEpB,GAAE,oCAAsCa,KAAKgC,UAC7CzB,UAAWgE,GAAAA,CAAU,kBAAmB,cAAe,CACrDC,OAAQxE,KAAKmD,YAAYC,WACzBqB,UAAWzE,KAAKmD,YAAYC,WAC5BsB,SAAU1E,KAAKmD,YAAYC,WAC3BhD,UAAWJ,KAAK2E,iBAElBC,KAAK,SACLC,YAAaC,GAAAA,CAAYzE,IAAAA,WAAAA,MAAqB,sEAC9ClC,MAAO6B,KAAKmD,YAAYC,WACxB2B,QAAS,SAACpC,GAAD,OAAO,EAAKQ,YAAYE,SAASV,EAAEjD,OAAOvB,QACnD6G,QAAS,kBAAO,EAAKC,UAAW,GAChCC,OAAQ,kBAAO,EAAKD,UAAW,KAEjC,QACE1E,UAAWgE,GAAAA,CAAU,gBAAiB,iBAAkB,OAAQ,CAC9DY,KAAM/E,KAGNJ,KAAKiD,SAEHjD,KAAKsD,QAAQ9C,KAAI,SAACV,GAAD,OAAYA,EAAOI,KAAK,EAAKiD,YAAYC,eAD1DgC,IAAAA,UAA2B,CAAEC,KAAM,OAAQ9E,UAAW,0C,EAStEgD,YAAA,WACE,IAAM+B,EAAQ,IAAIC,KAElB,OADAD,EAAME,IAAI,QAAS,IAAIvF,GAChBqF,G,EAGT1C,aAAA,SAAazE,GACX,IAAIsH,EAAStH,EAAMuH,MAAM,KACrBd,EAAOa,EAAO,GACdtG,EAAKsG,EAAO,GACZ1B,EAAY/D,KAAK2F,cAAcf,EAAMzF,GACnC+E,EAASH,EAAUrC,KAAKvC,GAE9Ba,KAAK0D,MAAMC,WAAW6B,IAAIrH,EAAO4F,GACjC/D,KAAK0D,MAAMS,cAAcD,GAAU,EACnClE,KAAKmD,YAAYyC,QACjB5F,KAAK0D,MAAMmC,UAAU7F,KAAK8F,gBAC1B9F,KAAK0D,MAAMqC,Y,EAGb1B,gBAAA,SAAgBN,EAAWpB,GACzBA,EAAEqD,iBAEF,IAAM9B,EAASH,EAAUrC,KAAKvC,UACvBa,KAAK0D,MAAMS,cAAcD,GAGhClE,KAAK0D,MAAMC,WAAWsC,OAAOrB,SAAab,EAAU5E,MACpDa,KAAK0D,MAAMmC,UAAU7F,KAAK8F,gBAC1B9F,KAAK0D,MAAMqC,Y,EAGbD,aAAA,WAEE,OAD2BrD,EAAE,uBAAuByD,MAC1B1I,OAAO2I,KAAKnG,KAAK0D,MAAMS,eAAetE,Q,EAGlE8F,cAAA,SAAcS,EAAOjH,GACnB,OAAOkB,IAAAA,MAAAA,QAAkB+F,EAAOjH,I,EAzJf4C,CAAiCsE,KCXtD,MAAM,EAA+BjI,OAAOC,KAAKC,OAAO,2B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCInCgI,EAAAA,SAAAA,G,oFAGnBrE,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,I,EAGf3B,UAAA,WACE,MAAO,gB,EAGTgG,MAAA,WACE,OAAOlG,IAAAA,WAAAA,MAAqB,sD,EAG9BmG,QAAA,WACE,MAAO,CACL,SAAKjG,UAAU,cACb,SAAKsD,MAAM,qBACN4C,IAAAA,UAAiB,CACd5C,MAAM,aACNtD,UAAW,yBACX6D,QAAS,WACPsC,SAASC,WAGbtG,IAAAA,WAAAA,MAAqB,sC,EA1BdiG,CAAkCM,KAAlCN,EACZO,eAAgB,E,ICMJC,EAAAA,SAAAA,G,oFAGnB7E,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACblC,KAAK2D,SAAWoD,GAAAA,CAAO,IAAIxB,MAC3BvF,KAAKmE,cAAgB,GACrBnE,KAAKgH,UAAY3G,IAAAA,MAAAA,UAAoB,8BAAgC,UAErE,IAAMb,EAAaQ,KAAK0D,MAAMjD,KAC3BjB,IACDQ,KAAK2D,WAAW6B,IAAI,SAAWhG,EAAWL,KAAMK,GAChDQ,KAAKmE,cAAc3E,EAAWL,OAGhCa,KAAKiH,gBAAkB,IAAIC,KAC3BlH,KAAK6F,UAAYkB,GAAAA,CAAO,I,EAG1BxG,UAAA,WACE,MAAO,gB,EAGTgG,MAAA,WACE,OAAOlG,IAAAA,WAAAA,MAAqB,8C,EAG9BmG,QAAA,WAAU,WACR,OACE,SAAKjG,UAAU,cACb,SAAKA,UAAU,QACb,SAAKsD,MAAM,uBAAuBtD,UAAU,2BACzCwB,EAAyBoF,UAAU,CAClCC,MAAOpH,KAAKiH,gBACZtD,SAAU3D,KAAK2D,SACfQ,cAAenE,KAAKmE,cACpB0B,UAAW7F,KAAK6F,UAChBE,SAAU,WACRlE,EAAEC,aAKR,SAAKvB,UAAU,cACb,eAAQF,IAAAA,WAAAA,MAAqB,mDAAoDL,KAAKgH,UAAUK,QAAQ,UAAWhH,IAAAA,QAAAA,KAAAA,UAA2B,WAC9I,WAAOlB,GAAG,qBAAqB0F,YAAaxE,IAAAA,WAAAA,MAAqB,+DAAgEiH,UAAQ,EAAC/G,UAAU,cAAcqE,KAAK,SAAS2C,KAAK,MAAMC,IAAI,IAAIzC,QAAS,SAACpC,GAAD,OAAO,EAAK8E,0BACxN,SAAK5D,MAAM,oBAAoBxD,IAAAA,WAAAA,MAAqB,gDAAgD,UAAMlB,GAAG,sBAAsBa,KAAKgH,UAAUK,QAAQ,UAAWrH,KAAK6F,gBAG5K,SAAKtF,UAAU,cACb,eAAQF,IAAAA,WAAAA,MAAqB,oDAC7B,cAAUlB,GAAG,0BAA0BuI,UAAU,MAAMnH,UAAU,iBAGnE,SAAKA,UAAU,aAAasD,MAAM,uBAC/B4C,IAAAA,UACC,CACElG,UAAW,yBACXqE,KAAM,SACNxE,QAASJ,KAAKI,SAEhBC,IAAAA,WAAAA,MAAqB,kCAPzB,IASGoG,IAAAA,UACC,CACElG,UAAW,mCACXH,QAASJ,KAAKI,QACdgE,QAAS,WACP,EAAKuD,OAE4B,mBAAvB,EAAKjE,MAAMqC,UACnB,EAAKrC,MAAMqC,aAIjB1F,IAAAA,WAAAA,MAAqB,0C,EAQjCuH,kBAAA,WACE,IAAIC,EAAqBC,WAAWrF,EAAE,uBAAuByD,OAM7D,OAJG6B,MAAMF,KACPA,EAAqB,GAGhBrK,OAAO2I,KAAKnG,KAAKmE,eAAetE,OAAOgI,G,EAGhDJ,qBAAA,WACE,IAAMO,EAAiBhI,KAAK4H,oBACtBK,EAAqBjI,KAAKgH,UAAUK,QAAQ,UAAWW,GAC7DvF,EAAE,uBAAuB3B,KAAKmH,I,EAGhCC,SAAA,SAASvF,GAAG,WACVA,EAAEqD,iBACF,IAAMmC,EAAY9H,IAAAA,QAAAA,KAAAA,UAA2B,SACvCwH,EAAqBC,WAAWrF,EAAE,uBAAuByD,OACzDkC,EAA0BpI,KAAK4H,oBAC/BS,EAA0B5F,EAAE,4BAA4ByD,MAE9D,GAAGkC,EAAwBD,EACzB9H,IAAAA,OAAAA,KAAgBiI,IAAO,CAAC1D,KAAM,SAAUvE,IAAAA,WAAAA,MAAqB,qEAI/D,GAA4C,IAAzC7C,OAAO2I,KAAKnG,KAAKmE,eAAetE,QAKnC,GAAGgI,EAAmB,EAAE,CACtB,IAAMU,EAAoB,CACxBC,cAAcX,EACdY,mBAAmBJ,EACnBlE,cAAcuE,KAAKC,UAAUnL,OAAO2I,KAAKnG,KAAKmE,iBAGhDnE,KAAKI,SAAU,EAEfC,IAAAA,MAAAA,aACgB,iBACbuI,KAAKL,GACLlH,MACC,SAACI,GACCpB,IAAAA,MAAAA,YAAsBoB,GACtBpB,IAAAA,MAAAA,KAAeiG,GACf,EAAKlG,SAAU,EAEkB,mBAAvB,EAAKsD,MAAMqC,UACnB,EAAKrC,MAAMqC,cAVnB,OAcS,SAACpD,GACN,EAAKvC,SAAU,WA5BnBC,IAAAA,OAAAA,KAAgBiI,IAAO,CAAC1D,KAAM,SAAUvE,IAAAA,WAAAA,MAAqB,uE,EAhH9CyG,CAA2BF,KAA3BE,EACZD,eAAgB,ECZzB,MAAM,EAA+BzI,OAAOC,KAAKC,OAAO,2B,ICGnCuK,GAAAA,SAAAA,G,oFACnBC,KAAA,WACE,MAAO,qB,EAGTC,KAAA,WACE,OAAO1I,IAAAA,MAAU,uBAAwB,CACvCM,SAAUN,IAAAA,QAAAA,KAAAA,c,EAIdmG,QAAA,WACE,IAAM/F,EAAOT,KAAK0D,MAAMsF,aAAazJ,WACrC,OAAOc,IAAAA,WAAAA,MAAqB,sEAAuE,CACjGI,KAAMA,K,EAIVwI,QAAA,WACE,IAAMD,EAAehJ,KAAK0D,MAAMsF,aAAaE,UACvC9J,EAAgB4J,EAAaG,UAAU,wBACvCC,EAAaJ,EAAaG,UAAU,MAEpCE,GADYhJ,IAAAA,MAAAA,UAAoB,8BAAgC,WAC3CgH,QAAQ,UAAWjI,GAE9C,OAAOiB,IAAAA,WAAAA,MAAqB,uEAAwE,CAClGiJ,KAAMD,EACNlK,GAAGiK,K,EA3BYP,C,MAAkCU,ICHvD,MAAM,GAA+BnL,OAAOC,KAAKC,OAAO,uB,eCAxD,MAAM,GAA+BF,OAAOC,KAAKC,OAAO,yB,eCAxD,MAAM,GAA+BF,OAAOC,KAAKC,OAAkB,U,eCAnE,MAAM,GAA+BF,OAAOC,KAAKC,OAAY,I,eCA7D,MAAM,GAA+BF,OAAOC,KAAKC,OAAO,+B,eCAxD,MAAM,GAA+BF,OAAOC,KAAKC,OAAO,mB,eCAxD,MAAM,GAA+BF,OAAOC,KAAKC,OAAO,kB,eCAxD,MAAM,GAA+BF,OAAOC,KAAKC,OAAO,oB,eCKnCkL,GAAAA,SAAAA,G,4EACnBtJ,KAAA,WACE,IAAOuJ,EAAmBzJ,KAAK0D,MAAxB+F,gBACDnJ,EAAgBD,IAAIqJ,QAAQjJ,KAAKtB,KACjCwK,EAAaF,EAAgBN,UAAU,gBACvC7J,EAAamK,EAAgBnK,aAC7BC,EAAWkK,EAAgBlK,WAC3BC,EAAaiK,EAAgBjK,aAC7BJ,EAAgBqK,EAAgBrK,gBAChCwK,EAAgBH,EAAgBpK,QAChCwK,EAAoBD,GAA4BvJ,IAAIyJ,WAAWC,MAAM,gEACrEX,EAAaK,EAAgBtK,KAC7B6K,EAAe3J,IAAIyJ,WAAWC,MAAMzJ,GAAeqJ,EAAW,gDAAgD,gDAC9GM,EAAoB3J,GAAeqJ,EAAW,YAAY,cAG1DO,GADY7J,IAAI8J,MAAMhB,UAAU,8BAAgC,WAClC9B,QAAQ,UAAWjI,GAEvD,OACE,SAAKmB,UAAU,4BACb,SAAKsD,MAAM,qBACT,WAAIxD,IAAIyJ,WAAWC,MAAM,iDAAzB,MACA,UAAMlG,MAAOoG,GAAoBD,GAFnC,MAIE,WAAI3J,IAAIyJ,WAAWC,MAAM,sDAAzB,MACCzK,GAGH,SAAKuE,MAAM,qBACT,WAAIxD,IAAIyJ,WAAWC,MAAM,+CAAzB,MACCX,EAFH,MAGE,WAAI/I,IAAIyJ,WAAWC,MAAM,sDAAzB,MACA,EAAC,KAAD,CAAMhB,KAAMxJ,EAAWc,IAAI+J,MAAM3J,KAAKlB,GAAY,IAAKgB,UAAU,sBAAsBsD,MAAM,8BAC1F9C,IAAAA,CAAOxB,GADV,IACsBoB,IAAAA,CAASpB,IALjC,MAQE,WAAIc,IAAIyJ,WAAWC,MAAM,wDAAzB,MACA,EAAC,KAAD,CAAMhB,KAAMvJ,EAAaa,IAAI+J,MAAM3J,KAAKjB,GAAc,IAAKe,UAAU,sBAAsBsD,MAAM,8BAC9F9C,IAAAA,CAAOvB,GADV,IACwBmB,IAAAA,CAASnB,IAVnC,MAYE,WAAIa,IAAIyJ,WAAWC,MAAM,4DAAzB,MACCG,EAbH,IAeGN,GACC,mBACE,WAAIvJ,IAAIyJ,WAAWC,MAAM,2DAAzB,MACCF,M,EA9CML,CAAgCa,MCEhCC,GAAAA,SAAAA,G,oFACnBrI,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACblC,KAAKI,SAAU,EACfJ,KAAKuK,aAAc,EACnBvK,KAAKyJ,gBAAkB,GACvBzJ,KAAKS,KAAOT,KAAK0D,MAAM8G,OAAO/J,KAC9BT,KAAKyK,e,EAGPvK,KAAA,WAAO,WAOL,OAJIF,KAAKI,SACGgF,KAAAA,UAA2B,CAAEC,KAAM,UAI7C,aACE,SAAKxB,MAAM,2DACRxD,KAAAA,WAAAA,MAAqB,sDAExB,QAAIwD,MAAM,kEACP7D,KAAKyJ,gBAAgBjJ,KAAI,SAACiJ,GACzB,OACE,QAAI5F,MAAM,kBAAkBvG,IAAKmM,EAAgBtK,KAAM,UAASsK,EAAgBtK,MAC7EqK,GAAwBrC,UAAU,CAAEsC,gBAAAA,UAM3CzJ,KAAKI,SAAyC,IAA9BJ,KAAKyJ,gBAAgB5J,QACrC,aACE,SAAKgE,MAAM,uGAAuGxD,KAAAA,WAAAA,MAAqB,oDAI1IL,KAAK0K,kBACJ,SAAK7G,MAAM,kCACT,EAAC,IAAD,CAAQtD,UAAW,yBAA0BoK,SAAU3K,KAAKI,QAASA,QAASJ,KAAKI,QAASgE,QAAS,kBAAM,EAAKwG,aAC7GvK,KAAAA,WAAAA,MAAqB,0D,EAQlCuK,SAAA,WACE5K,KAAKI,SAAU,EACfJ,KAAKyK,YAAYzK,KAAKyJ,gBAAgB5J,S,EAGxCgL,aAAA,SAAarJ,GAMX,OALAxB,KAAKuK,cAAgB/I,EAAQC,QAAQqJ,SAAWtJ,EAAQC,QAAQqJ,MAAMC,KACtE,GAAGnJ,KAAK7B,MAAMC,KAAKyJ,gBAAiBjI,GACpCxB,KAAKI,SAAU,EACfyB,EAAEC,SAEKN,G,EAGTkJ,eAAA,WACE,OAAO1K,KAAKuK,a,EAGdE,YAAA,SAAYO,GACV,YADsB,IAAZA,IAAAA,EAAS,GACZ3K,KAAAA,MAAAA,KACC,kBAAmB,CACvBY,OAAQ,CACNR,KAAMT,KAAKS,KAAKtB,MAElBgC,KAAM,CACJ6J,OAAAA,KANC,OASE,eACN3J,KAAKrB,KAAK6K,aAAatJ,KAAKvB,Q,EA9EdsK,CAA4BD,MCJ5BY,GAAAA,SAAAA,G,oFACnBhJ,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACblC,KAAKkL,SAASrJ,EAAEuI,MAAMe,MAAM,c,EAG9B3E,QAAA,WACE,OACE,SAAKjG,UAAU,uBACZ+J,GAAoBnD,UAAU,CAC7BqD,OAAQ,CACN/J,KAAMT,KAAKS,U,EAXFwK,CAA4BG,MCUjD/K,IAAIgL,aAAa7F,IAAI,wBAAwB,WAC3CnF,IAAI+F,MAAMkF,OAAOlM,cAAgBJ,EACjCqB,IAAIkL,uBAAuBnM,cAAgByJ,GCT3CxI,IAAImL,OAAO,wBAA0B,CACnCC,KAAM,+BACNtE,UAAW8D,KAGbS,EAAAA,EAAAA,QAAON,KAAAA,UAAoB,YAAY,SAAU9F,EAAM7E,GAChDJ,IAAIqJ,QAAQjJ,MACSJ,IAAIqJ,QAAQjJ,KAAKtB,MAClBa,KAAKS,KAAKtB,MAG7BmG,EAAME,IACJ,gBACAmG,KAAAA,UAAqB,CACjB5C,KAAM1I,IAAI+J,MAAM,uBAAwB,CACtCzJ,SAAUX,KAAKS,KAAKE,aAEtBmI,KAAM,qBAER,CACEzI,IAAIyJ,WAAWC,MACb,uDAIN,QCiFV2B,EAAAA,EAAAA,QAAOE,IAAAA,UAA2B,QAAQ,SAAU1J,GAClD,GAAK7B,IAAIqJ,QAAQjJ,KAAjB,CAIA,IAAMoL,EAAYxL,IAAIyL,QAAQnO,IAAI,aAE/BkO,IACc,SAAZA,EAlHT,WAGI,GAAuC,MAFGxL,IAAI8J,MAAMhB,UAAU,qCAE9D,CAIA,IAAI4C,EAA8BC,SAASC,eAAe,+BAEzB,OAA9BF,GACDtJ,EAAEsJ,GAA6B9F,UAyG7BiG,GAnGR,SAAiCC,EAAkB1L,GACjD,IAAM2L,EAAgD,WAAjC3J,EAAE,WAAW4J,IAAI,cAChCC,EAAoCjM,IAAI8J,MAAMhB,UAAU,qCAE9D,IAAkB,IAAfiD,GACoC,MAApCE,EAAH,CAEA7J,EAAE,qCAAqC4J,IAAI,UAAU,QACrD5J,EAAE,+CAA+CwD,SAEjD,IAAIsG,EAAOC,aAAY,WACrB,GAAGL,EAAKM,MACNC,cAAcH,QAEAvL,IAAXmL,EAAKM,KAAgB,CACtBhK,EAAE,qCAAqC4J,IAAI,UAAU,QACrD5J,EAAE,+CAA+CwD,SAEjD,IAAI8F,EAA8BC,SAASC,eAAe,+BAE1D,GAAiC,OAA9BF,EACD,OAGFtJ,EAAE,kDAAkDkK,YAAY,oBAChElK,EAAE,yDAAyDmK,SAAS,gBACpE,IAAIC,EAAUpK,EAAE,qCAAqCqK,QAElDD,EAAQhN,OAAO,IAChB4C,EAAE,iBAAiBwD,SACnBxD,EAAEoK,GAASE,KAAK,KAAK,gBACrBtK,EAAEoK,GAASR,IAAI,UAAU,IACzB5J,EAAE,sCAAsCuK,QAAQH,IAGlD,IAAMI,EAAgBjB,SAASC,eAAe,kBAExCiB,GADY7M,IAAI8J,MAAMhB,UAAU,8BAAgC,WACtC9B,QAAQ,UAAWhH,IAAIqJ,QAAQjJ,KAAK0I,UAAU,WAE9E4C,EAA8BC,SAASmB,cAAc,QACzBhO,GAAK,8BACjC4M,EAA4BxL,UAAY,8CAExC,IAAM6M,EAAyBpB,SAASmB,cAAc,OACtDC,EAAuB7M,UAAY,uFAEnC,IAAM2J,EAAoB8B,SAASmB,cAAc,OACjDjD,EAAkBmD,UAAY,8GAA8GH,EAC5IhD,EAAkB3J,UAAY,yCAE9B,IAAM+M,EAAoBtB,SAASmB,cAAc,OACjDG,EAAkBD,UAAY,gCAC9BC,EAAkB/M,UAAY,yCAE9B6M,EAAuBG,YAAYrD,GACnCkD,EAAuBG,YAAYD,GAEnC,IAAME,EAA0BxB,SAASmB,cAAc,OACvDK,EAAwBH,UAAYhN,IAAIyJ,WAAWC,MAAM,6CACzDyD,EAAwBjN,UAAY,yFAEpCkC,EAAE+K,GAAyBC,OAAM,WAC/BpN,IAAIqN,MAAMC,KAAK7G,MAGjB,IAAM8G,EAAsB5B,SAASmB,cAAc,OACnDS,EAAoBrN,UAAY,iFAEhC,IAAMsN,EAAcpL,EAAE,oDAAoDqK,QAC1ErK,EAAEoL,GAAad,KAAK,KAAK,eACzBtK,EAAEoL,GAAajB,SAAS,sBAExBnK,EAAEmL,GAAqBE,KAAKD,GAE5B,IAAIE,EAAgB,GACpBtL,EAAEoL,GAAanL,GAAG,SAAS,WACzBqL,EAAgC,KAAhBA,EAAmB,OAAO,GAC1CtL,EAAE,2BAA2B4J,IAAI,UAAU0B,MAG7ChC,EAA4BwB,YAAYH,GACxCrB,EAA4BwB,YAAYC,GACxCzB,EAA4BwB,YAAYK,GACxCX,EAAcM,YAAYxB,MArGhB,KAuHViC,CAAwB9L,EAAMlC,KAAK0D,MAAMjD,YFvG/CiL,EAAAA,EAAAA,QAAOuC,IAAAA,UAA4B,qBAAqB,SAAU3I,GAChEA,EAAME,IAAI,gBAAiB,CACzB9E,KAAM,gBACNoI,KAAM,qBACNoF,MAAO7N,IAAIyJ,WAAWC,MACpB,+DAKN2B,EAAAA,EAAAA,QAAOyC,IAAc,sBAAsB,SAAC7I,EAAO7E,GACjD,IAAM2N,EAAuB/N,IAAI8J,MAAMhB,UAAU,wBAE9C9I,IAAIqJ,QAAQjJ,MAAQ2N,GACC/N,IAAIqJ,QAAQjJ,KAAKtB,OAClBsB,EAAKtB,MAGxBmG,EAAME,IAAI,gBAAiBiB,IAAAA,UAAiB,CACxCqC,KAAM,oBACN1E,QAAS,kBAAM/D,IAAIqN,MAAMC,KAAK7G,EAAoB,CAACrG,KAAAA,MAClDJ,IAAIyJ,WAAWC,MAAM,mDAMhC2B,EAAAA,EAAAA,QAAOE,IAAAA,UAA2B,SAAS,SAAUtG,GAC9CjF,IAAIqJ,QAAQjJ,MAIjB6E,EAAME,IACJ,gBACAiB,IAAAA,UACE,CACEqC,KAAM,oBACN1E,QAAS,WACP/D,IAAIqN,MAAMC,KAAK7G,KAGnBzG,IAAIyJ,WAAWC,MAAM,+CAEtB,U", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['utils/UserControls']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/NotificationGrid']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/components/SessionDropdown']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Model']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/TransferMoney.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/app']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Modal']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/states/SearchState']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/utils/ItemList']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/utils/Stream']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Alert']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/components/Search']\"", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/extends.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/highlight']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/avatar']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/username']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/sources/UserSearchSource.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/utils/classList']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/utils/extractText']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/LoadingIndicator']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/models/User']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/TransferMoneySearchModal.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Modal']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/TransferMoneySuccessModal.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/TransferMoneyModal.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Notification']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/TransferMoneyNotification.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/UserPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LinkButton']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Component']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['app']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LoadingIndicator']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Link']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['helpers/avatar']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['helpers/username']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/TransferHistoryListItem.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/TransferHistoryList.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/TransferHistoryPage.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/index.ts", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addTransferMoneyPage.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addClient1CustomizationFeatures.ts"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['utils/UserControls'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/NotificationGrid'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/components/SessionDropdown'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Model'];", "import Model from \"flarum/Model\";\n\nexport default class TransferMoney extends Model {}\nObject.assign(TransferMoney.prototype, {\n  id: Model.attribute(\"id\"),\n  transferMoney: Model.attribute(\"transfer_money_value\"),\n  notes: Model.attribute(\"notes\"),\n  assignedAt: Model.attribute(\"assigned_at\"),\n  fromUser: Model.hasOne(\"fromUser\"),\n  targetUser: Model.hasOne(\"targetUser\"),\n});\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Modal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/states/SearchState'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/utils/ItemList'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/utils/Stream'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Alert'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/components/Search'];", "export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/highlight'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/avatar'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/username'];", "import app from 'flarum/forum/app';\nimport highlight from 'flarum/common/helpers/highlight';\nimport avatar from 'flarum/common/helpers/avatar';\nimport username from 'flarum/common/helpers/username';\n\nexport default class UserSearchSource {\n  view(query) {\n    if(query.length<3 || this.loading){\n      return;\n    } \n\n    if(!app.cache.userSearchResults){\n      app.cache.userSearchResults = [];\n    }\n\n    this.query = query;\n    const currentUserID = app.session.user.id();\n\n    if (!app.cache.userSearchResults[this.query]) {\n      this.loading = true;\n\n      app.cache.userSearchResults[this.query] = [];\n      app.store\n        .find('users', {\n          filter: { q: this.query + ' allows-pd' },\n          page: { limit: 5 },\n        })\n        .then(this.pushResults.bind(this));\n    } else\n      return [\n        <li className=\"Dropdown-header\">{app.translator.trans('wusong8899-transfer-money.forum.search_user_header')}</li>,\n        app.cache.userSearchResults[this.query].map((user) => {\n          if(currentUserID!=user.id()){\n            const name = username(user);\n            const children = [highlight(name.text, this.query)];\n\n            return (\n              <li className=\"SearchResult\" data-index={'users:' + user.id()}>\n                <a data-index={'users:' + user.id()}>\n                  {avatar(user)}\n                  {{ ...name, text: undefined, children }}\n                </a>\n              </li>\n            );\n          }\n        }),\n      ];\n  }\n\n  pushResults(results) {\n    results.payload.data.map((result) => {\n      var user = app.store.getById('users', result.id);\n      app.cache.userSearchResults[this.query].push(user);\n    });\n    this.loading = false;\n    m.redraw();\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/utils/classList'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/utils/extractText'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/models/User'];", "import app from 'flarum/forum/app';\nimport Search from 'flarum/forum/components/Search';\nimport UserSearchSource from './sources/UserSearchSource';\nimport ItemList from 'flarum/common/utils/ItemList';\nimport classList from 'flarum/common/utils/classList';\nimport extractText from 'flarum/common/utils/extractText';\nimport LoadingIndicator from 'flarum/common/components/LoadingIndicator';\nimport User from 'flarum/common/models/User';\nimport username from 'flarum/common/helpers/username';\nimport avatar from 'flarum/common/helpers/avatar';\n\nexport default class TransferMoneySearchModal extends Search {\n\n  inputUuid;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.inputUuid = Math.random().toString(36).substring(2);\n  }\n\n  oncreate(vnode) {\n    super.oncreate(vnode);\n\n    const $search = this;\n\n    this.$('.Search-results').on('click', (e) => {\n      const target = this.$('.SearchResult.active');\n\n      $search.addRecipient(target.data('index'));\n      $search.$('.RecipientsInput').focus();\n    });\n\n    this.$('.Search-results').on('touchstart', (e) => {\n      const target = this.$(e.target.parentNode);\n\n      $search.addRecipient(target.data('index'));\n      $search.$('.RecipientsInput').focus();\n    });\n\n    $('.RecipientsInput')\n      .on('input', () => {\n        clearTimeout(this.typingTimer);\n        this.doSearch = false;\n        this.typingTimer = setTimeout(() => {\n          this.doSearch = true;\n          m.redraw();\n        }, 900);\n      })\n      .on('keydown', () => {\n        clearTimeout(this.typingTimer);\n      });\n\n    super.oncreate(vnode);\n  }\n\n  view() {\n    if (typeof this.searchState.getValue() === 'undefined') {\n      this.searchState.setValue('');\n    }\n\n    const loading = this.searchState.getValue() && this.searchState.getValue().length >= 3;\n\n    if (!this.sources) {\n      this.sources = this.sourceItems().toArray();\n    }\n\n    const selectedUserArray = this.attrs.selected().toArray();\n\n    return (\n      <div role=\"search\" className=\"Search\">\n        <div className=\"RecipientsInput-selected RecipientsLabel\" aria-live=\"polite\">\n          <div style=\"padding-bottom:10px;font-weight:bold;font-size: 14px;color: var(--text-color);\">{app.translator.trans('wusong8899-transfer-money.forum.transfer-money-to-user')}</div>\n\n          {selectedUserArray.length===0 && (\n            <div style=\"height:34px;cursor: default !important;\" class=\"transferSearchUserContainer\">{app.translator.trans('wusong8899-transfer-money.forum.transfer-money-no-user-selected')}</div>\n          )}\n\n          {this.attrs\n            .selected()\n            .toArray()\n            .map((recipient) => {\n              const userName = username(recipient);\n              const userAvatar = avatar(recipient);\n              const userID = recipient.data.id;\n              this.attrs.selectedUsers[userID] = 1;\n\n              return (\n                <div class=\"transferSearchUserContainer\" onclick={(e) => this.removeRecipient(recipient, e)}><span class='transferSearchUser'>{userAvatar}</span> {userName}</div>\n              );\n            })}\n        </div>\n\n        <div className=\"Form-group\">\n          <label for={`transfer-money-user-search-input-${this.inputUuid}`}>{app.translator.trans('wusong8899-transfer-money.forum.transfer-money-search-user')}</label>\n\n          <div className=\"AddRecipientModal-form-input Search-input\">\n            <input\n              id={`transfer-money-user-search-input-${this.inputUuid}`}\n              className={classList('RecipientsInput', 'FormControl', {\n                open: !!this.searchState.getValue(),\n                focused: !!this.searchState.getValue(),\n                active: !!this.searchState.getValue(),\n                loading: !!this.loadingSources,\n              })}\n              type=\"search\"\n              placeholder={extractText(app.translator.trans('wusong8899-transfer-money.forum.transfer-money-search-user-placeholder'))}\n              value={this.searchState.getValue()}\n              oninput={(e) => this.searchState.setValue(e.target.value)}\n              onfocus={() => (this.hasFocus = true)}\n              onblur={() => (this.hasFocus = false)}\n            />\n            <ul\n              className={classList('Dropdown-menu', 'Search-results', 'fade', {\n                in: !!loading,\n              })}\n            >\n              {!this.doSearch\n                ? LoadingIndicator.component({ size: 'tiny', className: 'Button Button--icon Button--link' })\n                : this.sources.map((source) => source.view(this.searchState.getValue()))}\n            </ul>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  sourceItems() {\n    const items = new ItemList();\n    items.add('users', new UserSearchSource());\n    return items;\n  }\n\n  addRecipient(value) {\n    let values = value.split(':');\n    let type = values[0];\n    let id = values[1];\n    let recipient = this.findRecipient(type, id);\n    const userID = recipient.data.id;\n\n    this.attrs.selected().add(value, recipient);\n    this.attrs.selectedUsers[userID] = 1;\n    this.searchState.clear();\n    this.attrs.needMoney(this.getNeedMoney());\n    this.attrs.callback();\n  }\n\n  removeRecipient(recipient, e) {\n    e.preventDefault();\n\n    const userID = recipient.data.id;\n    delete this.attrs.selectedUsers[userID];\n\n    let type = \"users\";\n    this.attrs.selected().remove(type + ':' + recipient.id());\n    this.attrs.needMoney(this.getNeedMoney());\n    this.attrs.callback();\n  }\n\n  getNeedMoney(){\n    const moneyTransferValue = $(\"#moneyTransferInput\").val();\n    return moneyTransferValue*Object.keys(this.attrs.selectedUsers).length;\n  }\n\n  findRecipient(store, id) {\n    return app.store.getById(store, id);\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Modal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Button'];", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/common/components/Modal';\nimport Button from 'flarum/common/components/Button';\n\nexport default class TransferMoneySuccessModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-transfer-money.forum.transfer-money-success');\n  }\n\n  content() {\n    return [\n      <div className=\"Modal-body\">\n        <div style=\"text-align:center\">\n            {Button.component({\n                style:'width:66px',\n                className: 'Button Button--primary',\n                onclick: () => {\n                  location.reload();\n                }\n              },\n              app.translator.trans('wusong8899-transfer-money.forum.ok')\n            )}\n          </div>\n      </div>,\n    ];\n  }\n}", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\nimport SearchState from 'flarum/forum/states/SearchState';\nimport ItemList from 'flarum/common/utils/ItemList';\nimport Stream from 'flarum/common/utils/Stream';\nimport Alert from 'flarum/common/components/Alert';\n\nimport TransferMoneySearchModal from './TransferMoneySearchModal';\nimport TransferMoneySuccessModal from './TransferMoneySuccessModal';\n\nexport default class TransferMoneyModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.selected = Stream(new ItemList());\n    this.selectedUsers = {};\n    this.moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n\n    const targetUser = this.attrs.user;\n    if(targetUser){\n      this.selected().add('users:' + targetUser.id(), targetUser);\n      this.selectedUsers[targetUser.id()];\n    }\n    \n    this.recipientSearch = new SearchState();\n    this.needMoney = Stream(0);\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-transfer-money.forum.transfer-money');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div style=\"padding-bottom:20px;\" className=\"TransferMoneyModal-form\">\n            {TransferMoneySearchModal.component({\n              state: this.recipientSearch,\n              selected: this.selected,\n              selectedUsers: this.selectedUsers,\n              needMoney: this.needMoney,\n              callback: function(){\n                m.redraw();\n              }\n            })}\n          </div>\n\n          <div className=\"Form-group\">\n            <label>{app.translator.trans('wusong8899-transfer-money.forum.current-money-amount')}{this.moneyName.replace('[money]', app.session.user.attribute(\"money\"))}</label>\n            <input id=\"moneyTransferInput\" placeholder={app.translator.trans('wusong8899-transfer-money.forum.transfer-money-input-placeholder')} required className=\"FormControl\" type=\"number\" step=\"any\" min=\"0\" oninput={(e) => this.moneyTransferChanged()} />\n            <div style=\"padding-top:10px\">{app.translator.trans('wusong8899-transfer-money.forum.need-money-amount')}<span id=\"needMoneyContainer\">{this.moneyName.replace('[money]', this.needMoney())}</span></div>\n          </div>\n\n          <div className=\"Form-group\">\n            <label>{app.translator.trans('wusong8899-transfer-money.forum.transfer-money-notes')}</label>\n            <textarea id=\"moneyTransferNotesInput\" maxlength=\"255\" className=\"FormControl\" />\n          </div>\n\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                type: 'submit',\n                loading: this.loading,\n              },\n              app.translator.trans('wusong8899-transfer-money.forum.ok')\n            )}&nbsp;\n            {Button.component(\n              {\n                className: 'Button transferMoneyButton--gray',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n\n                  if(typeof(this.attrs.callback)===\"function\"){\n                    this.attrs.callback();\n                  }\n                }\n              },\n              app.translator.trans('wusong8899-transfer-money.forum.cancel')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  getTotalNeedMoney(){\n    let moneyTransferValue = parseFloat($(\"#moneyTransferInput\").val());\n\n    if(isNaN(moneyTransferValue)){\n      moneyTransferValue = 0;\n    }\n\n    return Object.keys(this.selectedUsers).length*moneyTransferValue;\n  }\n\n  moneyTransferChanged(){\n    const totalNeedMoney = this.getTotalNeedMoney();\n    const totalNeedMoneyText = this.moneyName.replace('[money]', totalNeedMoney);\n    $(\"#needMoneyContainer\").text(totalNeedMoneyText);\n  }\n\n  onsubmit(e) {\n    e.preventDefault();\n    const userMoney = app.session.user.attribute(\"money\");\n    const moneyTransferValue = parseFloat($(\"#moneyTransferInput\").val());\n    const moneyTransferValueTotal = this.getTotalNeedMoney();\n    const moneyTransferNotesValue = $(\"#moneyTransferNotesInput\").val();\n\n    if(moneyTransferValueTotal>userMoney){\n      app.alerts.show(Alert, {type: 'error'}, app.translator.trans('wusong8899-transfer-money.forum.transfer-error-insufficient-fund'));\n      return;\n    }\n\n    if(Object.keys(this.selectedUsers).length===0){\n      app.alerts.show(Alert, {type: 'error'}, app.translator.trans('wusong8899-transfer-money.forum.transfer-error-no-target-user-selected'));\n      return;\n    }\n\n    if(moneyTransferValue>0){\n      const moneyTransferData = {\n        moneyTransfer:moneyTransferValue,\n        moneyTransferNotes:moneyTransferNotesValue,\n        selectedUsers:JSON.stringify(Object.keys(this.selectedUsers))\n      };\n\n      this.loading = true;\n\n      app.store\n        .createRecord(\"transferMoney\")\n        .save(moneyTransferData)\n        .then(\n          (payload) => {\n            app.store.pushPayload(payload);\n            app.modal.show(TransferMoneySuccessModal);\n            this.loading = false;\n            \n            if(typeof(this.attrs.callback)===\"function\"){\n              this.attrs.callback();\n            }\n          }\n        )\n        .catch((e) => {\n          this.loading = false;\n        });\n    }\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Notification'];", "import app from 'flarum/forum/app';\nimport Notification from \"flarum/components/Notification\";\n\nexport default class TransferMoneyNotification extends Notification {\n  icon() {\n    return \"fas fa-money-bill\";\n  }\n\n  href() {\n    return app.route(\"user.transferHistory\", {\n      username: app.session.user.username(),\n    });\n  }\n\n  content() {\n    const user = this.attrs.notification.fromUser();\n    return app.translator.trans('wusong8899-transfer-money.forum.notifications.user-transfer-money-to-you', {\n      user: user,\n    });\n  }\n\n  excerpt() {\n    const notification = this.attrs.notification.subject();\n    const transferMoney = notification.attribute(\"transfer_money_value\");\n    const transferID = notification.attribute(\"id\");\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const costText = moneyName.replace('[money]', transferMoney);\n\n    return app.translator.trans('wusong8899-transfer-money.forum.notifications.user-transfer-money-details', {\n      cost: costText,\n      id:transferID\n    });\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/UserPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LinkButton'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Component'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Link'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['helpers/avatar'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['helpers/username'];", "import Component from \"flarum/Component\";\nimport Link from \"flarum/components/Link\";\nimport avatar from \"flarum/helpers/avatar\";\nimport username from \"flarum/helpers/username\";\n\nexport default class TransferHistoryListItem extends Component {\n  view() {\n    const {transferHistory} = this.attrs;\n    const currentUserID = app.session.user.id();\n    const fromUserID = transferHistory.attribute(\"from_user_id\");\n    const assignedAt = transferHistory.assignedAt();\n    const fromUser = transferHistory.fromUser();\n    const targetUser = transferHistory.targetUser();\n    const transferMoney = transferHistory.transferMoney();\n    const transferNotes = transferHistory.notes();\n    const transferNotesText = transferNotes?transferNotes:app.translator.trans('wusong8899-transfer-money.forum.transfer-list-transfer-notes-none');\n    const transferID = transferHistory.id();\n    const transferType = app.translator.trans(currentUserID==fromUserID?\"wusong8899-transfer-money.forum.transfer-money-out\":\"wusong8899-transfer-money.forum.transfer-money-in\");\n    const transferTypeStyle = currentUserID==fromUserID?\"color:red\":\"color:green\";\n\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const transferMoneyText = moneyName.replace('[money]', transferMoney);\n\n    return (\n      <div className=\"transferHistoryContainer\">\n        <div style=\"padding-top: 5px;\">\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-type')}: </b>\n          <span style={transferTypeStyle}>{transferType}</span>&nbsp;|&nbsp;\n\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-assign-at')}: </b>\n          {assignedAt}\n        </div>\n\n        <div style=\"padding-top: 5px;\">\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-id')}: </b>\n          {transferID}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-from-user')}: </b>\n          <Link href={fromUser ? app.route.user(fromUser) : \"#\"} className=\"transferHistoryUser\" style=\"color:var(--heading-color)\">\n            {avatar(fromUser)} {username(fromUser)}\n          </Link>&nbsp;|&nbsp;\n\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-target-user')}: </b>\n          <Link href={targetUser ? app.route.user(targetUser) : \"#\"} className=\"transferHistoryUser\" style=\"color:var(--heading-color)\">\n            {avatar(targetUser)} {username(targetUser)}\n          </Link>&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-transfer-amount')}: </b>\n          {transferMoneyText}&nbsp;\n\n          {transferNotes && \n            <span>|&nbsp;\n              <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-transfer-notes')}: </b>\n              {transferNotesText}\n            </span>\n          }\n        </div>\n      </div>\n    );\n  }\n}\n", "import Component from \"flarum/Component\";\nimport app from \"flarum/app\";\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\nimport Button from \"flarum/components/Button\";\n\nimport TransferHistoryListItem from \"./TransferHistoryListItem\";\n\nexport default class TransferHistoryList extends Component {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loading = true;\n    this.moreResults = false;\n    this.transferHistory = [];\n    this.user = this.attrs.params.user;\n    this.loadResults();\n  }\n\n  view() {\n    let loading;\n\n    if (this.loading) {\n      loading = LoadingIndicator.component({ size: \"large\" });\n    }\n\n    return (\n      <div>\n        <div style=\"padding-bottom:10px; font-size: 24px;font-weight: bold;\">\n          {app.translator.trans(\"wusong8899-transfer-money.forum.transfer-money-history\")}\n        </div>\n        <ul style=\"margin: 0;padding: 0;list-style-type: none;position: relative;\">\n          {this.transferHistory.map((transferHistory) => {\n            return (\n              <li style=\"padding-top:5px\" key={transferHistory.id()} data-id={transferHistory.id()}>\n                {TransferHistoryListItem.component({ transferHistory })}\n              </li>\n            );\n          })}\n        </ul>\n          \n        {!this.loading && this.transferHistory.length===0 && (\n          <div>\n            <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-transfer-money.forum.transfer-list-empty\")}</div>\n          </div>\n        )}\n\n        {this.hasMoreResults() && (\n          <div style=\"text-align:center;padding:20px\">\n            <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n              {app.translator.trans('wusong8899-transfer-money.forum.transfer-list-load-more')}\n            </Button>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.transferHistory.length);\n  }\n\n  parseResults(results) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.transferHistory, results);\n    this.loading = false;\n    m.redraw();\n\n    return results;\n  }\n\n  hasMoreResults() {\n    return this.moreResults;\n  }\n\n  loadResults(offset = 0) {\n    return app.store\n      .find(\"transferHistory\", {\n        filter: {\n          user: this.user.id(),\n        },\n        page: {\n          offset,\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n}\n", "import UserPage from \"flarum/components/UserPage\";\nimport TransferHistoryList from \"./TransferHistoryList\";\n\nexport default class TransferHistoryPage extends UserPage {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loadUser(m.route.param(\"username\"));\n  }\n\n  content() {\n    return (\n      <div className=\"TransferHistoryPage\">\n        {TransferHistoryList.component({\n          params: {\n            user: this.user,\n          },\n        })}\n      </div>\n    );\n  }\n}\n", "import { extend } from 'flarum/extend';\r\nimport UserControls from 'flarum/utils/UserControls';\r\nimport NotificationGrid from \"flarum/components/NotificationGrid\";\r\nimport SessionDropdown from 'flarum/forum/components/SessionDropdown';\r\nimport Button from 'flarum/components/Button';\r\n\r\nimport TransferMoney from \"./model/TransferMoney\";\r\nimport TransferMoneyModal from './components/TransferMoneyModal';\r\nimport TransferMoneyNotification from \"./components/TransferMoneyNotification\";\r\nimport addTransferMoneyPage from \"./addTransferMoneyPage\";\r\nimport addClient1CustomizationFeatures from \"./addClient1CustomizationFeatures\";\r\n\r\n\r\napp.initializers.add('wusong8899-money-transfer', () => {\r\n  app.store.models.transferMoney = TransferMoney;\r\n  app.notificationComponents.transferMoney = TransferMoneyNotification;\r\n\r\n  addTransferMoneyPage();\r\n  addClient1CustomizationFeatures();\r\n\r\n  extend(NotificationGrid.prototype, \"notificationTypes\", function (items) {\r\n    items.add(\"transferMoney\", {\r\n      name: \"transferMoney\",\r\n      icon: \"fas fa-dollar-sign\",\r\n      label: app.translator.trans(\r\n        \"wusong8899-transfer-money.forum.receive-transfer-from-user\"\r\n      ),\r\n    });\r\n  });\r\n\r\n  extend(UserControls, 'moderationControls', (items, user) => {\r\n    const allowUseTranferMoney = app.forum.attribute('allowUseTranferMoney');\r\n\r\n    if(app.session.user && allowUseTranferMoney){\r\n      const currentUserID = app.session.user.id();\r\n      const targetUserID = user.id();\r\n      \r\n      if(currentUserID!==targetUserID){\r\n        items.add('transferMoney', Button.component({\r\n            icon: 'fas fa-money-bill',\r\n            onclick: () => app.modal.show(TransferMoneyModal, {user})\r\n          }, app.translator.trans('wusong8899-transfer-money.forum.transfer-money'))\r\n        );\r\n      }\r\n    }\r\n  });\r\n\r\n  extend(SessionDropdown.prototype, 'items', function (items) {\r\n    if (!app.session.user) {\r\n      return;\r\n    }\r\n\r\n    items.add(\r\n      'transferMoney',\r\n      Button.component(\r\n        {\r\n          icon: 'fas fa-money-bill',\r\n          onclick: () => {\r\n            app.modal.show(TransferMoneyModal)\r\n          },\r\n        },\r\n        app.translator.trans('wusong8899-transfer-money.forum.transfer-money')\r\n      ),\r\n      -1\r\n    );\r\n  });\r\n});\r\n", "import { extend } from \"flarum/extend\";\nimport UserPage from \"flarum/components/UserPage\";\nimport LinkButton from \"flarum/components/LinkButton\";\nimport TransferHistoryPage from \"./components/TransferHistoryPage\";\n\nexport default function () {\n  app.routes[\"user.transferHistory\"] = {\n    path: \"/u/:username/transferHistory\",\n    component: TransferHistoryPage,\n  };\n\n  extend(UserPage.prototype, \"navItems\", function (items,user) {\n      if(app.session.user){\n        const currentUserID = app.session.user.id();\n        const targetUserID = this.user.id();\n\n        if(currentUserID==targetUserID){\n          items.add(\n            \"transferMoney\",\n            LinkButton.component({\n                href: app.route(\"user.transferHistory\", {\n                  username: this.user.username(),\n                }),\n                icon: \"fas fa-money-bill\",\n              },\n              [\n                app.translator.trans(\n                  \"wusong8899-transfer-money.forum.transfer-money-history\"\n                )\n              ]\n            ),\n            10\n          );\n        }\n      }\n  });\n}\n", "import { extend } from \"flarum/extend\";\nimport SessionDropdown from 'flarum/forum/components/SessionDropdown';\nimport TransferMoneyModal from './components/TransferMoneyModal';\n\nconst checkTime = 10;\n\nfunction detachTransferMoneyMenu(){\n    const moneyTransferClient1Customization = app.forum.attribute('moneyTransferClient1Customization');\n\n    if(moneyTransferClient1Customization!=='1'){\n      return;\n    }\n\n    let transferMoneyLabelContainer = document.getElementById(\"transferMoneyLabelContainer\");\n\n    if(transferMoneyLabelContainer!==null){\n      $(transferMoneyLabelContainer).remove();\n      // $(\"#app-navigation\").css(\"height\",\"var(--header-height-phone)\");\n      // $(\"#content .container .IndexPage-results\").css(\"marginTop\",\"15px\");\n    }\n}\n\nfunction attachTransferMoneyMenu(vdom: Vnode<any>, user: User): void {\n  const isMobileView = $(\"#drawer\").css('visibility')===\"hidden\";\n  const moneyTransferClient1Customization = app.forum.attribute('moneyTransferClient1Customization');\n\n  if(isMobileView===false){ return; }\n  if(moneyTransferClient1Customization!=='1'){ return; }\n\n  $(\"#content .IndexPage-nav .item-nav\").css(\"display\",\"none\");\n  $(\"#content .IndexPage-nav .item-newDiscussion\").remove();\n\n  let task = setInterval(function(){\n    if(vdom.dom){\n      clearInterval(task);\n\n      if(vdom.dom!==undefined){\n        $(\"#content .IndexPage-nav .item-nav\").css(\"display\",\"none\");\n        $(\"#content .IndexPage-nav .item-newDiscussion\").remove();\n\n        let transferMoneyLabelContainer = document.getElementById(\"transferMoneyLabelContainer\");\n\n        if(transferMoneyLabelContainer!==null){\n          return;\n        }\n\n        $(\"#content .IndexPage-nav .item-nav .ButtonGroup\").removeClass(\"App-titleControl\");\n        $(\"#content .IndexPage-nav .item-nav .ButtonGroup button\").addClass(\"Button--link\");\n        let itemNav = $(\"#content .IndexPage-nav .item-nav\").clone();\n\n        if(itemNav.length>0){\n          $(\"#itemNavClone\").remove();\n          $(itemNav).attr('id',\"itemNavClone\");\n          $(itemNav).css('display',\"\");\n          $(\"#header-secondary .Header-controls\").prepend(itemNav);\n        }\n\n        const appNavigation = document.getElementById(\"app-navigation\");\n        const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n        const userMoneyText = moneyName.replace('[money]', app.session.user.attribute(\"money\"));\n\n        transferMoneyLabelContainer = document.createElement(\"div\");\n        transferMoneyLabelContainer.id = \"transferMoneyLabelContainer\";\n        transferMoneyLabelContainer.className = \"clientCustomizeTransferMoneyButtonContainer\";\n\n        const transferMoneyContainer = document.createElement(\"div\");\n        transferMoneyContainer.className = \"clientCustomizeTransferMoneyHeaderItems clientCustomizeTransferMoneyHeaderTotalMoney\";\n\n        const transferMoneyText = document.createElement(\"div\");\n        transferMoneyText.innerHTML = '<span style=\"font-size:16px;\"><i class=\"fab fa-bitcoin\" style=\"padding-right: 8px;color: gold;\"></i></span>'+userMoneyText;\n        transferMoneyText.className = \"clientCustomizeTransferMoneyHeaderText\"\n\n        const transferMoneyIcon = document.createElement(\"div\");\n        transferMoneyIcon.innerHTML = '<i class=\"fas fa-wallet\"></i>';\n        transferMoneyIcon.className = \"clientCustomizeTransferMoneyHeaderIcon\";\n\n        transferMoneyContainer.appendChild(transferMoneyText);\n        transferMoneyContainer.appendChild(transferMoneyIcon);\n\n        const transferMoneyButtonText = document.createElement(\"div\");\n        transferMoneyButtonText.innerHTML = app.translator.trans('wusong8899-transfer-money.forum.transfer-money');\n        transferMoneyButtonText.className = \"clientCustomizeTransferMoneyHeaderItems clientCustomizeTransferMoneyHeaderTansferMoney\";\n\n        $(transferMoneyButtonText).click(function(){\n          app.modal.show(TransferMoneyModal);\n        });\n\n        const userAvatarContainer = document.createElement(\"div\");\n        userAvatarContainer.className = \"clientCustomizeTransferMoneyHeaderItems clientCustomizeTransferMoneyHeaderUser\";\n\n        const avatarClone = $(\"#header-secondary .item-session .SessionDropdown\").clone();\n        $(avatarClone).attr('id',\"avatarClone\");\n        $(avatarClone).addClass(\"App-primaryControl\");\n\n        $(userAvatarContainer).html(avatarClone);\n\n        let hideNavToggle = \"\";\n        $(avatarClone).on('click', function(){\n          hideNavToggle = hideNavToggle===\"\"?\"none\":\"\";\n          $(\"#content .IndexPage-nav\").css(\"display\",hideNavToggle);\n        });\n\n        transferMoneyLabelContainer.appendChild(transferMoneyContainer);\n        transferMoneyLabelContainer.appendChild(transferMoneyButtonText);\n        transferMoneyLabelContainer.appendChild(userAvatarContainer);\n        appNavigation.appendChild(transferMoneyLabelContainer);\n      }\n    }\n  },checkTime);\n}\n\nexport default function () {\n  extend(SessionDropdown.prototype, 'view', function (vnode) {\n    if (!app.session.user) {\n      return;\n    }\n\n    const routeName = app.current.get('routeName');\n\n    if(routeName){\n      if(routeName!==\"tags\"){\n        detachTransferMoneyMenu();\n      }else{\n        attachTransferMoneyMenu(vnode,this.attrs.user);\n      }\n    }\n  });\n}\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "flarum", "core", "compat", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "TransferMoney", "Model", "assign", "id", "transferMoney", "notes", "assignedAt", "fromUser", "targetUser", "_extends", "target", "i", "arguments", "length", "source", "apply", "this", "UserSearchSource", "view", "query", "loading", "app", "currentUserID", "className", "map", "user", "name", "username", "children", "highlight", "text", "avatar", "undefined", "filter", "q", "page", "limit", "then", "pushResults", "bind", "results", "payload", "data", "result", "push", "m", "redraw", "TransferMoneySearchModal", "inputUuid", "oninit", "vnode", "Math", "random", "toString", "substring", "oncreate", "$search", "$", "on", "e", "addRecipient", "focus", "parentNode", "clearTimeout", "typingTimer", "doSearch", "setTimeout", "searchState", "getValue", "setValue", "sources", "sourceItems", "toArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attrs", "selected", "role", "style", "class", "recipient", "userName", "userAvatar", "userID", "selectedUsers", "onclick", "removeRecipient", "for", "classList", "open", "focused", "active", "loadingSources", "type", "placeholder", "extractText", "oninput", "onfocus", "hasFocus", "onblur", "in", "LoadingIndicator", "size", "items", "ItemList", "add", "values", "split", "findRecipient", "clear", "<PERSON><PERSON><PERSON>", "getNeedMoney", "callback", "preventDefault", "remove", "val", "keys", "store", "Search", "TransferMoneySuccessModal", "title", "content", "<PERSON><PERSON>", "location", "reload", "Modal", "isDismissible", "TransferMoneyModal", "Stream", "moneyName", "recipient<PERSON><PERSON><PERSON>", "SearchState", "component", "state", "replace", "required", "step", "min", "moneyTransferChanged", "maxlength", "hide", "getTotalNeedMoney", "moneyTransferValue", "parseFloat", "isNaN", "totalNeedMoney", "totalNeedMoneyText", "onsubmit", "userMoney", "moneyTransferValueTotal", "moneyTransferNotesValue", "<PERSON><PERSON>", "moneyTransferData", "moneyTransfer", "moneyTransferNotes", "JSON", "stringify", "save", "TransferMoneyNotification", "icon", "href", "notification", "excerpt", "subject", "attribute", "transferID", "costText", "cost", "Notification", "TransferHistoryListItem", "transferHistory", "session", "fromUserID", "transferNotes", "transferNotesText", "translator", "trans", "transferType", "transferTypeStyle", "transferMoneyText", "forum", "route", "Component", "TransferHistoryList", "moreResults", "params", "loadResults", "hasMoreResults", "disabled", "loadMore", "parseResults", "links", "next", "offset", "TransferHistoryPage", "loadUser", "param", "UserPage", "initializers", "models", "notificationComponents", "routes", "path", "extend", "LinkButton", "SessionDropdown", "routeName", "current", "transferMoneyLabelContainer", "document", "getElementById", "detachTransferMoneyMenu", "vdom", "isMobile<PERSON>iew", "css", "moneyTransferClient1Customization", "task", "setInterval", "dom", "clearInterval", "removeClass", "addClass", "itemNav", "clone", "attr", "prepend", "appNavigation", "userMoneyText", "createElement", "transferMoneyContainer", "innerHTML", "transferMoneyIcon", "append<PERSON><PERSON><PERSON>", "transferMoneyButtonText", "click", "modal", "show", "userAvatarContainer", "avatar<PERSON><PERSON>", "html", "hideNavToggle", "attachTransferMoneyMenu", "NotificationGrid", "label", "UserControls", "allowUseTranferMoney"], "sourceRoot": ""}