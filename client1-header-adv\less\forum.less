#client1HeaderButton2 {
  display:none;
}

#client1HeaderButton3 {
  display:none;
}

.selectTitleContainer{
  text-align: center;
}

.selectTitle {
    width: 100%;
    overflow-y: hidden;
    overflow-x: hidden;
    padding-top: 10px;
    padding-bottom: 5px;
}

.selectTitle .switch-btns {
    width: auto;
    overflow-x: auto;
    display: inline-block;
    background-color: #1b2132;
    border-radius: 25px;
    padding: 4px;
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}

.selectTitle .switch-btns::-webkit-scrollbar { 
    display: none;  /* Safari and Chrome */
}

.selectTitle .switch-btns .btns-container {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

@media @phone{
  .selectTitle .switch-btns button {
      font-size: 14px !important;
      padding: 6px 6px !important;
  }
}

.selectTitle .switch-btns button {
    color: #f3f3f3;
    padding: 10px 15px;
    background: transparent;
    border-radius: 0;
    font-size: 15px;
    position: relative;
    z-index: 1;
    cursor: pointer;
    white-space: nowrap;
}

.u-btn {
    padding: 0;
    margin: 0;
    text-align: center;
    background: #313952;
    border: 0;
    border-radius: 8px;
    outline: none;
    padding: 4px 12px;
    font-size: 12px;
    color: #f3f3f3;
}

.u-btn-text{
  min-width: 53px;
  display: inline-block;
}

.selectTitle .switch-btns i {
    margin-right: 4px;
    color: gold;
}

@media @phone{
  .selected-bg{
    height: 33px !important;
  }
}

.selected-bg{
  width: 106px;
  height: 43px;
  position: absolute;
  left: 0;
  top: 0;
  background: #2b3248;
  border-radius: 21px;
  z-index: 0;
  opacity: 0;
  -webkit-transition: .2s;
  transition: .2s;
}

.zhiboContainer{
  left: 0px;
  width: 100%;
  display:none;
  margin-top: 0px;
  margin-bottom: -10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--button-color);
}

.zhiboSubContainer{
  text-align: center;
  vertical-align: middle;
  display: flex;
  right: 0px;
  height: 46px;
  background: var(--header-bg);
  width: 100%;
  position: fixed;
  bottom: 0px;
  z-index: 999;
  align-items: center;
  justify-content: center;
}

.buttonCustomizationContainer{
  display:none;
  left: 0px;
  width: 100%;
  display:none;
  margin-top: 0px;
  margin-bottom: -10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--button-color);
}

.youxiContainer{
  display:none;
  justify-content: center;
  align-items: center;
  border: 2px solid var(--button-color);
  border-radius: 12px;
  margin-top: 10px;
  margin-bottom: -10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--button-color);
}

.shangchengContainer{
  display:none;
  justify-content: center;
  align-items: center;
  border: 2px solid var(--button-color);
  border-radius: 12px;
  margin-top: 10px;
  margin-bottom: -10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--button-color);
}

.zhiboIframe{
  width: 100%;
  border: 0;
}

.customButtonIframe{
  width: 100%;
  border: 0;
}

.swiper {
 
}

.swiper-wrapper a:hover, a:visited, a:link, a:active{
    text-decoration: none;
}

.tagSwiper{
  padding-top: 6px !important;
}

.TagTextIcon{
  background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAACGFjVEwAAAAcAAAAACxdupMAAAMAUExURQAAACgrSqW20LbJ42B1j0RadJept+z48Iyfr1VqhEthe42hu6i81tDe5OHt6NTi4JWntcnZ473P5bTH3lBlf5KmwNjm66Kzv9Hh6srb6Nnm47rKz5iqt8DR3cbU5Xt9kFNgfVRhfmt/mmd8lsDS5ae6ym6Dm3iMoYufuZ6xyJWnwisuTVhshoSYslluiDAyUJaqxHaKpH2Rq4ictlRWb0ZIY1trhlttiH+TrYWZs6u/2TU4VZmtx1BTbHKFnpOivVxlg1tvinSJoN/s7VxxiVFmgGR5kJKktH6SpYGVp66+x9De3oOWqOn179Tm5cDT13yprHihqMrb3naapXKToc7c4WBahGIOJ5omJLEwI8FOJOt1I7A8JKkxJLVBJIogJWMOJ6pEJYssJnUcJpMyJWsSJqMqJJEjJYgrJnIaJmgRJ4QcJZQjJXAWJmgTJ6M9JYUpJnQYJpomJW8UJp9se28XK3oaJmYRJ2QPJ3wdJpclJWQSKosgJXgXJpUqJa04JJ06JYI/U3gvRXsdLH4aJZklJYsoJawvI2wdNYA7UG0TJo4hJW8YK40nJYorJshWJIkrJngfJrZNJYElJo8wJr1TJWMPJ+NuI9NjJKo0JLpGJK03JNtnI5MkJVJKdqBERbJKRNTCxufn58HQ5LWwxKOvy7bI4s/U3+Tk5ebm58uiorhPRbhoZrSVoqOxzs/Z5ceSkrJRTbfK47jK48zX5cqenr9zbrewwrbC2rbH4LW3zayuxXl7n3FxmKCuy7zM5LhjX5ehwbI5LrNfXY6OrYGBo4qQsoqRs3BtkoyMq6Wxy6u2z+Lh5N7a3dfMzzxMaFx7iqK1uaCor2JyhsnU0iktS0NXcVl2h4Sqro2ssKa5vDE1U2Z1iUhbdERYclJsf5aus1Jjei81UisxTztKZlZyhI2fpnyDkldmfDZDYEphd09kejU8WjI7WUFSavyMNf+yAGc5GP///+bm5v/FKXdJKuRqGfrouv/YH7Ozs5VhSJQ5AMdUD99qLv/EJnlQMuNK8JoAAAEAdFJOUwD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8Kl/Q8AAAAGmZjVEwAAAAAAAAAMAAAADAAAAAAAAAAAABkA+gAACQG1LIAAAAZSURBVEiJ7cGBAAAAAMOg+VPf4ARVAQDwDAkwAAGWQYeBAAAAGmZjVEwAAAABAAAAIAAAABAAAAAIAAAAAABkA+gCAdsAiKMAAAERZmRBVAAAAAIokXWQaVPCMBCGsyxLuBTElsOibamW2gNo6YXl+P//ipiMLeL4fMhk8z67MxsG0MI2dXiXftHlHWpjC4AB9PqD4QN/HI2beDx6mjwPB/2eFDTScco5nzXCjM8RUSetFiaLKX8xlipeGq9vkzsBcS5mGEow+AKxFkzTsleOju+LD3ftSdbup4h9Z2VbpskYCyCkSLx4tJGNuCFPnBGFELBvtlLYxUmyV8I+SeKdFLZSUBMScrDGoeRmQprlRRyV7gGHkeQLD24ZxUWepUxRQUGxaDyqLY7iGlMBFfvhVOXh+eLrpRJK3b+cw7w6sYYMAOzmJ21RZuyWNNgGllZjiTJl95hQY/4J/xeuWLEe/5qWQU0AAAAaZmNUTAAAAAMAAAAgAAAAIAAAAAgAAAAEAGQD6AIBDn7eiAAAAedmZEFUAAAABDiNhZNtW9owFIYpIU2tbGVIRShbqGwFoQVbpaAoloJgBd+m+/9/ZadJSal78fnQ6yS5T3qSPCeXE6KSEM39Tf8FznuDXpAXMmF4ngGGkBYgIR+Gw53l64lv2/asEM6ZwsIMhv7kWgATaYRjLQjTgg1G0mT7/6E/usVLWZZX4R0hd+0VhEt8O/KHvI6eZENCBKkGVglRsQFhBFO21GPAAIBldA+z62ijKJtoDeF9tARgIHaQ+d+JhrGWhHKyA6VBMEuAh8cnjEvVegLMApPSnCTlUSEB5s8vcf1GAhRQXpIYEC5Wxpo8zH++vsVAw6iTtaH0LQHMSQineHx+xYmqcAoLbnQL/NLa6kZ7ennbAiUtUqfFSwHENSg4IyVTwwfAnrpf/EQ+a6V0uaR9KR8U99U9BlSwjg7hXNUUqJIjKFHHFQGUa4ekbjT4csP4+q38DkDoiD0W5hdVY7bhAKVN87ilo++1H1a7w9S2TmC52zo2m5Qmj+XATAf3ueH6uANfJ/PcDjp1Pe+MA2ee554yIH1uB3m4lZq2hb2dHWLLuc7YukBFh+kSXVhjxxWW46Z1IfGKn+IKQnfHtNz205uuPubAWO/eTDO2541jpjdpvmsc3nrNilDzj9b7sHn/DfwGsRNEq0+tzfAAAAAaZmNUTAAAAAUAAAAgAAAAIAAAAAgAAAAOAGQD6AIBR8TzvAAAAehmZEFUAAAABjiNhZN7W9owFMYp4TRY2coYiFD2BMRRBi3YKhdFa7kIVvA2nd//o+w0KSl1F98/+pwkv5OeJO9JpaSYIsVSf9N/gbNuv+unpd5weJYABpjmEykfh4Od5avJ0LKsaWY255plpjgcTq4kMFFGEGpBuRZ8MFIm2/8PhqMbWKqquprdUnprrjBcws1oOBB1dBULEwJMNUCjVAMDwwCnLKXLgT4Cy+AOZ9fBJpvdBGsM74IlAn25gyr+TnUAPQrVaAfGfH8aAfcPjwD5cjUCpv4bYylFSZNMBMyfnsP6jQjIkLSicGC2WBlrej//+fIaAjWjStfGqteSwJyaeIqHpxeIVMZTeHijW+CXbmob/fH5dQvk9UDzchcSCGvIQkLZRA0fAHvafu4T/azn4+W8/qXwNbev7XGgCCVygOcqx0CZHmKJJShKoFA5oFWjJpZrxrfjwjuAkEP+WCAuqsJtIwDG6o2jZoccV763zDaX2fqBy53mUaPOWPRYNs60oScM14M2fu3Ec9vkxHHdUwGcuq5zwoH4uW3iQjM2bRPcnR1Cyzn22DsnOZvrgpy3xrYjLSdM62DipTjFJYbOjmmF7b3rTmksgHGpc+0lbC8apxHfZONd44jWqxel6n+03ofN+2/gN/yhRY5zhBoQAAAAGmZjVEwAAAAHAAAAIAAAACAAAAAIAAAABABkA+gCAQ4ifxsAAAHnZmRBVAAAAAg4jYWTbVvaMBSGKSFNrWxlSEUoW6hsBaEFW6WgKJaCYAXfpvv/f2WnSUmpe/H50OskuU96kjwnlxOikhDN/U3/Bc57g16QFzJheJ4BhpAWICEfhsOd5euJb9v2rBDOmcLCDIb+5FoAE2mEYy0I04INRtJk+/+hP7rFS1mWV+EdIXftFYRLfDvyh7yOnmRDQgSpBlYJUbEBYQRTttRjwACAZXQPs+tooyibaA3hfbQEYCB2kPnfiYaxloRysgOlQTBLgIfHJ4xL1XoCzAKT0pwk5VEhAebPL3H9RgIUUF6SGBAuVsaaPMx/vr7FQMOok7Wh9C0BzEkIp3h8fsWJqnAKC250C/zS2upGe3p52wIlLVKnxUsBxDUoOCMlU8MHwJ66X/xEPmuldLmkfSkfFPfVPQZUsI4O4VzVFKiSIyhRxxUBlGuHpG40+HLD+Pqt/A5A6Ig9FuYXVWO24QClTfO4paPvtR9Wu8PUtk5guds6NpuUJo/lwEwH97nh+rgDXyfz3A46dT3vjANnnueeMiB9bgd5uJWatoW9nR1iy7nO2LpARYfpEl1YY8cVluOmdSHxip/iCkJ3x7Tc9tObrj7mwFjv3kwztueNY6Y3ab5rHN56zYpQ84/W+7B5/w38BrETRKvFe2DfAAAAGmZjVEwAAAAJAAAAIAAAAA8AAAAIAAAAAABkA+gCActTl1cAAAECZmRBVAAAAAoYlXWQ2W7CMBBFPR1ZFpAWShOWhjZrQ5qFEJMNheX//6rGVh1E1fNgeTxnrjQmAKPx5OmZvUxnVDObvs7fjMl4BEAATGrhgjG2HIQlWyGiRU0thOGCvdsb1d7YH5/zBwFxJTJsJdhsjagFx3E9P0gwXH/521iyjb5v7cD3XMchhKSQ0Vy8xHQnB3FHY3HmNIOU3CiksC85PyjhwHm5l0IhBZXAaYCagPK7hKpu2jLvoiMaucTAY9Tll7apK6LooaWlGDypLU7iWtIWevLLuW+yyzWxOiV0VnKNsqY/k4EaALzhJz1R1uSeKi1S19S4oqzIIw5onD/N/4UfKMweRVWXSF0AAAAaZmNUTAAAAAsAAAABAAAAAQAAAAAAAAAAAGQD6AAAxSIZ1gAAAA5mZEFUAAAADAiZY2AAAAACAAHUGARwAAAAGmZjVEwAAAANAAAAIAAAABAAAAAIAAAAAABkA+gCAdvlahYAAADuZmRBVAAAAA4okXWQ61LCMBBG9y23a2xTldqmwXghlEsE7QUtykO7tEUIDGcmP758ZzKbBcSAboQQtxQKj4gkMsAnvuN8Hz/4wihOHtNewL2QoToTMKegE/T4yTz7wosx5jV9G4SAchz5guJnJ0SdYO00KXShZjhX/yy01mNaJlNrAcChpAl6aNoj0QEMwvtqkSmcZwPrC4HoQ3yiOcwQekJZ1c2GwugLZ9HAN9ebpq5K6Gmx4RvNHz+hwRYObNtaSlnkP/K3L5cc63YLRyoePaEU415IOFZwSumc2wWat9ax41jCOfa4B3tRXhf+ABgln5GeYhIdAAAAGmZjVEwAAAAPAAAAIAAAACAAAAAIAAAABABkA+gCAQ6bPD0AAAGdZmRBVAAAABA4jY2Ta3fBQBCG/cuxtrJJJeTCol2UVBFR92oppZf/2pFsV3L09n7JmZlnT+aaySgJUBKZ7/QrMPB9f5H1ALKRGmgOUkCAzyxSAp1EstAMEuHpZMiWxoNbMYxcpArM2HAyVcAEQtKjfeBUyoEuCWHy9f9gGJra6gQ8PtnmkozCZhDn4QMjGvoVwKFPe5gJA18Cs26Vru3axjyGn+3NZktzvWICADCpDU78fgfr6KtJQIiFpSNgWhv0vvT7BWcrgY61ECKDvSEuAhckj949wF5WomEWWYAIqHNzdVE8rFZrg/PvgBw9YB9esZM7qpQGeuO399fxx66VBMbFuQKozIEmAXYc7H8A0A20L/VCGjB1q1iKATgCqlEKAFcm6ZUrvJoGapzzeulKVeEmWx2PG+BalilEw2p6TacFN45S2/O8Mra6IYQc9zWk5EWLd5omI7fdtu3AjS11dwaQ9MrlU8Bx5UYkr91DS5MaY3gUDoNBYmkJ8bDwhE5LG689Y013xuZxsINmcu3/PJyz01ucnd6fx/sz8AmPdUWIe9adLQAAABpmY1RMAAAAEQAAACAAAAAgAAAACAAAAA4AZAPoAgFG6tRjAAABoWZkQVQAAAASOI2Fk+lWwjAQhXnLIUSaVlvoInELi1SklCKIKEoFxeVdnbYxpAeX+6dnki+ns9ypVJQEKInKT/oTGIVhuKgGANVcbQxHJSDGZw5pgklyORjG2vXtbMzurQe/ZVm1XC2Ys/HsVgEzSMiQRsCplAcDksDs+//xOLk2ljvg8cm178kk6cRFHiEwYuC5AjhEdIiZMAglMB+c0JV7mtrZ9bObpmtaG9Y1AMCmLnjF+w2s8q8hASEWjomA7aR4+hJFnreWQN9ZCFHB3hAfgQNSx9NXgFdZiYFZVAFy4Izby4PpdrlcWZz/BNToFvvwhp3cUKUyMJy+f7xNPzddHZg27hRAZQ5UB1g2WA3Y/gaAaWF8aB6Vgch0Gs0CgAxQjfrWM/gyyeC4xU/KQMo5P2ueqyp8vdXFuAEuZJlCtJ1O0PG60POULoMgOMZWt4WQ476AkoLceLtpMnI1uHQ96LlS/T2AlC1XLwGZ5SakbtxA15DKgEkyjkeaaQkJsHBNO9MWtmds4M/ZXXHZx1C3/b+Ls7d6i73V+3d5fwe+AELyRkRjD4DnAAAAGmZjVEwAAAATAAAAIAAAACAAAAAIAAAABABkA+gCAQ8MWMQAAAGdZmRBVAAAABQ4jY2Ta3fBQBCG/cuxtrJJJeTCol2UVBFR92oppZf/2pFsV3L09n7JmZlnT+aaySgJUBKZ7/QrMPB9f5H1ALKRGmgOUkCAzyxSAp1EstAMEuHpZMiWxoNbMYxcpArM2HAyVcAEQtKjfeBUyoEuCWHy9f9gGJra6gQ8PtnmkozCZhDn4QMjGvoVwKFPe5gJA18Cs26Vru3axjyGn+3NZktzvWICADCpDU78fgfr6KtJQIiFpSNgWhv0vvT7BWcrgY61ECKDvSEuAhckj949wF5WomEWWYAIqHNzdVE8rFZrg/PvgBw9YB9esZM7qpQGeuO399fxx66VBMbFuQKozIEmAXYc7H8A0A20L/VCGjB1q1iKATgCqlEKAFcm6ZUrvJoGapzzeulKVeEmWx2PG+BalilEw2p6TacFN45S2/O8Mra6IYQc9zWk5EWLd5omI7fdtu3AjS11dwaQ9MrlU8Bx5UYkr91DS5MaY3gUDoNBYmkJ8bDwhE5LG689Y013xuZxsINmcu3/PJyz01ucnd6fx/sz8AmPdUWIxBjxpQAAABpmY1RMAAAAFQAAACAAAAAPAAAACAAAAAAAZAPoAgHKxPOuAAAA5GZkQVQAAAAWGJV10F9PwjAUh+HzLX87aaRTGKwtzihlzlWj+4MZwp1XflvLNoRCfO5Oz5umKQGIb4UQd/FUBGaxnu8A8gEOQQp1EUBz1AdmcZ89hMF3lmWPy6cxiFhjFgbKX7ti7gNr10lucvWMQv3JjTFLLpO1tUTkIHmFgOEDCUc0Bq9vL6lCkY7Kq4D5XXwgO77hJwiqumk3fDP5hJ6M5n69aZu6okGH1p8YFHymRUdH266RUrb6S+6GZenHabelk9o/PeEF4iFI/FjTuco5t4+M/7Xe3o8VXbKnf7BXy/+DXxm8lJhTojZcAAAAGmZjVEwAAAAXAAAAAQAAAAEAAAAAAAAAAABkA+gAAMS1fS8AAAAOZmRBVAAAABgImWNgAAAAAgAByjezOQAAABpmY1RMAAAAGQAAACAAAAAQAAAACAAAAAAAZAPoAgHay03JAAAA42ZkQVQAAAAaKJF1jutygjAQRndm36OPlZm83BILpPVC1NIiGkBjuflWDRKQYnv+JF++szsBIo8Lvnid4WNgnz0iIArl2/vyd71ab0QkpQzvgsLtfH7H9zxCROWEj/hzWn8lhzRNjxMB8WUqJPyEPZ2gdZYXGJ/HHcnlcDre6yLPtAYARsYmfxCuPHXzhhjAKMS+43v/tzAinjaUVd2ooQ6CKOp/oJq6KqGnpcb1knPprg21MHBra2NM0QlCdEJhY93e4EFFRPljQ25jBVNKxlimlAo9L7RHZmMJczSN6Kfyf+EH6W7I/4xlq8UAAAAaZmNUTAAAABsAAAAgAAAAIAAAAAgAAAAEAGQD6AIBD7Ub4gAAAW9mZEFUAAAAHDiNjZPrcoIwEIWZ2ffgsZjJywUIgooCKoIot9LKRSs+UxNuRkqr508m2S+7m+REEAaFeFAoTOlf4CxJUmTbti7LOh0iOj0/ATndFgOAgZBBh5hOcy58qYosy66O48wVZe7sIKHToroMQIVLui0lhCwQWhAXmEpc9fXzovwWRdEhN89U1SXZiwyxyyJv+5BwBiJhqtGqGUna5MiwNAa26xYQNXca6DMQog1AGEbxNXXo2u2ubkweSOIoDAWMbXCatTvyCeEB2ijGHXCr61r1vb8BD23RhpBpQDfmC3O1XpkjwDAMvQFkpCCVjKTBjC7L7wGsxPI5TCsq1lCiPyanDe3Z4k6xc/d82PMPQRAcOQC6q+7koxPAcEx21Qm46ZDD/zicjk24ver2sQC0HvhEQbeff00AV+v0tZ0GBim/MjDL2X14NrOstoOH5XrTwsP2z6btbZ8wQFEYMLb9y4/zxtd7+Xn/Bn4AcN+s7bRWluoAAAAaZmNUTAAAAB0AAAAgAAAAIAAAAAgAAAAOAGQD6AIBRg821gAAAXVmZEFUAAAAHjiNjZPbkoIwDIaZyXtwsQ/FTF+uQBFQFFBRRMth2ZWDrvhM2yIgIq7733SafE3SNhGEThR3osKY/gSOkiSFjuPosqyzJWLb4wOQsWMRABgIGWyJ2DbruU9lnqbp2XVdU1FMdw0x2+blqQNKXLBjCSFkitCUeMBV4LLNn+XFjyiKLrlsLVWdkY3IEafIs1sdEk5BJFwVmtcrSeoYKZaGwGpxA0zNGwfaCIRoHUBpGJ0Tl9kuV3Vp9YE4+qBUwNgBt7ZdkU9IH2CFYtwAl6qqVH/7GrDQCi0JGQd0w5xa88XcGgCGYeg1ICMFqWQgDSbMLP8P4Clmj26WUbG7FO01e1qymu3eLdbepu/e+rsgCPY9AJqnbuSjA0B3Tf7UMXhJF8P/3B32tTuOQkqbzwLQWuALBc35/m8CrLVG36txoJPyFIG3nNO6JxPbvlVwb7m2aeHe9o9N27Z9zAFF4cCw7d8OztPohU+j93Z4XwO/UkWs0cjDEN0AAAAaZmNUTAAAAB8AAAAgAAAAIAAAAAgAAAAIAGQD6AIByEnQBQAAAW9mZEFUAAAAIDiNjZPrcoIwEIWZ2ffgsZjJywUIgooCKoIot9LKRSs+UxNuRkqr508m2S+7m+REEAaFeFAoTOlf4CxJUmTbti7LOh0iOj0/ATndFgOAgZBBh5hOcy58qYosy66O48wVZe7sIKHToroMQIVLui0lhCwQWhAXmEpc9fXzovwWRdEhN89U1SXZiwyxyyJv+5BwBiJhqtGqGUna5MiwNAa26xYQNXca6DMQog1AGEbxNXXo2u2ubkweSOIoDAWMbXCatTvyCeEB2ijGHXCr61r1vb8BD23RhpBpQDfmC3O1XpkjwDAMvQFkpCCVjKTBjC7L7wGsxPI5TCsq1lCiPyanDe3Z4k6xc/d82PMPQRAcOQC6q+7koxPAcEx21Qm46ZDD/zicjk24ver2sQC0HvhEQbeff00AV+v0tZ0GBim/MjDL2X14NrOstoOH5XrTwsP2z6btbZ8wQFEYMLb9y4/zxtd7+Xn/Bn4AcN+s7azHei0AAAAaZmNUTAAAACEAAAAgAAAADwAAAAgAAAAAAGQD6AIByQ/Y6QAAANZmZEFUAAAAIhiVdY7ZDoIwEEUn6X/44Ec16c+NRKVqlCrgAtaiVTb/yiJlczkv7c09MxlAdPliuZoOOKwfjsc5dxEBUZDt9IMd85lHCBFWCML9YP54iqIo7gmEjPrCkZ1JTSVIeVEJCa/tjs3tdI7fdaKUlABAUZs0a4Q7i+y8RgrQCsHM8vB/Cy3+14Y0ywvR1PO559UXiCLPUqgpsbA9Z4zbb4ElNDzLXGudVILjVEJiYl4+oSNDRNVtUCZm0CellF6EEO5k4ppnbGIKn0hskV/lf+EFNJu2KXgJpzUAAAAaZmNUTAAAACMAAAABAAAAAQAAAAAAAAAAAGQD6AAAx35WaAAAAA5mZEFUAAAAJAiZY2AAAAACAAHoR2riAAAAGmZjVEwAAAAlAAAAIAAAABAAAAAIAAAAAABkA+gCAdm5JagAAADMZmRBVAAAACYokX2PSRKCMBBFuxepHCMHyBZWFHfIzaMyCCIIBIcg3MEAAgroW+XXf91JAFGSnRBiTyk9YM/BHD3SIRFhIfh+8FcIKN0SwvAoovjUbUiS7opzmn0JhFzMjnx+Q0HIJDBWqoqE6VVE+e3e8yiG+UqVjAEAR21SbXbEtCd9z2vkAJMQ1nX99HqyTWGDUbDsppXrWraNbcGAg+1aaNGBEddptNbVXFYmNo4LM7b5vpoFZaINn1ic81JOJCZasIThBFuVv4UXRk93xStYHekAAAAaZmNUTAAAACcAAAAgAAAAIAAAAAgAAAAEAGQD6AIBDMdzgwAAAT1mZEFUAAAAKDiNpZNJloIwEEDJIi/HyAFY9AZWPO/AzaOIEUQGAbWJpM9gBgijsui/Ii+/SKVSZVkGDAzYWuOr4Ni2XRJDKZbORHBFWAUNlVi6o+2d1zZNQ/YdFNZi2Xo7I3iAi7DM76DqLxx4/fluy//yPN/3wjmXCuHc1XnYoIG5PyFV/2iAPROigyLy74yuCjFSxOKTGQHjpCKZPD8qHk/Fo4iUUFclxhYABOr0ChToOgaoUIJIFAAjRPFFCj+JFC7xayEcxOEBOCF0EgJCx09CGIKvQpfDf4Qw3MhhPUlKz/01E3XNa5pNBKhKPSrUrWscKWBcVjWk6X0o9e9Nx+tS68eCkA2PlXbx49eEkDLGXkdFtiqs0Auy5chym/DWdSZNO2No2r7t62Fz3vabg7MYvWQxepvD+1l4A2alDWO6VhYrAAAAGmZjVEwAAAApAAAAIAAAACAAAAAIAAAADgBkA+gCAUXEHZEAAAFDZmRBVAAAACo4jaWTS5KDIBBAZUFxDA7AYja6snIHb05iDNE4fqImGYnMGYaPokZjFvNWUryWpul2HAsGFuyssSm4hJCKWiq5dGeCJ8NqaKnl0pts7/yubVu672GwkcvO31nBB0LFBT1M/0UAfzjf68RvURT7QTgXSmmE8EweBLSwCGZk+h8tIC9CfNDEwY2zVSFBmkR+citgnNY0V+fH5f2huZexFpq6wtgBgEKTXolCU8cQlVqAkAJghTi5KOErVcIleS6Egzw8BCeETlJA6PhGAFEEtgWTw3+EKCKbwpskGTsP10z1Nb+zfCZAXepJoa594ygB46puIMtuY6l/ribelNo8FoR8fKysj5++JoSMc/48avJVYYVBUC1Hl9tUdJ47b9o5Y9Oqtheyz5tx87XtPw6OGb10HL10MXofh/e98AeikwsbLp9MrwAAABpmY1RMAAAAKwAAACAAAAAgAAAACAAAAAQAZAPoAgEMIpE2AAABPWZkQVQAAAAsOI2lk0mWgjAQQMkiL8fIAVj0BlY878DNo4gRRAYBtYmkz2AGCKOy6L8iL79IpVJlWQYMDNha46vg2LZdEkMpls5EcEVYBQ2VWLqj7Z3XNk1D9h0U1mLZejsjeICLsMzvoOovHHj9+W7L//I83/fCOZcK4dzVediggbk/IVX/aIA9E6KDIvLvjK4KMVLE4pMZAeOkIpk8PyoeT8WjiJRQVyXGFgAE6vQKFOg6BqhQgkgUACNE8UUKP4kULvFrIRzE4QE4IXQSAkLHT0IYgq9Cl8N/hDDcyGE9SUrP/TUTdc1rmk0EqEo9KtStaxwpYFxWNaTpfSj1703H61Lrx4KQDY+VdvHj14SQMsZeR0W2KqzQC7LlyHKb8NZ1Jk07Y2javu3rYXPe9puDsxi9ZDF6m8P7WXgDZqUNYyqI7YIAAAAaZmNUTAAAAC0AAAAgAAAADwAAAAgAAAAAAGQD6AIByeo6XAAAAM1mZEFUAAAALhiVdY9LEoIwEERnFqkcIwfIwg2sKO6Qm6OAKCIIBD9BcgcTVD6Cb5Wued1VAcSAbIUQO0ppiBjHiKF5RsQSIMJc2FO6XxGS5CCO6ckubDK7cM6LmUDIxWyUVrCEtCJkEBirZUOS4iqO5e3e86je/UbWjAEAR2VSazZS2pN/+go5wCAkbds+o55iVVjhKzhup4PludGd68AbD/VS0OjBF9/rlFLNpG2i9nwYcc335ShIE12Y4nDOs2CgNtGBXxgOsMXxv/ACuTRvZHFKYYMAAAAaZmNUTAAAAC8AAAABAAAAAQAAAAAAAAAAAGQD6AAAx5u03QAAAA5mZEFUAAAAMAiZY2AAAAACAAH2aN2rAAAAGmZjVEwAAAAxAAAAHAAAABIAAAAKAAAAAABkA+gAAc9ENdwAAABcZmRBVAAAADIokb3Puw4AIQhE0fn/nx1RXCw3PmNhcKu9FeHQADKMRGJMSSQEtlThIsBVziJzbnTBnfu6BnzB3jPCKRdV60PkP2hWiq7qwfaKg2Z9tbfYRR674gu1OTUADJcEKwAAABpmY1RMAAAAMwAAACwAAAAmAAAAAgAAAAAAZAPoAgHZOuxeAAAA3WZkQVQAAAA0OI3N1OsOgyAMBWDe/2VLlYsKnfNY2TTRLahLdv4Q5SOh0GBidM7OcS7GrvO+acyUvme21swhwmzXmWqMH/o5DM0UZmNSyhkjkc5jwR2YWSQlZqLzGGgP5yxyHy5HBy6CAn+DcSnXMFHBGJW3LS7kGsZAFIK1j3kba6zIuRBgls8KrCkFDoPIOLZtadGyjU0qsbVoqi1+F3ge42g0zFqg0utYj1DjvbbQIa3Ea64MOaCVWBOWfGEnsD5nm8b5Y4wWda9gwYejq8Bo0PKwlxzyKky7OY2fncSk/pb+Rx4AAAAaZmNUTAAAADUAAAAsAAAAMAAAAAIAAAAAC7gD6AEBX4fkwQAAAPhmZEFUAAAANkiJ1dXdEoIgEAXgff+XXUhE/CHztDEoqdMqN52LdOpzWmDbiN5xzpin67qwxH0id0TMeF1FhY0BLoN3me9hIn8SKqPCxrhigYdFXMbtErvkLu46+cKE2/axpGn6HlwwMz4NQY9TGSEMAyiKGMdpsjaXgQfq4BjH0drcRnoMtIenKcZ6OG8dythZYCWcD+UqZi4bCcddNpIe48LsfR4F39h7XIm0eNv8wxDjPG9b9PCX8iM2Bk1VH2NrJNamBTLXwLKFEoyCdL9LlXjNhckwr4Elh4P2Nk7D9j8wWnT9x3ZyJCqMBk2DPeWQqzDv5jJ+AXwh6JVhZmXFAAAAAElFTkSuQmCC);
  content: "";
  display: inline-block;
  vertical-align: -3px;
  width: 24px;
  height: 24px;
  line-height: 26px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top;
  margin-right: 6px;
}

.TagTextContainer{
  font-size: 14px;
  padding: 0px 0px 10px 0px;
  font-weight: bold;
}



.swiperTagContainer{
  margin-top: 10px;
  margin-bottom: -10px;
  padding-bottom: 50px;
}

.swiperAdContainer{
  margin-top: 10px;
  margin-bottom: -10px;
}

.swiper-slide {
  height: 280px;
  width: 600px;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  border-radius:12px;
}

.swiper-slide-tag {
  -webkit-transition: all .23s linear;
  transition: all .23s linear;
}

@media @desktop-hd{
  .swiper-slide-tag:hover {
    /* opacity: .6; */
    -webkit-transform: translateY(-6px);
    transform: translateY(-6px);
  }
}

.TagTextOuterContainer{

  background:#141623;
  padding: 10px;
  border-radius: 1rem;

}

.swiper-slide-tag-inner {
  border-radius: 12px;
  width:146px;
  height:196px;
  text-align: center;
  padding: 15px;
}

.swiper-slide-tag-inner-mobile {
  border-radius: 12px;
  width: 200px;
    height: 240px;
  text-align: center;
  padding: 10px;
}

.swiper-pagination{
  transform: translateY(100%) !important;
  line-height: 0;
}

.buttonRegister{
  width: 74px;
  margin-right: 0;
  font-size: 14px;
  -webkit-animation: shockwaveJump 1s ease-out infinite;
  animation: shockwaveJump 1s ease-out infinite;
  height: 30px;
  color: #2b3248;
  background: #FACA46;
  border-radius: 32px;
  text-align: center;
  font-size: 16px;
  border: none;
  padding: 0;
  position: relative;
  outline: none;
  margin-top: -5px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.buttonRegister:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    border-radius: 16px;
    -webkit-animation: shockwave 1s 0.5s ease-out infinite;
    animation: shockwave 1s 0.5s ease-out infinite;
}

.buttonRegister:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    border-radius: 16px;
    -webkit-animation: shockwave 1s 0.65s ease-out infinite;
    animation: shockwave 1s 0.65s ease-out infinite;
}

@-webkit-keyframes shockwaveJump {
  0% {
    -webkit-transform: scale(0.4);
            transform: scale(0.4);
  }
  40% {
    -webkit-transform: scale(1.08);
            transform: scale(1.08);
  }
  50% {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
  }
  55% {
    -webkit-transform: scale(1.02);
            transform: scale(1.02);
  }
  60% {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@keyframes shockwaveJump {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  40% {
    -webkit-transform: scale(1.08);
            transform: scale(1.08);
  }
  50% {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
  }
  55% {
    -webkit-transform: scale(1.02);
            transform: scale(1.02);
  }
  60% {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@-webkit-keyframes shockwave {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 0 2px rgba(255, 255, 255, 0.15), inset 0 0 2px rgba(255, 255, 255, 0.15);
  }
  95% {
    box-shadow: 0 0 4px #ffffff, inset 0 0 4px #ffffff;
  }
  100% {
    -webkit-transform: scale(1.25);
            transform: scale(1.25);
  }
}
@keyframes shockwave {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 0 2px rgba(250, 202, 70, 0.65), inset 0 0 1px rgba(250, 202, 70, 0.95);
  }
  95% {
    box-shadow: 0 0 16px rgba(250, 202, 70, 0.15), inset 0 0 16px rgba(250, 202, 70, 0.15);
  }
  100% {
    -webkit-transform: scale(1.3);
            transform: scale(1.3);
  }
}

.swiper-button-prev {
    left: calc(~"50% - 280px") !important;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABEZVhJZk1NACoAAAAIAAGHaQAEAAAAAQAAABoAAAAAAAOgAQADAAAAAQABAACgAgAEAAAAAQAAADygAwAEAAAAAQAAADwAAAAAi9/pXAAAA0BJREFUaEPtmktrU0EUgJPYKkIea5du/QfqwqVuRCQgFopi8dGKVMVSRGnBikKlQlIfrQ+UqlhBBIsrf4BFf4U793mASmvid3pPIHrvTcA7MzfIfHA5M6eL8uWcmTuZNuPxeDwej8fj8ZghqzE1Go3Gnna7PclwF896oVCoZrPZH1s/tECqwsgeQ/Ylz3ZNZZD9hPQB4i9NGSWn0Tm1Wm0U0dfdsgLz/XwQh3VqnFSE6/X6GBVcQW6bpv6A/G4dGse5MNWbIDxFqtfvXtdoHKfCtPHFVqv1ENnYvSOXyy2WSqUvOjWOM2HaeJpQCWaxLOXz+Us6toITYdp4lqrO6zQSKluhsudZ221NWcG6MJW9RRvf0GkkSM7zKrqsU6tYFUZ2gcpe02kcc8Vi8aqOrWPl4CGbErKLDC8EmWio7HVkb+vUCcaFRZY1u0w8q6lIkJ1C9q5OnWFUGMkcss+IJzUVQjYlnknW7H1NOcWYMJJDtPELhiNBJozIEs5R2SdBxj1GhJEdprKrxLKmQiDbIowhuxJk0iGxMJI7kH1LjD3wI7vJe/YEh4pVTaVGImGRpY3fMzwUZMIgu8Ezwpp9p6lUSfQeprJyoOgl+5NQHhRZIVGFqe43qiw3FSGQ/U4bH6WNP2pqIHD25WFQSCosr6FIqPxOztBrdIG124t/wcSmtcbwYJAJ819tWrIp8V49QvygqRB8KMM8b5rNZuyBxCWJ17BIU70yMbaCCA/R3q/ohtgjpyuMbFrIbiB9nGHswQJp+V3PkT4TZNLBiLCA9CbtPUqMPToiLXvGI97fPb822sSYsIBsi0qfIj7WVAiRpr3vUekrmnKKUWEB2TbS4wx7fv1DfAHpfrchxkn85aEXCMkVT79K3iyVSrM6to7xCnfDmp6i4v2ucGb4YHreaJrEaoU7sEnNsG7ndBoJ5+4qS8HqnbTgRFigitO0d79KLtMVVu+mrbZ0N4jcIfSr4DgfTFXHVnBW4Q609wSVfiCvJ01FsZeN7LOOjeKswh1Yp0uE0/LODjJh+Nk+HRrHubBAe29d5SIW91f+rxqN47ylu6G9nf/LQ6rCAtJ//1NLBVm5C/N4PB6Px+PxeAaXTOY3/ptWEo03PGQAAAAASUVORK5CYII=);
    width: 16px !important;
    height: 16px !important;
    transform: translate(-50%,-50%);
    background-size: 100% 100%;
}

.swiper-button-next {
    right: calc(~"50% - 295px") !important;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABEZVhJZk1NACoAAAAIAAGHaQAEAAAAAQAAABoAAAAAAAOgAQADAAAAAQABAACgAgAEAAAAAQAAADygAwAEAAAAAQAAADwAAAAAi9/pXAAAA0BJREFUaEPtmktrU0EUgJPYKkIea5du/QfqwqVuRCQgFopi8dGKVMVSRGnBikKlQlIfrQ+UqlhBBIsrf4BFf4U793mASmvid3pPIHrvTcA7MzfIfHA5M6eL8uWcmTuZNuPxeDwej8fj8ZghqzE1Go3Gnna7PclwF896oVCoZrPZH1s/tECqwsgeQ/Ylz3ZNZZD9hPQB4i9NGSWn0Tm1Wm0U0dfdsgLz/XwQh3VqnFSE6/X6GBVcQW6bpv6A/G4dGse5MNWbIDxFqtfvXtdoHKfCtPHFVqv1ENnYvSOXyy2WSqUvOjWOM2HaeJpQCWaxLOXz+Us6toITYdp4lqrO6zQSKluhsudZ221NWcG6MJW9RRvf0GkkSM7zKrqsU6tYFUZ2gcpe02kcc8Vi8aqOrWPl4CGbErKLDC8EmWio7HVkb+vUCcaFRZY1u0w8q6lIkJ1C9q5OnWFUGMkcss+IJzUVQjYlnknW7H1NOcWYMJJDtPELhiNBJozIEs5R2SdBxj1GhJEdprKrxLKmQiDbIowhuxJk0iGxMJI7kH1LjD3wI7vJe/YEh4pVTaVGImGRpY3fMzwUZMIgu8Ezwpp9p6lUSfQeprJyoOgl+5NQHhRZIVGFqe43qiw3FSGQ/U4bH6WNP2pqIHD25WFQSCosr6FIqPxOztBrdIG124t/wcSmtcbwYJAJ819tWrIp8V49QvygqRB8KMM8b5rNZuyBxCWJ17BIU70yMbaCCA/R3q/ohtgjpyuMbFrIbiB9nGHswQJp+V3PkT4TZNLBiLCA9CbtPUqMPToiLXvGI97fPb822sSYsIBsi0qfIj7WVAiRpr3vUekrmnKKUWEB2TbS4wx7fv1DfAHpfrchxkn85aEXCMkVT79K3iyVSrM6to7xCnfDmp6i4v2ucGb4YHreaJrEaoU7sEnNsG7ndBoJ5+4qS8HqnbTgRFigitO0d79KLtMVVu+mrbZ0N4jcIfSr4DgfTFXHVnBW4Q609wSVfiCvJ01FsZeN7LOOjeKswh1Yp0uE0/LODjJh+Nk+HRrHubBAe29d5SIW91f+rxqN47ylu6G9nf/LQ6rCAtJ//1NLBVm5C/N4PB6Px+PxeAaXTOY3/ptWEo03PGQAAAAASUVORK5CYII=);
    transform: translate(-50%,-50%) rotateY(180deg);
    width: 16px !important;
    height: 16px !important;
    background-size: 100% 100%;
}

@media @phone {
  .swiper {
    height: 100%;
  }

  .swiperContainer{
    /* width: 700px; */
    /* margin-left: -178px; */
  }

  .swiper-slide {
    /* width: calc(~"100% - 32px") !important; */
  }

  .swiper-slide img {
    width: 100%;
    height: 100%;
    border-radius:12px;
  }
}

.swiper-slide-shadow-right{
  border-radius:12px;
}

.swiper-slide-shadow-left{
  border-radius:12px;
}

/**
 * Swiper 8.4.5
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2022 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: November 21, 2022
 */

@themeColor: #007aff;

@font-face {
  font-family: 'swiper-icons';
  src: url('data:application/font-woff;charset=utf-8;base64, 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');
  font-weight: 400;
  font-style: normal;
}

:root {
  --swiper-theme-color: @themeColor;
}
.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
}
.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}

.swiper-pointer-events {
  touch-action: pan-y;
  &.swiper-vertical {
    touch-action: pan-x;
  }
}
.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}
/* Auto Height */
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}
.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}
.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  backface-visibility: hidden;
}
/* 3D Effects */
.swiper-3d {
  &,
  &.swiper-css-mode .swiper-wrapper {
    perspective: 1200px;
  }
  .swiper-wrapper,
  .swiper-slide,
  .swiper-slide-shadow,
  .swiper-slide-shadow-left,
  .swiper-slide-shadow-right,
  .swiper-slide-shadow-top,
  .swiper-slide-shadow-bottom,
  .swiper-cube-shadow {
    transform-style: preserve-3d;
  }
  .swiper-slide-shadow,
  .swiper-slide-shadow-left,
  .swiper-slide-shadow-right,
  .swiper-slide-shadow-top,
  .swiper-slide-shadow-bottom {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
  }
  .swiper-slide-shadow {
    background: rgba(0, 0, 0, 0.15);
  }
  .swiper-slide-shadow-left {
    background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
  .swiper-slide-shadow-right {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
  .swiper-slide-shadow-top {
    background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
  .swiper-slide-shadow-bottom {
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
}

/* CSS Mode */
.swiper-css-mode {
  > .swiper-wrapper {
    overflow: auto;
    scrollbar-width: none; /* For Firefox */
    -ms-overflow-style: none; /* For Internet Explorer and Edge */
    &::-webkit-scrollbar {
      display: none;
    }
  }
  > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: start start;
  }
}
.swiper-horizontal.swiper-css-mode {
  > .swiper-wrapper {
    scroll-snap-type: x mandatory;
  }
}
.swiper-vertical.swiper-css-mode {
  > .swiper-wrapper {
    scroll-snap-type: y mandatory;
  }
}
.swiper-centered {
  > .swiper-wrapper::before {
    content: '';
    flex-shrink: 0;
    order: 9999;
  }
  &.swiper-horizontal {
    > .swiper-wrapper > .swiper-slide:first-child {
      margin-inline-start: var(--swiper-centered-offset-before);
    }
    > .swiper-wrapper::before {
      height: 100%;
      min-height: 1px;
      width: var(--swiper-centered-offset-after);
    }
  }
  &.swiper-vertical {
    > .swiper-wrapper > .swiper-slide:first-child {
      margin-block-start: var(--swiper-centered-offset-before);
    }
    > .swiper-wrapper::before {
      width: 100%;
      min-width: 1px;
      height: var(--swiper-centered-offset-after);
    }
  }

  > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: center center;
  }
}

@themeColor: #007aff;

:root {
  /*
  --swiper-pagination-color: var(--swiper-theme-color);
  --swiper-pagination-bullet-size: 8px;
  --swiper-pagination-bullet-width: 8px;
  --swiper-pagination-bullet-height: 8px;
  --swiper-pagination-bullet-inactive-color: #000;
  --swiper-pagination-bullet-inactive-opacity: 0.2;
  --swiper-pagination-bullet-opacity: 1;
  --swiper-pagination-bullet-horizontal-gap: 4px;
  --swiper-pagination-bullet-vertical-gap: 6px;
  */
}
.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
  &.swiper-pagination-hidden {
    opacity: 0;
  }
  .swiper-pagination-disabled > &,
  &.swiper-pagination-disabled {
    display: none !important;
  }
}
/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: 10px;
  left: 0;
  width: 100%;
}
/* Bullets */
.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;
  .swiper-pagination-bullet {
    transform: scale(0.33);
    position: relative;
  }
  .swiper-pagination-bullet-active {
    transform: scale(1);
  }
  .swiper-pagination-bullet-active-main {
    transform: scale(1);
  }
  .swiper-pagination-bullet-active-prev {
    transform: scale(0.66);
  }
  .swiper-pagination-bullet-active-prev-prev {
    transform: scale(0.33);
  }
  .swiper-pagination-bullet-active-next {
    transform: scale(0.66);
  }
  .swiper-pagination-bullet-active-next-next {
    transform: scale(0.33);
  }
}
.swiper-pagination-bullet {
  display: inline-block;
  border-radius: 50%;
  height: 5px;
  width: 5px;
  opacity: .5;
  background: #f3f3f3;
  button& {
    border: none;
    margin: 0;
    padding: 0;
    box-shadow: none;
    appearance: none;
  }
  .swiper-pagination-clickable & {
    cursor: pointer;
  }

  &:only-child {
    display: none !important;
  }
}
.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  width: 22px;
  border-radius: 2.5px;
  opacity: 1;
}

.swiper-vertical > .swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  right: 10px;
  top: 50%;
  transform: translate3d(0px, -50%, 0);
  .swiper-pagination-bullet {
    margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
    display: block;
  }
  &.swiper-pagination-bullets-dynamic {
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    .swiper-pagination-bullet {
      display: inline-block;
      transition: 200ms transform, 200ms top;
    }
  }
}
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-horizontal.swiper-pagination-bullets {
  .swiper-pagination-bullet {
    margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
  }
  &.swiper-pagination-bullets-dynamic {
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    .swiper-pagination-bullet {
      transition: 200ms transform, 200ms left;
    }
  }
}
.swiper-horizontal.swiper-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform, 200ms right;
}
/* Progress */
.swiper-pagination-progressbar {
  background: rgba(0, 0, 0, 0.25);
  position: absolute;
  .swiper-pagination-progressbar-fill {
    background: var(--swiper-pagination-color, var(--swiper-theme-color));
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    transform: scale(0);
    transform-origin: left top;
  }
  .swiper-rtl & .swiper-pagination-progressbar-fill {
    transform-origin: right top;
  }
  .swiper-horizontal > &,
  &.swiper-pagination-horizontal,
  .swiper-vertical > &.swiper-pagination-progressbar-opposite,
  &.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
    width: 100%;
    height: 4px;
    left: 0;
    top: 0;
  }
  .swiper-vertical > &,
  &.swiper-pagination-vertical,
  .swiper-horizontal > &.swiper-pagination-progressbar-opposite,
  &.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
    width: 4px;
    height: 100%;
    left: 0;
    top: 0;
  }
}
.swiper-pagination-lock {
  display: none;
}
:root {
  --swiper-navigation-size: 44px;
  --swiper-navigation-color: var(--swiper-theme-color);
}
.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: 50%;
  width: calc(~"var(--swiper-navigation-size) / 44 * 27");
  height: var(--swiper-navigation-size);
  margin-top: calc(~"0px - (var(--swiper-navigation-size) / 2)");
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
  &.swiper-button-disabled {
    opacity: 0.35;
    cursor: auto;
    pointer-events: none;
  }
  &.swiper-button-hidden {
    opacity: 0;
    cursor: auto;
    pointer-events: none;
  }
  .swiper-navigation-disabled & {
    display: none !important;
  }
  &:after {
    font-family: swiper-icons;
    font-size: var(--swiper-navigation-size);
    text-transform: none !important;
    letter-spacing: 0;
    font-variant: initial;
    line-height: 1;
  }
}
.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  &:after {
    /* content: 'prev'; */
  }
  left: 10px;
  right: auto;
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  &:after {
    /* content: 'next'; */
  }
  right: 10px;
  left: auto;
}
.swiper-button-lock {
  display: none;
}
