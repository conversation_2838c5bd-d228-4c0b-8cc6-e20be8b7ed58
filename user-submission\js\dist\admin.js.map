{"version": 3, "file": "admin.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFf,EAAyBM,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,M,wBCLxC,SAASC,EAAgBb,EAAGc,GAMzC,OALAD,EAAkBZ,OAAOc,gBAAkB,SAAyBf,EAAGc,GAErE,OADAd,EAAEgB,UAAYF,EACPd,GAGFa,EAAgBb,EAAGc,GCLb,SAASG,EAAeC,EAAUC,GAC/CD,EAASX,UAAYN,OAAOmB,OAAOD,EAAWZ,WAC9CW,EAASX,UAAUc,YAAcH,EACjCH,EAAeG,EAAUC,G,OCJUG,OAAOC,KAAKC,OAAe,OCAhE,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,+B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,qB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAkB,U,aCAnE,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,oB,ICGnCC,EAAAA,SAAAA,G,oFAInBC,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbC,KAAKC,SAAWD,KAAKE,MAAMD,SAC3BD,KAAKG,SAAU,G,EAGjBC,UAAA,WACE,MAAO,gB,EAGTC,MAAA,WACE,OAAOC,IAAIC,WAAWC,MAAM,kD,EAG9BC,QAAA,WAAU,WAER,OACE,SAAKL,UAAU,cACb,SAAKA,UAAU,aAAaM,MAAM,uBAC/BC,IAAAA,UACC,CACED,MAAO,kBACPN,UAAW,yBACXQ,SAAUZ,KAAKG,QACfU,QAAS,SAACC,GACR,EAAKC,cAAcD,EAAE,KAGzBR,IAAIC,WAAWC,MAAM,qCAVzB,IAYGG,IAAAA,UACC,CACED,MAAO,kBACPN,UAAW,wBACXQ,SAAUZ,KAAKG,QACfU,QAAS,SAACC,GACR,EAAKC,cAAcD,EAAE,KAGzBR,IAAIC,WAAWC,MAAM,sCArBzB,IAuBGG,IAAAA,UACC,CACED,MAAO,kBACPN,UAAW,SACXQ,SAAUZ,KAAKG,QACfU,QAAS,WACP,EAAKG,SAGTV,IAAIC,WAAWC,MAAM,wC,EAO/BO,cAAA,SAAcD,EAAE9B,GAAO,WACrB8B,EAAEG,iBAEFjB,KAAKG,SAAU,EACfH,KAAKC,SAASiB,KAAK,CACjBC,aAAanC,IAEdoC,MACC,kBAAM,EAAKJ,UACX,SAACK,GACC,EAAKlB,SAAU,M,EAvEFN,C,MAAkCyB,IAAlCzB,EACZ0B,+BAAgC,EADpB1B,EAEZ2B,6BAA8B,ECLvC,MAAM,EAA+B9B,OAAOC,KAAKC,OAAO,oB,aCKnC6B,EAAAA,SAAAA,G,oFAEnBC,KAAA,WAAO,WACEzB,EAAYD,KAAKE,MAAjBD,SAGD0B,GAFYrB,IAAIsB,MAAMC,UAAU,6BAEvB5B,EAAS0B,UAClBG,EAAW7B,EAAS6B,WACpBC,EAAkB9B,EAAS+B,mBAC3BC,EAAchC,EAASiC,eACvBC,EAAWlC,EAASkC,WAEpBhB,GADalB,EAASmC,aACPnC,EAASoC,iBACxBC,EAAmBhC,IAAIC,WAAWC,MAAqB,IAAfW,EAAiB,mDAAmD,qDAC5GoB,EAAatC,EAASuC,cACtBC,EAAaxC,EAASyC,cAI5B,OACE,SAAKtC,UAAU,+BACb,SAAKM,MAAM,eACR+B,GACC,SAAK/B,MAAM,2BACRC,IAAAA,UAAiB,CAChBD,MAAO,oBACPE,UAAU,EACVR,UAAW,UAEbE,IAAIC,WAAWC,MAAM,gDAIvBiC,GACA,SAAK/B,MAAM,2BACRC,IAAAA,UAAiB,CAChBD,MAAO,oBACPN,UAAW,yBACXS,QAAS,SAACC,GACR,EAAK6B,WAAW1C,KAGpBK,IAAIC,WAAWC,MAAM,8CAK3B,aACE,WAAIF,IAAIC,WAAWC,MAAM,2CAAzB,MACCoC,GAAAA,CAAST,GAFZ,MAGE,WAAI7B,IAAIC,WAAWC,MAAM,yCAAzB,MACCmB,EAJH,MAKE,WAAIrB,IAAIC,WAAWC,MAAM,6CAAzB,MACC+B,GAEH,aACE,WAAIjC,IAAIC,WAAWC,MAAM,2CAAzB,MACCsB,EAFH,MAGE,WAAIxB,IAAIC,WAAWC,MAAM,kDAAzB,MACCuB,EAJH,MAKE,WAAIzB,IAAIC,WAAWC,MAAM,8CAAzB,MACCyB,GAEFQ,GACC,aACE,WAAInC,IAAIC,WAAWC,MAAM,+CAAzB,MACC8B,EAFH,MAGE,WAAIhC,IAAIC,WAAWC,MAAM,2CAAzB,MACCiC,K,EAOXE,WAAA,SAAW1C,GACTK,IAAIuC,MAAMC,KAAKjD,EAA2B,CAACI,SAAAA,K,EA5E1BwB,CAA+BsB,KCA/BC,EAAAA,SAAAA,G,oFACnBlD,OAAA,SAAOI,GACL,YAAMJ,OAAN,UAAaI,GACbF,KAAKG,SAAU,EACfH,KAAKiD,aAAc,EACnBjD,KAAKkD,mBAAqB,GAC1BlD,KAAKmD,e,EAGP1C,QAAA,WAAU,IACJN,EADI,OAOR,OAJGH,KAAKG,UACNA,EAAUiD,IAAAA,UAA2B,CAAEC,KAAM,WAI7C,SAAKjD,UAAU,2CACb,SAAKA,UAAU,aAEb,QAAIM,MAAM,sCACPV,KAAKkD,mBAAmBI,KAAI,SAACrD,GAC5B,OACE,QAAIsD,OAAQtD,EAASuD,KAAM9C,MAAM,8CAC9Be,EAAuBgC,UAAU,CAAExD,SAAAA,UAM1CD,KAAKG,SAA4C,IAAjCH,KAAKkD,mBAAmBQ,QACxC,aACE,SAAKhD,MAAM,yFAAyFJ,IAAIC,WAAWC,MAAM,4CAI3HL,GAAWH,KAAK2D,kBAChB,SAAKjD,MAAM,kCACT,EAAC,IAAD,CAAQN,UAAW,yBAA0BQ,SAAUZ,KAAKG,QAASA,QAASH,KAAKG,QAASU,QAAS,kBAAM,EAAK+C,aAC7GtD,IAAIC,WAAWC,MAAM,8CAK3BL,GAAW,SAAKC,UAAU,2BAA2BD,M,EAO9DwD,eAAA,WACE,OAAO3D,KAAKiD,a,EAGdW,SAAA,WACE5D,KAAKG,SAAU,EACfH,KAAKmD,YAAYnD,KAAKkD,mBAAmBQ,S,EAG3CG,aAAA,SAAaC,GAOX,OANA9D,KAAKiD,cAAgBa,EAAQC,QAAQC,SAAWF,EAAQC,QAAQC,MAAMC,KACtE,GAAGC,KAAKC,MAAMnE,KAAKkD,mBAAoBY,GAEvC9D,KAAKG,SAAU,EACfiE,EAAEC,SAEKP,G,EAGTX,YAAA,SAAYmB,GACV,YADsB,IAAZA,IAAAA,EAAS,GACZhE,IAAIiE,MACRC,KAAK,qBAAsB,CAC1BC,KAAM,CACJH,OAAAA,KAHC,OAME,eACNlD,KAAKpB,KAAK6D,aAAaa,KAAK1E,Q,EA9EdgD,CAAgC2B,KCLrD,MAAM,EAA+BjF,OAAOC,KAAKC,OAAc,M,aCE1CgF,EAAAA,SAAAA,G,kEAAAA,CAAuBC,KAC5CxG,OAAOyG,OAAOF,EAAejG,UAAW,CACtC6E,GAAIqB,IAAAA,UAAgB,MACpBlD,OAAQkD,IAAAA,UAAgB,UACxB/C,SAAU+C,IAAAA,UAAgB,YAC1B7C,iBAAkB6C,IAAAA,UAAgB,oBAClC3C,aAAc2C,IAAAA,UAAgB,gBAC9BE,mBAAoBF,IAAAA,UAAgB,sBACpCG,eAAgBH,IAAAA,UAAgB,kBAChCxC,cAAewC,IAAAA,UAAgB,iBAC/BrC,YAAaqC,IAAAA,UAAgB,eAC7BnC,YAAamC,IAAAA,UAAgB,eAC7B1C,SAAU0C,IAAAA,OAAa,YACvBzC,WAAYyC,IAAAA,OAAa,gBCX3BvE,IAAI2E,aAAaC,IAAI,yBAAyB,WAC5C5E,IAAIiE,MAAMY,OAAOjC,mBAAqB0B,EACtCtE,IAAI8E,cAAJ,IACO,0BAA0BC,aAAaC,O", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/ExtensionPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LoadingIndicator']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Component']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Modal']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/components/UserSubmissionReviewModal.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['helpers/username']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/components/UserSubmissionListItem.js", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/components/SettingsPage.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Model']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/UserSubmission.js", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/ExtensionPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Component'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Modal'];", "import Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class UserSubmissionReviewModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.itemData = this.attrs.itemData;\n    this.loading = false;\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-user-submission.admin.submission-review');\n  }\n\n  content() {\n    //\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form-group\" style=\"text-align: center;\">\n          {Button.component(\n            {\n              style: 'min-width:66px;',\n              className: 'Button Button--primary',\n              disabled: this.loading,\n              onclick: (e) => {\n                this.reviewConfirm(e,1);\n              }\n            },\n            app.translator.trans('wusong8899-user-submission.lib.accept')\n          )}&nbsp;\n          {Button.component(\n            {\n              style: 'min-width:66px;',\n              className: 'But<PERSON> Button--danger',\n              disabled: this.loading,\n              onclick: (e) => {\n                this.reviewConfirm(e,0);\n              }\n            },\n            app.translator.trans('wusong8899-user-submission.lib.decline')\n          )}&nbsp;\n          {Button.component(\n            {\n              style: 'min-width:66px;',\n              className: 'Button',\n              disabled: this.loading,\n              onclick: () => {\n                this.hide();\n              }\n            },\n            app.translator.trans('wusong8899-user-submission.lib.cancel')\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  reviewConfirm(e,value) {\n    e.preventDefault();\n\n    this.loading = true;\n    this.itemData.save({\n      reviewResult:value,\n    })\n    .then(\n      () => this.hide(),\n      (response) => {\n        this.loading = false;\n      }\n    );\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['helpers/username'];", "import Component from \"flarum/Component\";\nimport Button from 'flarum/components/Button';\nimport UserSubmissionReviewModal from './UserSubmissionReviewModal';\nimport username from \"flarum/helpers/username\";\n\nexport default class UserSubmissionListItem extends Component {\n\n  view() {\n    const {itemData} = this.attrs;\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n\n    const amount = itemData.amount();\n    const platform = itemData.platform();\n    const platformAccount = itemData.platform_account();\n    const userAccount = itemData.user_account();\n    const fromUser = itemData.fromUser();\n    const reviewUser = itemData.reviewUser();\n    const reviewResult = itemData.review_result();\n    const reviewResultText = app.translator.trans(reviewResult===1?'wusong8899-user-submission.lib.list-submission-accept':'wusong8899-user-submission.lib.list-submission-decline');\n    const assignedAt = itemData.assigned_at();\n    const reviewedAt = itemData.reviewed_at();\n\n    // const bidText = moneyName.replace('[money]', bidValue);\n\n    return (\n      <div className=\"biddingRankSettingContainer\">\n        <div style=\"float:right\">\n          {reviewedAt && (\n            <div style=\"padding:0px 0px 5px 5px\">\n              {Button.component({\n                style: \"font-weight:bold;\",\n                disabled: true,\n                className: 'Button',\n              },\n              app.translator.trans('wusong8899-user-submission.admin.list-reviewed')\n              )}\n            </div>\n          )}\n          {!reviewedAt && (\n            <div style=\"padding:0px 0px 5px 5px\">\n              {Button.component({\n                style: \"font-weight:bold;\",\n                className: 'Button Button--primary',\n                onclick: (e) => {\n                  this.reviewItem(itemData)\n                }\n              },\n              app.translator.trans('wusong8899-user-submission.admin.list-review')\n              )}\n            </div>\n          )}\n        </div>\n        <div>\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-username')}: </b>\n          {username(fromUser)}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-amount')}: </b>\n          {amount}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-assignedAt')}: </b>\n          {assignedAt}\n        </div>\n        <div>\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-platform')}: </b>\n          {platform}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-platformAccount')}: </b>\n          {platformAccount}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-userAccount')}: </b>\n          {userAccount}\n        </div>\n        {reviewedAt && (\n          <div>\n            <b>{app.translator.trans('wusong8899-user-submission.lib.list-reviewResult')}: </b>\n            {reviewResultText}&nbsp;|&nbsp;\n            <b>{app.translator.trans('wusong8899-user-submission.lib.list-reviewAt')}: </b>\n            {reviewedAt}\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  reviewItem(itemData){\n    app.modal.show(UserSubmissionReviewModal, {itemData});\n  }\n}\n", "import ExtensionPage from 'flarum/components/ExtensionPage';\nimport LoadingIndicator from 'flarum/components/LoadingIndicator';\nimport Button from 'flarum/components/Button';\nimport UserSubmissionListItem from './UserSubmissionListItem';\n\nexport default class DecorationStoreSettings extends ExtensionPage {\n  oninit(attrs) {\n    super.oninit(attrs);\n    this.loading = true;\n    this.moreResults = false;\n    this.userSubmissionList = [];\n    this.loadResults();\n  }\n\n  content() {\n    let loading;\n\n    if(this.loading){\n      loading = LoadingIndicator.component({ size: \"large\" });\n    }\n\n    return (\n      <div className=\"ExtensionPage-settings FlarumBadgesPage\">\n        <div className=\"container\">\n\n          <ul style=\"padding:0px;list-style-type: none;\">\n            {this.userSubmissionList.map((itemData) => {\n              return (\n                <li itemID={itemData.id()} style=\"margin-top:5px;background: var(--body-bg);\">\n                  {UserSubmissionListItem.component({ itemData })}\n                </li>\n              );\n            })}\n          </ul>\n\n          {!this.loading && this.userSubmissionList.length===0 && (\n            <div>\n              <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;line-height: 100px;\">{app.translator.trans(\"wusong8899-decoration-store.lib.list-empty\")}</div>\n            </div>\n          )}\n\n          {!loading && this.hasMoreResults() && (\n            <div style=\"text-align:center;padding:20px\">\n              <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n                {app.translator.trans('wusong8899-user-submission.lib.list-load-more')}\n              </Button>\n            </div>\n          )}\n\n          {loading && <div className=\"UserSubmission-loadMore\">{loading}</div>}\n\n        </div>\n      </div>\n    );\n  }\n  \n  hasMoreResults() {\n    return this.moreResults;\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.userSubmissionList.length);\n  }\n\n  parseResults(results) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.userSubmissionList, results);\n\n    this.loading = false;\n    m.redraw();\n\n    return results;\n  }\n\n  loadResults(offset = 0) {\n    return app.store\n      .find(\"userSubmissionList\", {\n        page: {\n          offset\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Model'];", "import Model from \"flarum/Model\";\n\nexport default class UserSubmission extends Model {}\nObject.assign(UserSubmission.prototype, {\n  id: Model.attribute(\"id\"),\n  amount: Model.attribute(\"amount\"),\n  platform: Model.attribute(\"platform\"),\n  platform_account: Model.attribute(\"platform_account\"),\n  user_account: Model.attribute(\"user_account\"),\n  submission_user_id: Model.attribute(\"submission_user_id\"),\n  review_user_id: Model.attribute(\"review_user_id\"),\n  review_result: Model.attribute(\"review_result\"),\n  assigned_at: Model.attribute(\"assigned_at\"),\n  reviewed_at: Model.attribute(\"reviewed_at\"),\n  fromUser: Model.hasOne(\"fromUser\"),\n  reviewUser: Model.hasOne(\"reviewUser\"),\n});\n", "import {extend, override} from 'flarum/extend';\nimport SettingsPage from './components/SettingsPage';\nimport UserSubmission from \"../forum/model/UserSubmission\";\n\napp.initializers.add('wusong8899-user-submission', () => {\n  app.store.models.userSubmissionList = UserSubmission;\n  app.extensionData\n    .for('wusong8899-user-submission').registerPage(SettingsPage);\n});\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "flarum", "core", "compat", "UserSubmissionReviewModal", "oninit", "vnode", "this", "itemData", "attrs", "loading", "className", "title", "app", "translator", "trans", "content", "style", "<PERSON><PERSON>", "disabled", "onclick", "e", "reviewConfirm", "hide", "preventDefault", "save", "reviewResult", "then", "response", "Modal", "isDismissibleViaBackdropClick", "isDismissibleViaCloseButton", "UserSubmissionListItem", "view", "amount", "forum", "attribute", "platform", "platformAccount", "platform_account", "userAccount", "user_account", "fromUser", "reviewUser", "review_result", "reviewResultText", "assignedAt", "assigned_at", "reviewedAt", "reviewed_at", "reviewItem", "username", "modal", "show", "Component", "DecorationStoreSettings", "moreResults", "userSubmissionList", "loadResults", "LoadingIndicator", "size", "map", "itemID", "id", "component", "length", "hasMoreResults", "loadMore", "parseResults", "results", "payload", "links", "next", "push", "apply", "m", "redraw", "offset", "store", "find", "page", "bind", "ExtensionPage", "UserSubmission", "Model", "assign", "submission_user_id", "review_user_id", "initializers", "add", "models", "extensionData", "registerPage", "SettingsPage"], "sourceRoot": ""}