{"version": 3, "file": "forum.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,mBCAlF,MAAM,EAA+BI,OAAOC,KAAKC,OAAO,a,aCAxD,SAASC,EAAgBC,EAAGC,GAC1B,OAAOF,EAAkBZ,OAAOe,eAAiBf,OAAOe,eAAeC,OAAS,SAAUH,EAAGC,GAC3F,OAAOD,EAAEI,UAAYH,EAAGD,CAC1B,EAAGD,EAAgBC,EAAGC,EACxB,CCJqCL,OAAOC,KAAKC,OAAe,OCAhE,MAAM,EAA+BF,OAAOC,KAAKC,OAAc,M,aCE1CO,EAAU,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAC,MAAA,KAAAC,YAAA,KCD/B,IAAwBR,EAAGd,EDCI,OCDJA,EDCIoB,GCDPN,EDCOK,GCA3BZ,UAAYN,OAAOsB,OAAOvB,EAAEO,WAAYO,EAAEP,UAAUiB,YAAcV,EAAGE,EAAeF,EAAGd,GDA5DmB,CAAA,EAASM,KACxCxB,OAAOyB,OAAOP,EAAWZ,UAAW,CAClCoB,GAAIF,IAAAA,UAAgB,MACpBG,KAAMH,IAAAA,UAAgB,QACtBI,MAAOJ,IAAAA,UAAgB,SACvBK,KAAML,IAAAA,UAAgB,UEHxBM,IAAAA,aAAiBC,IAAI,iCAAkC,WACrDD,IAAAA,MAAUE,OAAOC,eAAiBf,CACpC,E", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/app']\"", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Model']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/LinksQueue.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Model'];", "import Model from \"flarum/Model\";\n\nexport default class LinksQueue extends Model {}\nObject.assign(LinksQueue.prototype, {\n  id: Model.attribute(\"id\"),\n  name: Model.attribute(\"name\"),\n  links: Model.attribute(\"links\"),\n  sort: Model.attribute(\"sort\"),\n});\n", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "import app from 'flarum/forum/app';\r\nimport { extend } from 'flarum/extend';\r\nimport LinksQueue from \"./model/LinksQueue\";\r\n\r\napp.initializers.add('wusong8899-client1-links-queue', () => {\r\n  app.store.models.linksQueueList = LinksQueue;\r\n});"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "flarum", "core", "compat", "_setPrototypeOf", "t", "e", "setPrototypeOf", "bind", "__proto__", "LinksQueue", "_Model", "apply", "arguments", "create", "constructor", "Model", "assign", "id", "name", "links", "sort", "app", "add", "models", "linksQueueList"], "sourceRoot": ""}