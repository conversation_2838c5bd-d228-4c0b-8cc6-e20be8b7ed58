{"version": 3, "file": "admin.js", "sources": ["../src/admin/components/SetBackgroundModal.tsx", "../src/admin/components/SettingsPage.tsx", "../src/admin/index.ts"], "sourcesContent": ["import app from 'flarum/admin/app';\nimport Modal, { IInternalModalAttrs } from 'flarum/common/components/Modal';\nimport Button from 'flarum/common/components/Button';\nimport Stream from 'flarum/common/utils/Stream';\nimport type Mithril from 'mithril';\nimport type Tag from 'flarum/tags/common/models/Tag';\n\ninterface SetBackgroundModalAttrs extends IInternalModalAttrs {\n  tagData: Tag;\n}\n\nexport default class SetBackgroundModal extends Modal<SetBackgroundModalAttrs> {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  loading = false;\n  tagData!: Tag;\n  backgroundUrl!: Stream<string | null>;\n\n  oninit(vnode: Mithril.Vnode<SetBackgroundModalAttrs, this>) {\n    super.oninit(vnode);\n    this.loading = false;\n    this.tagData = this.attrs.tagData;\n    this.backgroundUrl = Stream(this.tagData.attribute<string | null>('wusong8899BackgroundURL'));\n  }\n\n  className(): string {\n    return 'Modal--small';\n  }\n\n  title(): Mithril.Children {\n    return app.translator.trans('wusong8899-tag-background.admin.set-background');\n  }\n\n  content(): Mithril.Children {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            <div className=\"ModuleItemSettingsLabel\">\n              {app.translator.trans('wusong8899-tag-background.admin.item-background-url')}\n            </div>\n            {/* `bidi` is a compat attr supported by Flarum's Stream */}\n            <input maxlength=\"200\" className=\"FormControl\" bidi={this.backgroundUrl as any} />\n          </div>\n\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                style: 'min-width:66px;',\n                className: 'Button Button--primary',\n                disabled: this.loading,\n                onclick: () => {\n                  this.saveData();\n                },\n              },\n              app.translator.trans('wusong8899-tag-background.admin.save')\n            )}\n            &nbsp;\n            {Button.component(\n              {\n                style: 'min-width:66px;background: rgba(0,0,0,0.1);',\n                className: 'Button',\n                disabled: this.loading,\n                onclick: () => {\n                  this.hide();\n                },\n              },\n              app.translator.trans('wusong8899-tag-background.admin.cancel')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  saveData() {\n    this.loading = true;\n\n    const backgroundUrl = this.backgroundUrl();\n    const tagID = this.tagData.id()!;\n\n    app\n      .request({\n        url: `${app.forum.attribute('apiUrl')}/tagBackgroundSetImage`,\n        method: 'POST',\n        body: { tagID, backgroundUrl },\n      })\n      .then((result: any) => {\n        this.hide();\n        // pushPayload expects a JSON:API payload; the controller returns Tag payload\n        app.store.pushPayload(result as any);\n        // Ensure store is updated; force redraw\n        // eslint-disable-next-line no-console\n        console.log(app.store.getById('tags', tagID));\n        m.redraw();\n      })\n      .catch(() => {\n        this.loading = false;\n      });\n  }\n}", "import app from 'flarum/admin/app';\nimport ExtensionPage from 'flarum/admin/components/ExtensionPage';\nimport Button from 'flarum/common/components/Button';\nimport LoadingIndicator from 'flarum/common/components/LoadingIndicator';\nimport sortTags from 'flarum/tags/common/utils/sortTags';\nimport tagIcon from 'flarum/tags/common/helpers/tagIcon';\nimport type Tag from 'flarum/tags/common/models/Tag';\nimport type Mithril from 'mithril';\n\nimport SetBackgroundModal from './SetBackgroundModal';\n\nexport default class SettingsPage extends ExtensionPage {\n  loading = false;\n\n  oninit(vnode: Mithril.Vnode<unknown, this>) {\n    super.oninit(vnode);\n    this.loading = true;\n\n    // Load tag list with parents\n    // @ts-ignore tagList is provided by flarum/tags\n    app.tagList.load(['parent']).then(() => {\n      this.loading = false;\n      m.redraw();\n    });\n  }\n\n  content(vnode: Mithril.VnodeDOM<unknown, this>): JSX.Element {\n    if (this.loading) {\n      return <LoadingIndicator />;\n    }\n\n    const tags: Tag[] = sortTags(\n      // @ts-ignore tags model provided by flarum/tags\n      app.store.all('tags').filter((tag: Tag) => !tag.parent())\n    );\n\n    return (\n      <div className=\"tagBackgroundSettingsGroups\" style=\"text-align: left;padding: 20px;\">\n        {tags.map((tagData: Tag) => {\n          const wusong8899BackgroundURL = tagData.attribute<string | null>('wusong8899BackgroundURL');\n          const tagBackgroundImageStyle = `background:url(${wusong8899BackgroundURL});background-size: cover;background-position: center;background-repeat: no-repeat;`;\n\n          return (\n            <div className=\"tagBackgroundContainer\">\n              <div className=\"tagBackgroundItemContainer\">\n                {tagIcon(tagData)}\n                <span className=\"tagBackgroundItemName TagListItem-name\">{tagData.name()}</span>\n\n                <div style=\"padding-top: 10px;display: flex;justify-content: center;align-items: center;\">\n                  {wusong8899BackgroundURL && (\n                    <div\n                      style={tagBackgroundImageStyle as any}\n                      className=\"tagBackgroundImage\"\n                      onclick={() => app.modal.show(SetBackgroundModal, { tagData })}\n                    ></div>\n                  )}\n\n                  {!wusong8899BackgroundURL && (\n                    <div className=\"tagBackgroundImage\">\n                      {Button.component(\n                        {\n                          style: 'min-width: 66px;font-size: 12px;font-weight: normal;',\n                          className: 'Button',\n                          onclick: () => {\n                            app.modal.show(SetBackgroundModal, { tagData });\n                          },\n                        },\n                        app.translator.trans('wusong8899-tag-background.admin.set-background')\n                      )}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    );\n  }\n}\n\n", "import app from 'flarum/admin/app';\nimport SettingsPage from './components/SettingsPage';\n\napp.initializers.add('wusong8899-tag-background', () => {\n  app.extensionData.for('wusong8899-tag-background').registerPage(SettingsPage);\n});\n\n"], "names": ["_SetBackgroundModal", "Modal", "vnode", "Stream", "app", "<PERSON><PERSON>", "backgroundUrl", "tagID", "result", "SetBackgroundModal", "SettingsPage", "ExtensionPage", "LoadingIndicator", "tags", "sortTags", "tag", "tagData", "wusong8899BackgroundURL", "tagBackgroundImageStyle", "tagIcon"], "mappings": "wCAWA,MAAqBA,EAArB,MAAqBA,UAA2BC,CAA+B,CAA/E,aAAA,CAAA,MAAA,GAAA,SAAA,EAIE,KAAA,QAAU,EAAA,CAIV,OAAOC,EAAqD,CAC1D,MAAM,OAAOA,CAAK,EAClB,KAAK,QAAU,GACf,KAAK,QAAU,KAAK,MAAM,QAC1B,KAAK,cAAgBC,EAAO,KAAK,QAAQ,UAAyB,yBAAyB,CAAC,CAC9F,CAEA,WAAoB,CAClB,MAAO,cACT,CAEA,OAA0B,CACxB,OAAOC,EAAI,WAAW,MAAM,gDAAgD,CAC9E,CAEA,SAA4B,CAC1B,SACG,MAAA,CAAI,UAAU,cACb,EAAC,MAAA,CAAI,UAAU,QACb,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,qBAAA,IAC/B,MAAA,CAAI,UAAU,2BACZA,EAAI,WAAW,MAAM,qDAAqD,CAC7E,EAEA,EAAC,SAAM,UAAU,MAAM,UAAU,cAAc,KAAM,KAAK,cAAsB,CAClF,EAEA,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,uBAC/BC,EAAO,UACN,CACE,MAAO,kBACP,UAAW,yBACX,SAAU,KAAK,QACf,QAAS,IAAM,CACb,KAAK,SAAA,CACP,CAAA,EAEFD,EAAI,WAAW,MAAM,sCAAsC,CAAA,EAC3D,IAEDC,EAAO,UACN,CACE,MAAO,8CACP,UAAW,SACX,SAAU,KAAK,QACf,QAAS,IAAM,CACb,KAAK,KAAA,CACP,CAAA,EAEFD,EAAI,WAAW,MAAM,wCAAwC,CAAA,CAEjE,CACF,CACF,CAEJ,CAEA,UAAW,CACT,KAAK,QAAU,GAEf,MAAME,EAAgB,KAAK,cAAA,EACrBC,EAAQ,KAAK,QAAQ,GAAA,EAE3BH,EACG,QAAQ,CACP,IAAK,GAAGA,EAAI,MAAM,UAAU,QAAQ,CAAC,yBACrC,OAAQ,OACR,KAAM,CAAE,MAAAG,EAAO,cAAAD,CAAA,CAAc,CAC9B,EACA,KAAME,GAAgB,CACrB,KAAK,KAAA,EAELJ,EAAI,MAAM,YAAYI,CAAa,EAGnC,QAAQ,IAAIJ,EAAI,MAAM,QAAQ,OAAQG,CAAK,CAAC,EAC5C,EAAE,OAAA,CACJ,CAAC,EACA,MAAM,IAAM,CACX,KAAK,QAAU,EACjB,CAAC,CACL,CACF,EAzFEP,EAAO,8BAAgC,GACvCA,EAAO,4BAA8B,GAFvC,IAAqBS,EAArBT,ECAA,MAAqBU,UAAqBC,CAAc,CAAxD,aAAA,CAAA,MAAA,GAAA,SAAA,EACE,KAAA,QAAU,EAAA,CAEV,OAAOT,EAAqC,CAC1C,MAAM,OAAOA,CAAK,EAClB,KAAK,QAAU,GAIfE,EAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,EAAE,KAAK,IAAM,CACtC,KAAK,QAAU,GACf,EAAE,OAAA,CACJ,CAAC,CACH,CAEA,QAAQF,EAAqD,CAC3D,GAAI,KAAK,QACP,SAAQU,EAAA,IAAiB,EAG3B,MAAMC,EAAcC,EAElBV,EAAI,MAAM,IAAI,MAAM,EAAE,OAAQW,GAAa,CAACA,EAAI,OAAA,CAAQ,CAAA,EAG1D,OACE,EAAC,OAAI,UAAU,8BAA8B,MAAM,mCAChDF,EAAK,IAAKG,GAAiB,CAC1B,MAAMC,EAA0BD,EAAQ,UAAyB,yBAAyB,EACpFE,EAA0B,kBAAkBD,CAAuB,qFAEzE,OACE,EAAC,OAAI,UAAU,wBAAA,IACZ,MAAA,CAAI,UAAU,4BAAA,EACZE,EAAQH,CAAO,IACf,OAAA,CAAK,UAAU,0CAA0CA,EAAQ,KAAA,CAAO,EAEzE,EAAC,MAAA,CAAI,MAAM,8EAAA,EACRC,GACC,EAAC,MAAA,CACC,MAAOC,EACP,UAAU,qBACV,QAAS,IAAMd,EAAI,MAAM,KAAKK,EAAoB,CAAE,QAAAO,EAAS,CAAA,CAAA,EAIhE,CAACC,KACC,MAAA,CAAI,UAAU,sBACZZ,EAAO,UACN,CACE,MAAO,uDACP,UAAW,SACX,QAAS,IAAM,CACbD,EAAI,MAAM,KAAKK,EAAoB,CAAE,QAAAO,EAAS,CAChD,CAAA,EAEFZ,EAAI,WAAW,MAAM,gDAAgD,CAAA,CAEzE,CAEJ,CACF,CACF,CAEJ,CAAC,CACH,CAEJ,CACF,CC5EAA,EAAI,aAAa,IAAI,4BAA6B,IAAM,CACtDA,EAAI,cAAc,IAAI,2BAA2B,EAAE,aAAaM,CAAY,CAC9E,CAAC"}