{"version": 3, "file": "forum.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,mBCAlF,MAAM,EAA+BI,OAAOC,KAAKC,OAAO,a,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAe,OCA1D,EAA+BF,OAAOC,KAAKC,OAAO,wB,aCAxD,SAASC,EAAgBC,EAAGC,GAC1B,OAAOF,EAAkBZ,OAAOe,eAAiBf,OAAOe,eAAeC,OAAS,SAAUH,EAAGC,GAC3F,OAAOD,EAAEI,UAAYH,EAAGD,CAC1B,EAAGD,EAAgBC,EAAGC,EACxB,CCHA,SAASI,EAAeL,EAAGd,GACzBc,EAAEP,UAAYN,OAAOmB,OAAOpB,EAAEO,WAAYO,EAAEP,UAAUc,YAAcP,EAAGE,EAAeF,EAAGd,EAC3F,CCHqCU,OAAOC,KAAKC,OAAO,yBCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,mB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,+B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,qB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAkB,U,aCA9BF,OAAOC,KAAKC,OAAO,mBAAxD,MCAM,EAA+BF,OAAOC,KAAKC,OAAO,oB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,kB,aCInCU,EAA8B,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAC,MAAA,KAAAC,YAAA,KAAAN,EAAAG,EAAAC,GAAA,IAAAG,EAAAJ,EAAAf,UA2ChD,OA3CgDmB,EAIjDC,OAAA,SAAOC,GACLL,EAAAhB,UAAMoB,OAAMlB,KAAC,KAAAmB,GACbC,KAAKC,oBAAsBD,KAAKE,MAAMD,mBACxC,EAACJ,EAEDM,UAAA,WACE,MAAO,cACT,EAACN,EAEDO,MAAA,WACE,OAAOC,IAAAA,WAAeC,MAAM,2DAC9B,EAACT,EAEDU,QAAA,WAAU,IAAAC,EAAA,KACFC,EAAYJ,IAAAA,MAAUK,UAAU,8BAAgC,UAChEC,EAAWX,KAAKC,oBAAoBS,UAAU,OAC9CE,EAAUZ,KAAKC,oBAAoBY,WACnCC,EAASd,KAAKC,oBAAoBc,MAClCC,EAAahB,KAAKC,oBAAoBM,UAItCU,GAHUR,EAAUS,QAAQ,UAAWP,GAG1BQ,IAAOP,IAE1B,OACEQ,EAAA,OAAKjB,UAAU,cACbiB,EAAA,OAAKC,MAAM,oBAAoBC,QAAS,WAAF,OAAQd,EAAKe,QAAQT,EAAO,GAChEM,EAAA,OAAKC,MAAM,cAAcJ,GACzBG,EAAA,OAAKC,MAAM,uBACTD,EAAA,WAAMJ,GACNI,EAAA,OAAKC,MAAM,sBAAsBP,KAK3C,EAACjB,EAED0B,QAAA,SAAQT,GACNU,OAAOC,KAAKX,EAAQ,UAAUY,OAChC,EAACjC,CAAA,CA3CgD,CAASkC,KAAvClC,EACZmC,+BAAgC,EADpBnC,EAEZoC,6BAA8B,ECNvC,MAAM,EAA+BhD,OAAOC,KAAKC,OAAO,gB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,2B,aCInC+C,EAAoC,SAAApC,GAAA,SAAAoC,IAAA,OAAApC,EAAAC,MAAA,KAAAC,YAAA,KAAAN,EAAAwC,EAAApC,GAAA,IAAAG,EAAAiC,EAAApD,UAmCtD,OAnCsDmB,EAIvDC,OAAA,SAAOC,GACLL,EAAAhB,UAAMoB,OAAMlB,KAAC,KAAAmB,EACf,EAACF,EAEDM,UAAA,WACE,MAAO,cACT,EAACN,EAEDO,MAAA,WACE,OAAOC,IAAAA,WAAeC,MAAM,uDAC9B,EAACT,EAEDU,QAAA,WACE,OACEa,EAAA,OAAKjB,UAAU,cACbiB,EAAA,OAAKjB,UAAU,QACbiB,EAAA,OAAKjB,UAAU,aAAakB,MAAM,uBAC/BU,IAAAA,UACC,CACE5B,UAAW,yBACX6B,QAAShC,KAAKgC,QACdV,QAAS,WACPW,SAASC,QACX,GAEF7B,IAAAA,WAAeC,MAAM,uCAMjC,EAACwB,CAAA,CAnCsD,CAASH,KAA7CG,EACZF,+BAAgC,EADpBE,EAEZD,6BAA8B,ECJO,IAEzBM,EAAkC,SAAAzC,GAAA,SAAAyC,IAAA,OAAAzC,EAAAC,MAAA,KAAAC,YAAA,KAAAN,EAAA6C,EAAAzC,GAAA,IAAAG,EAAAsC,EAAAzD,UAmCpD,OAnCoDmB,EAIrDC,OAAA,SAAOC,GACLL,EAAAhB,UAAMoB,OAAMlB,KAAC,KAAAmB,EACf,EAACF,EAEDM,UAAA,WACE,MAAO,cACT,EAACN,EAEDO,MAAA,WACE,OAAOC,IAAAA,WAAeC,MAAM,qDAC9B,EAACT,EAEDU,QAAA,WACE,OACEa,EAAA,OAAKjB,UAAU,cACbiB,EAAA,OAAKjB,UAAU,QACbiB,EAAA,OAAKjB,UAAU,aAAakB,MAAM,uBAC/BU,IAAAA,UACC,CACE5B,UAAW,yBACX6B,QAAShC,KAAKgC,QACdV,QAAS,WACPW,SAASC,QACX,GAEF7B,IAAAA,WAAeC,MAAM,uCAMjC,EAAC6B,CAAA,CAnCoD,CAASR,KAA3CQ,EACZP,+BAAgC,EADpBO,EAEZN,6BAA8B,ECA+C,IAEjEO,EAA6B,SAAA1C,GAAA,SAAA0C,IAAA,OAAA1C,EAAAC,MAAA,KAAAC,YAAA,KAAAN,EAAA8C,EAAA1C,GAAA,IAAAG,EAAAuC,EAAA1D,UAgJ/C,OAhJ+CmB,EAIhDC,OAAA,SAAOC,GACLL,EAAAhB,UAAMoB,OAAMlB,KAAC,KAAAmB,GACbC,KAAKqC,SAAWrC,KAAKE,MAAMmC,SAC3BrC,KAAKsC,YAAc,MACnBtC,KAAKgC,SAAU,EAEZhC,KAAKqC,UACNrC,KAAKsC,YAAc,OACnBtC,KAAKuC,IAAMC,IAAOxC,KAAKqC,SAASE,OAChCvC,KAAKe,IAAMyB,IAAOxC,KAAKqC,SAAStB,OAChCf,KAAKgB,WAAawB,IAAOxC,KAAKqC,SAAS9B,WACvCP,KAAKyC,SAAWD,IAAOxC,KAAKqC,SAASjC,SACrCJ,KAAK0C,SAAWF,IAAO,KAEvBxC,KAAKuC,IAAMC,IAAO,KAClBxC,KAAKe,IAAMyB,IAAO,IAClBxC,KAAKyC,SAAWD,IAAO,IACvBxC,KAAKgB,WAAawB,IAAO,IAE7B,EAAC3C,EAEDM,UAAA,WACE,MAAO,cACT,EAACN,EAEDO,MAAA,WACE,OAAOC,IAAAA,WAAeC,MAAyB,QAAnBN,KAAKsC,YAAoB,+CAA+C,6CACtG,EAACzC,EAEDU,QAAA,WAAU,IAAAC,EAAA,KACR,OACEY,EAAA,OAAKjB,UAAU,cACbiB,EAAA,OAAKjB,UAAU,QACbiB,EAAA,OAAKjB,UAAU,aAAakB,MAAM,uBAEV,QAAnBrB,KAAKsC,aACJlB,EAAA,WACEA,EAAA,OAAKjB,UAAU,wBAAwBE,IAAAA,WAAeC,MAAM,wDAC5Dc,EAAA,SAAOuB,KAAK,SAASC,IAAI,QAAQC,SAAU7C,KAAKgC,QAASc,UAAQ,EAAC3C,UAAU,cAAc4C,KAAM/C,KAAKuC,OAIrF,SAAnBvC,KAAKsC,aACJlB,EAAA,WACEA,EAAA,OAAKjB,UAAU,wBAAwBE,IAAAA,WAAeC,MAAM,uDAAuD,KAAGN,KAAKuC,OAC3HnB,EAAA,OAAKjB,UAAU,wBAAwBE,IAAAA,WAAeC,MAAM,8DAC5Dc,EAAA,SAAOuB,KAAK,SAASC,IAAI,IAAIC,SAAU7C,KAAKgC,QAASc,UAAQ,EAAC3C,UAAU,cAAc4C,KAAM/C,KAAK0C,YAIrGtB,EAAA,OAAKjB,UAAU,wBAAwBE,IAAAA,WAAeC,MAAM,0DAC5Dc,EAAA,SAAO4B,UAAU,IAAIH,SAAU7C,KAAKgC,QAASc,UAAQ,EAAC3C,UAAU,cAAc4C,KAAM/C,KAAKyC,WAEzFrB,EAAA,OAAKjB,UAAU,wBAAwBE,IAAAA,WAAeC,MAAM,wDAC5Dc,EAAA,SAAO4B,UAAU,MAAMH,SAAU7C,KAAKgC,QAASc,UAAQ,EAAC3C,UAAU,cAAc4C,KAAM/C,KAAKe,MAE3FK,EAAA,OAAKjB,UAAU,wBAAwBE,IAAAA,WAAeC,MAAM,4DAC5Dc,EAAA,YAAU4B,UAAU,MAAMH,SAAU7C,KAAKgC,QAASc,UAAQ,EAAC3C,UAAU,cAAc4C,KAAM/C,KAAKgB,cAGlGI,EAAA,OAAKjB,UAAU,aAAakB,MAAM,uBAC/BU,IAAAA,UACC,CACE5B,UAAW,yBACXwC,KAAM,SACNX,QAAShC,KAAKgC,SAEhB3B,IAAAA,WAAeC,MAAM,wDACrB,IACDyB,IAAAA,UACC,CACE5B,UAAW,SACX6B,QAAShC,KAAKgC,QACdV,QAAS,WACPd,EAAKyC,MACP,GAEF5C,IAAAA,WAAeC,MAAM,yDAMjC,EAACT,EAGDqD,SAAA,SAAShE,GAAG,IAAAiE,EAAA,KACVjE,EAAEkE,iBAEF,IAAMC,EAAYhD,IAAAA,QAAYiD,KAAK5C,UAAU,SAEvB,QAAnBV,KAAKsC,aAAuBe,EAAUrD,KAAKuC,OAKxB,SAAnBvC,KAAKsC,aAAwBe,EAAUrD,KAAK0C,WAJ7CrC,IAAAA,OAAWkD,KAAKC,IAAO,CAACb,KAAM,SAAUtC,IAAAA,WAAeC,MAAM,qDAS/DN,KAAKgC,SAAU,EAEO,SAAnBhC,KAAKsC,YACNtC,KAAKqC,SAASoB,KAAK,CACjBf,SAAS1C,KAAK0C,WACd3B,IAAIf,KAAKe,MACTR,QAAQP,KAAKgB,aACbZ,MAAMJ,KAAKyC,aAEZiB,KACC,SAACC,GACCR,EAAKnB,SAAU,EACf3B,IAAAA,MAAUkD,KAAKpB,EACjB,GACD,MACM,SAACjD,GACNiE,EAAKnB,SAAU,CACjB,GAEA3B,IAAAA,MACCuD,aAAa,mBACbH,KAAK,CACJlB,IAAIvC,KAAKuC,MACTxB,IAAIf,KAAKe,MACTR,QAAQP,KAAKgB,aACbZ,MAAMJ,KAAKyC,aAEZiB,KACC,SAACG,GACCxD,IAAAA,MAAUyD,YAAYD,GACtBxD,IAAAA,QAAYiD,KAAKS,KAAKC,WAAWC,OAAOd,EAAKZ,MAC7CY,EAAKnB,SAAU,EACf3B,IAAAA,MAAUkD,KAAKzB,EACjB,GACD,MACM,SAAC5C,GACNiE,EAAKnB,SAAU,CACjB,GAEJ,EAACI,CAAA,CAhJ+C,CAAST,KAAtCS,EACZR,+BAAgC,EADpBQ,EAEZP,6BAA8B,ECPqC,IAEvDqC,EAA0B,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAxE,MAAA,KAAAC,YAAA,KAAAN,EAAA4E,EAAAC,GAAA,IAAAtE,EAAAqE,EAAAxF,UA4D5C,OA5D4CmB,EAC7CuE,KAAA,WAAO,IAAA5D,EAAA,KACL6D,EAAqCrE,KAAKE,MAAnCD,EAAmBoE,EAAnBpE,oBAAoBqE,EAAMD,EAANC,OACrB7D,EAAYJ,IAAIkE,MAAM7D,UAAU,8BAAgC,UAChEC,EAAWV,EAAoBS,UAAU,OACzCE,EAAUX,EAAoBY,WAG9B4B,GAFSxC,EAAoBc,MAChBd,EAAoBM,UACtBN,EAAoBG,SAGjCoE,GAFY/D,EAAUS,QAAQ,UAAWP,GAE3B,IAEfN,IAAIoE,QAAQnB,MACAjD,IAAIoE,QAAQnB,KAAKoB,MAEnB9D,EAAQ8D,OACjBF,EAAcnE,IAAIsE,WAAWrE,MAAM,oDAIvC,IAAIsE,EAAgBN,EAAO,GAAI,EAAE,uBAAuB,GAMxD,MAJiB,KAAdE,IACDI,EAAgB,0BAIhBxD,EAAA,OAAKjB,UAAU,+BAA+BkB,MAAOuD,EAAetD,QAAS,WAAF,OAAQd,EAAKqE,YAAY5E,EAAoBW,EAAQ8D,KAAK,GACnItD,EAAA,OAAKC,MAAM,yBAAyBlB,UAAU,gCAC5CiB,EAAA,WAAMqB,IAERrB,EAAA,OAAKC,MAAM,uDAAuDlB,UAAU,8BACzEQ,EACDS,EAAA,KAAGjB,UAAU,4DAA4DkB,MAAM,uBAIvF,EAACxB,EAEDgF,YAAA,SAAY5E,EAAoB6E,GAC3BzE,IAAIoE,QAAQnB,MACAjD,IAAIoE,QAAQnB,KAAKoB,MAEnBI,EACT9E,KAAK+E,YAAY9E,GAKjBD,KAAKgF,YAAY/E,EAEvB,EAACJ,EAEDmF,YAAA,SAAY/E,GACVI,IAAI4E,MAAM1B,KAAK9D,EAAgC,CAACQ,oBAAAA,GAClD,EAACJ,EAEDkF,YAAA,SAAY1C,GACVhC,IAAI4E,MAAM1B,KAAKnB,EAA8B,CAACC,SAAAA,GAChD,EAAC6B,CAAA,CA5D4C,CAASgB,KCLxD,MAAM,EAA+BrG,OAAOC,KAAKC,OAAO,yB,aCUnCoG,EAA2B,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAzF,MAAA,KAAAC,YAAA,KAAAN,EAAA6F,EAAAC,GAAA,IAAAvF,EAAAsF,EAAAzG,UAkJ7C,OAlJ6CmB,EAC9CC,OAAA,SAAOC,GACLqF,EAAA1G,UAAMoB,OAAMlB,KAAC,KAAAmB,GACbC,KAAKgC,SAAU,EACfhC,KAAKqF,aAAc,EACnBrF,KAAKsF,gBAAkB,GACvBtF,KAAKuF,QAAS,EACdvF,KAAKwF,uBAAyB,CAAC,EAE/BxF,KAAKyF,aACP,EAAC5F,EAEDuE,KAAA,WAAO,IACDpC,EADCxB,EAAA,KAED8D,EAAS,EAMb,OAJGtE,KAAKgC,UACNA,EAAU0D,IAAAA,UAA2B,CAACC,KAAM,WAI5CvE,EAAA,OAAKjB,UAAU,wBACZyF,IAAAA,UAAoBC,OAErBzE,EAAA,OAAKjB,UAAU,aACbiB,EAAA,OAAKjB,UAAU,oBACbiB,EAAA,OAAKjB,UAAU,yBACbiB,EAAA,UAAK0E,IAAUF,IAAAA,UAAoBG,eAAeC,aAGpD5E,EAAA,OAAKjB,UAAU,wBACbiB,EAAA,OAAKjB,UAAU,mCAAmCkB,MAAM,2EACtDD,EAAA,OAAK6E,MAAM,eAAc7E,EAAA,OAAK6E,MAAM,kBAAiB7E,EAAA,UAAQC,MAAM,gBAAgBsB,KAAK,SAASsD,MAAM,SAAQ7E,EAAA,OAAK8E,IAAI,yqKAAyqK7E,MAAM,wDAAwDD,EAAA,OAAK6E,MAAM,cAAc5F,IAAIsE,WAAWrE,MAAM,wDAAqEc,EAAA,OAAKsD,GAAG,2BAA2BuB,MAAM,cAAc5E,MAAM,wCAEjhLD,EAAA,OAAKC,MAAM,uEACPrB,KAAKgC,UAAyB,IAAdhC,KAAKuF,QACrBnE,EAACW,IAAM,CAACV,MAAM,gGAAgGlB,UAAW,yBAA0BmB,QAAS,WAAF,OAAQd,EAAK2F,eAAe,GACnL9F,IAAIsE,WAAWrE,MAAM,kDAIxBN,KAAKgC,UAAyB,IAAdhC,KAAKuF,QACrBnE,EAACW,IAAM,CAACV,MAAM,gGAAgGlB,UAAW,yBAA0BmB,QAAS,WAAF,OAAQd,EAAKuE,aAAa,GACjL1E,IAAIsE,WAAWrE,MAAM,gDAK5Bc,EAAA,OAAK6E,MAAM,yBACT7E,EAAA,OAAK6E,MAAM,+BAA+B5E,MAAM,sBAAsBhB,IAAIsE,WAAWrE,MAAM,0DAC3Fc,EAAA,OAAK6E,MAAM,6BAA6B5E,MAAM,4CAA4ChB,IAAIsE,WAAWrE,MAAM,yDAGjHc,EAAA,OAAKjB,UAAU,mBACZH,KAAKsF,gBAAgBc,IAAI,SAACnG,GAGzB,OAFAqE,IAGElD,EAAA,OAAKjB,UAAU,wBACZ+D,EAA2BmC,UAAU,CAAEpG,oBAAAA,EAAqBqE,OAAAA,IAGnE,KAGAtE,KAAKgC,SAAyC,IAA9BhC,KAAKsF,gBAAgBgB,QACrClF,EAAA,WACEA,EAAA,OAAKC,MAAM,uGAAuGhB,IAAIsE,WAAWrE,MAAM,+CAIzI0B,GAAWhC,KAAKuG,kBAChBnF,EAAA,OAAKC,MAAM,kCACTD,EAACW,IAAM,CAAC5B,UAAW,yBAA0B0C,SAAU7C,KAAKgC,QAASA,QAAShC,KAAKgC,QAASV,QAAS,WAAF,OAAQd,EAAKgG,UAAU,GACvHnG,IAAIsE,WAAWrE,MAAM,8DAK3B0B,GAAWZ,EAAA,OAAKjB,UAAU,6BAA6B6B,MAMpE,EAACnC,EAEDsG,cAAA,WACK9F,IAAIoE,QAAQnB,KACbjD,IAAI4E,MAAM1B,KAAKnB,GAEf/B,IAAI4E,MAAM1B,KAAKkD,IAEnB,EAAC5G,EAEDkF,YAAA,WACE,IAAM1C,EAAWrC,KAAKwF,uBACtBnF,IAAI4E,MAAM1B,KAAKnB,EAA8B,CAACC,SAAAA,GAChD,EAACxC,EAED0G,eAAA,WACE,OAAOvG,KAAKqF,WACd,EAACxF,EAED2G,SAAA,WACExG,KAAKgC,SAAU,EACfhC,KAAKyF,YAAYzF,KAAKsF,gBAAgBgB,OACxC,EAACzG,EAED6G,aAAA,SAAaC,GAMX,OALA3G,KAAKqF,cAAgBsB,EAAQC,QAAQC,SAAWF,EAAQC,QAAQC,MAAMC,KACtE,GAAGC,KAAKpH,MAAMK,KAAKsF,gBAAiBqB,GACpC3G,KAAKgC,SAAU,EACfZ,EAAE4F,SAEKL,CAkBT,EAAC9G,EAED4F,YAAA,SAAYwB,GAEV,YAFgB,IAANA,IAAAA,EAAS,GAEZ5G,IAAI6G,MACRC,KAAK,kBAAmB,CACvBC,KAAM,CACJH,OAAAA,EACAI,MALQ,MAOV,MACK,WAAO,GACb3D,KAAK1D,KAAK0G,aAAatH,KAAKY,MACjC,EAACmF,CAAA,CAlJ6C,CAASmC,KCVzD,MAAM,EAA+BzI,OAAOC,KAAKC,OAAc,M,aCE1CwI,EAAW,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAA7H,MAAA,KAAAC,YAAA,YAAAN,EAAAiI,EAAAC,GAAAD,CAAA,EAASE,KACzCrJ,OAAOsJ,OAAOH,EAAY7I,UAAW,CACnCgG,GAAI+C,IAAAA,UAAgB,MACpBrH,MAAOqH,IAAAA,UAAgB,SACvBlH,QAASkH,IAAAA,UAAgB,WACzBE,QAASF,IAAAA,UAAgB,WACzB1G,IAAK0G,IAAAA,UAAgB,OACrBlF,IAAKkF,IAAAA,UAAgB,OACrB5G,SAAU4G,IAAAA,OAAa,cCVzB,MAAM,EAA+B5I,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,oC,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,0B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,0B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,uB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,wB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,yB,aCuBxDsB,IAAAA,aAAiBuH,IAAI,0CAA2C,WAC9DvH,IAAAA,OAAwB,YAAI,CAC1BwH,KAAM,eACNxB,UAAWlB,GAGb9E,IAAAA,MAAUyH,OAAOxC,gBAAkBiC,EAEnC,IAAI/G,EAAQuH,IAAAA,WACZC,EAAAA,EAAAA,QAAOD,IAAAA,UAAoB,SAAU,SAAUhI,GAE7CS,EAAMyH,oBAAqB,EAC3BzH,EAAM6E,aAAc,EACpB7E,EAAM8E,gBAAkB,GACxB9E,EAAM+E,QAAS,EACf/E,EAAMgF,uBAAyB,CAAC,EAEhChF,EAAMiF,aAER,GAEAsC,IAAAA,UAAmBtC,YAAc,SAASwB,GAExC,YAF8C,IAANA,IAAAA,EAAS,GAE1C5G,IAAAA,MACJ8G,KAAK,kBAAmB,CACvBC,KAAM,CACJH,OAAAA,EACAI,MALQ,MAOV,MACK,WAAO,GACb3D,KAAKlD,EAAMkG,aAAatH,KAAKoB,GAClC,EAEAuH,IAAAA,UAAmBrB,aAAe,SAASC,GAMzC,OALAnG,EAAM6E,cAAgBsB,EAAQC,QAAQC,SAAWF,EAAQC,QAAQC,MAAMC,KACvE,GAAGC,KAAKpH,MAAMa,EAAM8E,gBAAiBqB,GACrCnG,EAAMyH,oBAAqB,EAC3B7G,EAAE4F,SAEKL,CACT,EAEAoB,IAAAA,UAAmB5B,cAAgB,WAC9B9F,IAAAA,QAAYiD,KACbjD,IAAAA,MAAUkD,KAAKnB,GAEf/B,IAAAA,MAAUkD,KAAKkD,IAEnB,EAEAsB,IAAAA,UAAmBhD,YAAc,WAC/B,IAAM1C,EAAW7B,EAAMgF,uBACvBnF,IAAAA,MAAUkD,KAAKnB,EAA8B,CAACC,SAAAA,GAChD,EAEA0F,IAAAA,UAAmBxB,eAAiB,WAClC,OAAO/F,EAAM6E,WACf,EAEA0C,IAAAA,UAAmBvB,SAAW,WAC5BhG,EAAMyH,oBAAqB,EAC3BzH,EAAMiF,YAAYjF,EAAM8E,gBAAgBgB,OAC1C,GAEA4B,EAAAA,EAAAA,UAASH,IAAAA,UAAoB,OAAQ,SAAUhI,GAAO,IAEhDkI,EAFgD9E,EAAA,KAChDmB,EAAS,EAOb,GAJG9D,EAAMyH,qBACPA,EAAqBvC,IAAAA,UAA2B,CAACC,KAAM,WAGrD3F,KAAKgC,QACP,OAAOZ,EAACsE,IAAgB,MAG1B,IAAMyC,EAASnI,KAAKoI,KAAKC,OAAO,SAACC,GAAG,OAAwB,OAAnBA,EAAIC,UAAmB,GAC1DC,EAAQxI,KAAKoI,KAAKC,OAAO,SAACC,GAAG,OAAwB,OAAnBA,EAAIC,UAAmB,GAE/D,OACEnH,EAAA,OAAKjB,UAAU,YACZyF,IAAAA,UAAoBC,OACrBzE,EAAA,OAAKjB,UAAU,aACbiB,EAAA,OAAKjB,UAAU,sCACbiB,EAAA,UAAK0E,IAAUF,IAAAA,UAAoBG,eAAeC,aAGpD5E,EAAA,OAAKjB,UAAU,kCACbiB,EAAA,MAAIjB,UAAU,YACXgI,EAAO/B,IAAI,SAACkC,GACX,IAAMG,EAAuBH,EAAIG,uBAC3BC,EAAWC,IAASL,EAAII,YAAc,IAG5C,OACEtH,EAAA,MAAIjB,UAAWyI,IAAU,UAAW,CAAEC,QAASP,EAAIQ,SAAWC,IAAkBT,EAAIQ,WAClF1H,EAAC4H,IAAI,CAAC7I,UAAU,eAAe8I,KAAM5I,IAAAA,MAAUiI,IAAIA,IAChDA,EAAIY,QAAUC,IAAQb,EAAK,CAAC,EAAG,CAAEc,UAAU,IAC5ChI,EAAA,MAAIjB,UAAU,gBAAgBmI,EAAIe,QAClCjI,EAAA,KAAGjB,UAAU,uBAAuBmI,EAAIgB,eACvCZ,EACCtH,EAAA,OAAKjB,UAAU,oBACZuI,EAAStC,IAAI,SAACmD,GAAK,MAAK,CAACnI,EAAC4H,IAAI,CAACC,KAAM5I,IAAAA,MAAUiI,IAAIiB,IAASA,EAAMF,QAAgB,IAAI,IAGzF,IAGHZ,EACCrH,EAAC4H,IAAI,CACH7I,UAAU,+BACV8I,KAAM5I,IAAAA,MAAUmJ,WAAWf,EAAsBA,EAAqBgB,mBAEtErI,EAAA,QAAMjB,UAAU,sCAAsCsI,EAAqBrI,SAC1EsJ,IAAUjB,EAAqBkB,iBAGlCvI,EAAA,QAAMjB,UAAU,iCAIxB,IAGDqI,EAAMlC,OAASlF,EAAA,OAAKjB,UAAU,YAAYqI,EAAMpC,IAAI,SAACkC,GAAG,MAAK,CAACsB,IAAStB,EAAK,CAAEuB,MAAM,IAAS,IAAI,IAAW,IAI/GzI,EAAA,OAAKjB,UAAU,wBACbiB,EAAA,OAAKjB,UAAU,mCAAmCkB,MAAM,2EACtDD,EAAA,OAAK6E,MAAM,eAAc7E,EAAA,OAAK6E,MAAM,kBAAiB7E,EAAA,UAAQC,MAAM,gBAAgBsB,KAAK,SAASsD,MAAM,SAAQ7E,EAAA,OAAK8E,IAAI,yqKAAyqK7E,MAAM,wDAAwDD,EAAA,OAAK6E,MAAM,cAAc5F,IAAAA,WAAeC,MAAM,wDAAqEc,EAAA,OAAKsD,GAAG,2BAA2BuB,MAAM,cAAc5E,MAAM,wCAEjhLD,EAAA,OAAKC,MAAM,+FACPb,EAAMyH,qBAAqC,IAAfzH,EAAM+E,QAClCnE,EAACW,IAAM,CAACV,MAAM,gGAAgGlB,UAAW,yBAA0BmB,QAAS,WAAF,OAAQ6B,EAAKgD,eAAe,GACnL9F,IAAAA,WAAeC,MAAM,kDAIxBE,EAAMyH,qBAAqC,IAAfzH,EAAM+E,QAClCnE,EAACW,IAAM,CAACV,MAAM,gGAAgGlB,UAAW,yBAA0BmB,QAAS,WAAF,OAAQ6B,EAAK4B,aAAa,GACjL1E,IAAAA,WAAeC,MAAM,gDAK5Bc,EAAA,OAAK6E,MAAM,yBACT7E,EAAA,OAAK6E,MAAM,+BAA+B5E,MAAM,sBAAsBhB,IAAAA,WAAeC,MAAM,0DAC3Fc,EAAA,OAAK6E,MAAM,6BAA6B5E,MAAM,4CAA4ChB,IAAAA,WAAeC,MAAM,yDAGjHc,EAAA,OAAKjB,UAAU,mBACZK,EAAM8E,gBAAgBc,IAAI,SAACnG,GAG1B,OAFAqE,IAGElD,EAAA,OAAKjB,UAAU,wBACZ+D,EAA2BmC,UAAU,CAAEpG,oBAAAA,EAAqBqE,OAAAA,IAGnE,KAGA9D,EAAMyH,oBAAqD,IAA/BzH,EAAM8E,gBAAgBgB,QAClDlF,EAAA,WACEA,EAAA,OAAKC,MAAM,uGAAuGhB,IAAAA,WAAeC,MAAM,8CAI1IE,EAAM+F,kBACLnF,EAAA,OAAKC,MAAM,kCACTD,EAACW,IAAM,CAAC5B,UAAW,yBAA0B0C,SAAUrC,EAAMyH,mBAAoBjG,QAASxB,EAAMyH,mBAAoB3G,QAAS,WAAF,OAAQd,EAAMgG,UAAU,GAChJnG,IAAAA,WAAeC,MAAM,8DAK3B2H,GAAsB7G,EAAA,OAAKjB,UAAU,6BAA6B8H,KAM7E,EAGF,E", "sources": ["webpack://@wusong8899/bidding-rank-content/webpack/bootstrap", "webpack://@wusong8899/bidding-rank-content/webpack/runtime/compat get default export", "webpack://@wusong8899/bidding-rank-content/webpack/runtime/define property getters", "webpack://@wusong8899/bidding-rank-content/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['forum/app']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/IndexPage']\"", "webpack://@wusong8899/bidding-rank-content/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/bidding-rank-content/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/LinkButton']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/Page']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['common/helpers/listItems']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/LoadingIndicator']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['Component']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/Link']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/Modal']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['helpers/avatar']\"", "webpack://@wusong8899/bidding-rank-content/./src/forum/components/BiddingRankContentDetailsModal.js", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['utils/Stream']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['common/components/Alert']\"", "webpack://@wusong8899/bidding-rank-content/./src/forum/components/BiddingRankContentSubmitSuccessModal.js", "webpack://@wusong8899/bidding-rank-content/./src/forum/components/BiddingRankContentEditSuccessModal.js", "webpack://@wusong8899/bidding-rank-content/./src/forum/components/BiddingRankContentSubmitModal.js", "webpack://@wusong8899/bidding-rank-content/./src/forum/components/BiddingRankContentListItem.js", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/LogInModal']\"", "webpack://@wusong8899/bidding-rank-content/./src/forum/components/BiddingRankContentIndexPage.js", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['Model']\"", "webpack://@wusong8899/bidding-rank-content/./src/forum/model/BiddingRank.js", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['common/helpers/humanTime']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['common/helpers/textContrastClass']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['common/components/Link']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['common/utils/classList']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['tags/components/TagsPage']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['tags/utils/sortTags']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['tags/helpers/tagIcon']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['tags/helpers/tagLabel']\"", "webpack://@wusong8899/bidding-rank-content/./src/forum/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/IndexPage'];", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LinkButton'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Page'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/listItems'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Component'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Link'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Modal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['helpers/avatar'];", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport avatar from \"flarum/helpers/avatar\";\n\nexport default class BiddingRankContentDetailsModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.biddingRankListItem = this.attrs.biddingRankListItem;\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-details-title');\n  }\n\n  content() {\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const bidValue = this.biddingRankListItem.attribute(\"bid\");\n    const bidUser = this.biddingRankListItem.fromUser();\n    const bidURL = this.biddingRankListItem.url();\n    const bidContent = this.biddingRankListItem.content();\n    const bidText = moneyName.replace('[money]', bidValue);\n\n    // Use standard Flarum helpers as fallbacks for decoration-store components\n    const userAvatar = avatar(bidUser);\n\n    return (\n      <div className=\"Modal-body\">\n        <div style=\"min-height: 50px;\" onclick={() => this.openURL(bidURL) }>\n          <div style=\"float:left\">{userAvatar}</div>\n          <div style=\"padding-left: 70px;\">\n            <div>{bidContent}</div>\n            <div style=\"padding-top: 10px;\">{bidURL}</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  openURL(bidURL) {\n    window.open(bidURL, '_blank').focus();\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['utils/Stream'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Alert'];", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class BiddingRankContentSubmitSuccessModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-bidding-rank.forum.submit-content-success');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                loading: this.loading,\n                onclick: () => {\n                  location.reload();\n                },\n              },\n              app.translator.trans('wusong8899-bidding-rank.forum.ok')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class BiddingRankContentEditSuccessModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-bidding-rank.forum.edit-content-success');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                loading: this.loading,\n                onclick: () => {\n                  location.reload();\n                },\n              },\n              app.translator.trans('wusong8899-bidding-rank.forum.ok')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Stream from 'flarum/utils/Stream';\nimport Button from 'flarum/components/Button';\nimport Alert from 'flarum/common/components/Alert';\nimport BiddingRankContentSubmitSuccessModal from './BiddingRankContentSubmitSuccessModal';\nimport BiddingRankContentEditSuccessModal from './BiddingRankContentEditSuccessModal';\n\nexport default class BiddingRankContentSubmitModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.itemData = this.attrs.itemData;\n    this.settingType = \"add\";\n    this.loading = false;\n\n    if(this.itemData){\n      this.settingType = \"edit\";\n      this.bid = Stream(this.itemData.bid());\n      this.url = Stream(this.itemData.url());\n      this.bidContent = Stream(this.itemData.content());\n      this.bidTitle = Stream(this.itemData.title());\n      this.bidRaise = Stream(0);\n    }else{\n      this.bid = Stream(10000);\n      this.url = Stream(\"\");\n      this.bidTitle = Stream(\"\");\n      this.bidContent = Stream(\"\");\n    }\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans(this.settingType===\"add\"?'wusong8899-bidding-rank.forum.submit-content':'wusong8899-bidding-rank.forum.edit-content');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n\n              {this.settingType===\"add\" && (\n                <div>\n                  <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-bid')}</div>\n                  <input type=\"number\" min=\"10000\" disabled={this.loading} required className=\"FormControl\" bidi={this.bid} />\n                </div>\n              )}\n\n              {this.settingType===\"edit\" && (\n                <div>\n                  <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-bid')}: {this.bid()}</div>\n                  <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-bid-raise')}</div>\n                  <input type=\"number\" min=\"0\" disabled={this.loading} required className=\"FormControl\" bidi={this.bidRaise} />\n                </div>\n              )}\n\n              <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-title')}</div>\n              <input maxlength=\"6\" disabled={this.loading} required className=\"FormControl\" bidi={this.bidTitle} />\n\n              <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-url')}</div>\n              <input maxlength=\"500\" disabled={this.loading} required className=\"FormControl\" bidi={this.url} />\n\n              <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-content')}</div>\n              <textarea maxlength=\"500\" disabled={this.loading} required className=\"FormControl\" bidi={this.bidContent} />\n          </div>\n\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                type: 'submit',\n                loading: this.loading,\n              },\n              app.translator.trans('wusong8899-guaguale.forum.guaguale-purchase-confirm')\n            )}&nbsp;\n            {Button.component(\n              {\n                className: 'Button',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n                }\n              },\n              app.translator.trans('wusong8899-guaguale.forum.guaguale-purchase-cancel')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    const userMoney = app.session.user.attribute(\"money\");\n\n    if(this.settingType===\"add\" && userMoney<this.bid()){\n      app.alerts.show(Alert, {type: 'error'}, app.translator.trans('wusong8899-bidding-rank.forum.insufficient-fund'));\n      return;\n    }\n\n    if(this.settingType===\"edit\" && userMoney<this.bidRaise()){\n      app.alerts.show(Alert, {type: 'error'}, app.translator.trans('wusong8899-bidding-rank.forum.insufficient-fund'));\n      return;\n    }\n\n    this.loading = true;\n\n    if(this.settingType===\"edit\"){\n      this.itemData.save({\n        bidRaise:this.bidRaise(),\n        url:this.url(),\n        content:this.bidContent(),\n        title:this.bidTitle()\n      })\n      .then(\n        (response) => {\n          this.loading = false;\n          app.modal.show(BiddingRankContentEditSuccessModal);\n        }\n      )\n      .catch((e) => {\n        this.loading = false;\n      });\n    }else{\n      app.store\n      .createRecord(\"biddingRankList\")\n      .save({\n        bid:this.bid(),\n        url:this.url(),\n        content:this.bidContent(),\n        title:this.bidTitle()\n      })\n      .then(\n        (result) => {\n          app.store.pushPayload(result);\n          app.session.user.data.attributes.money-=this.bid();\n          this.loading = false;\n          app.modal.show(BiddingRankContentSubmitSuccessModal);\n        }\n      )\n      .catch((e) => {\n        this.loading = false;\n      });\n    }\n  }\n}\n", "import Component from \"flarum/Component\";\nimport Link from \"flarum/components/Link\";\nimport BiddingRankContentDetailsModal from './BiddingRankContentDetailsModal';\nimport BiddingRankContentSubmitModal from './BiddingRankContentSubmitModal';\n\nexport default class BiddingRankContentListItem extends Component {\n  view() {\n    const {biddingRankListItem,rankID} = this.attrs;\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const bidValue = biddingRankListItem.attribute(\"bid\");\n    const bidUser = biddingRankListItem.fromUser();\n    const bidURL = biddingRankListItem.url();\n    const bidContent = biddingRankListItem.content();\n    const bidTitle = biddingRankListItem.title();\n    const bidText = moneyName.replace('[money]', bidValue);\n\n    let userBidText = \"\";\n\n    if(app.session.user){\n      let userID = app.session.user.id();\n\n      if(userID==bidUser.id()){\n        userBidText = app.translator.trans('wusong8899-money-leaderboard.forum.user-own-bid');\n      }\n    }\n\n    let listItemStyle = rankID%2===1?\"background: #1b2132;\":\"\";\n\n    if(userBidText!==\"\"){\n      listItemStyle = \"background: royalblue;\";\n    }\n\n    return (\n      <div className=\"BiddingRankListItemContainer\" style={listItemStyle} onclick={() => this.itemClicked(biddingRankListItem,bidUser.id())}>\n        <div style=\"width: 40% !important;\" className=\"BiddingRankListHeaderContent\">\n          <div>{bidTitle}</div>\n        </div>\n        <div style=\"width: 60% !important;;text-align: right !important;\" className=\"BiddingRankListHeaderMoney\">\n          {bidValue}\n          <i className=\"u-sprites-money u-sprites-currency u-sprites-currency-cny\" style=\"margin-left: 4px;\"></i>\n        </div>\n      </div>\n    );\n  }\n\n  itemClicked(biddingRankListItem,bidUserID){\n    if(app.session.user){\n      let userID = app.session.user.id();\n\n      if(userID==bidUserID){\n        this.editContent(biddingRankListItem);\n      }else{\n        this.showDetails(biddingRankListItem);\n      }\n    }else{\n        this.showDetails(biddingRankListItem);\n    }\n  }\n\n  showDetails(biddingRankListItem){\n    app.modal.show(BiddingRankContentDetailsModal, {biddingRankListItem});\n  }\n\n  editContent(itemData){\n    app.modal.show(BiddingRankContentSubmitModal,{itemData});\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LogInModal'];", "import Page from 'flarum/components/Page';\nimport IndexPage from 'flarum/components/IndexPage';\nimport listItems from 'flarum/common/helpers/listItems';\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\nimport Button from 'flarum/components/Button';\n\nimport BiddingRankContentListItem from \"./BiddingRankContentListItem\";\nimport BiddingRankContentSubmitModal from './BiddingRankContentSubmitModal';\nimport LogInModal from \"flarum/components/LogInModal\";\n\nexport default class BiddingRankContentIndexPage extends Page {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loading = true;\n    this.moreResults = false;\n    this.biddingRankList = [];\n    this.hasBid = false;\n    this.biddingRankHistoryData = {};\n\n    this.loadResults();\n  }\n\n  view() {\n    let loading;\n    let rankID = 0;\n\n    if(this.loading){\n      loading = LoadingIndicator.component({size: \"large\"});\n    }\n\n    return (\n      <div className=\"MoneyLeaderboardPage\">\n        {IndexPage.prototype.hero()}\n\n        <div className=\"container\">\n          <div className=\"sideNavContainer\">\n            <nav className=\"IndexPage-nav sideNav\">\n              <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\n            </nav>\n\n            <div className=\"BiddingRangContainer\">\n              <div className=\"BiddingRankListTitle selectTitle\" style=\"height:40px;padding-top: 0px!important;;padding-bottom: 0px !important;\">\n                <div class=\"switch-btns\"><div class=\"btns-container\"><button style=\"width: 106px;\" type=\"button\" class=\"u-btn\"><img src=\"data:image/png;base64,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\" style=\"width: 15px;margin-right: 4px;vertical-align: -2px;\" /><div class=\"u-btn-text\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-content\")}</div></button><div id=\"buttonSelectedBackground\" class=\"selected-bg\" style=\"left: 0px; top: 0px; opacity: 1;\"></div></div></div>\n              </div>\n              <div style=\"display: flex;position: fixed;bottom: 50px;z-index: 1;width: 100%;\">\n                {!this.loading && this.hasBid===false && (\n                  <Button style=\"position: absolute;right: 25px;font-weight: normal !important;background: #e6601b !important;\" className={'Button Button--primary'} onclick={() => this.submitContent()}>\n                    {app.translator.trans(\"wusong8899-bidding-rank.forum.submit-content\")}\n                  </Button>\n                )}\n\n                {!this.loading && this.hasBid===true && (\n                  <Button style=\"position: absolute;right: 25px;font-weight: normal !important;background: #e6601b !important;\" className={'Button Button--primary'} onclick={() => this.editContent()}>\n                    {app.translator.trans(\"wusong8899-bidding-rank.forum.edit-content\")}\n                  </Button>\n                )}\n              </div>\n\n              <div class=\"BiddingRankListHeader\">\n                <div class=\"BiddingRankListHeaderContent\" style=\"padding-left: 8px;\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-list-title\")}</div>\n                <div class=\"BiddingRankListHeaderMoney\" style=\"margin-left: -5px;color:white !important\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-list-bid\")}</div>\n              </div>\n\n              <div className=\"BiddingRankList\">\n                {this.biddingRankList.map((biddingRankListItem) => {\n                  rankID++;\n\n                  return (\n                    <div className=\"BiddingRankListItems\">\n                      {BiddingRankContentListItem.component({ biddingRankListItem, rankID })}\n                    </div>\n                  );\n                })}\n              </div>\n\n              {!this.loading && this.biddingRankList.length===0 && (\n                <div>\n                  <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-bidding-rank.forum.list-empty\")}</div>\n                </div>\n              )}\n\n              {!loading && this.hasMoreResults() && (\n                <div style=\"text-align:center;padding:20px\">\n                  <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n                    {app.translator.trans('wusong8899-money-leaderboard.forum.leaderboard-load-more')}\n                  </Button>\n                </div>\n              )}\n\n              {loading && <div className=\"MoneyLeaderboard-loadMore\">{loading}</div>}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  submitContent(){\n    if(app.session.user){\n      app.modal.show(BiddingRankContentSubmitModal);\n    }else{\n      app.modal.show(LogInModal);\n    }\n  }\n\n  editContent(){\n    const itemData = this.biddingRankHistoryData;\n    app.modal.show(BiddingRankContentSubmitModal,{itemData});\n  }\n\n  hasMoreResults() {\n    return this.moreResults;\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.biddingRankList.length);\n  }\n\n  parseResults(results) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.biddingRankList, results);\n    this.loading = false;\n    m.redraw();\n\n    return results;\n\n    // return app.store\n    //   .find(\"biddingRankHistory\")\n    //   .catch(() => {})\n    //   .then((result) => {\n    //     this.hasBid = result.length>0;\n        \n    //     if(this.hasBid){\n    //       this.biddingRankHistoryData = result[0];\n    //     }\n\n    //     this.loading = false;\n    //     m.redraw();\n\n    //     return results;\n    //   });\n    \n  }\n\n  loadResults(offset = 0) {\n    const limit = 10;\n    return app.store\n      .find(\"biddingRankList\", {\n        page: {\n          offset,\n          limit\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Model'];", "import Model from \"flarum/Model\";\n\nexport default class BiddingRank extends Model {}\nObject.assign(BiddingRank.prototype, {\n  id: Model.attribute(\"id\"),\n  title: Model.attribute(\"title\"),\n  content: Model.attribute(\"content\"),\n  user_id: Model.attribute(\"user_id\"),\n  url: Model.attribute(\"url\"),\n  bid: Model.attribute(\"bid\"),\n  fromUser: Model.hasOne(\"fromUser\"),\n});\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/humanTime'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/textContrastClass'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Link'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/utils/classList'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['tags/components/TagsPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['tags/utils/sortTags'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['tags/helpers/tagIcon'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['tags/helpers/tagLabel'];", "import app from 'flarum/forum/app';\r\nimport { extend,override } from 'flarum/extend';\r\nimport addSidebarMenu from './addSidebarMenu';\r\nimport BiddingRankContentIndexPage from './components/BiddingRankContentIndexPage';\r\nimport BiddingRank from \"./model/BiddingRank\";\r\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\r\nimport Button from 'flarum/components/Button';\r\nimport IndexPage from 'flarum/components/IndexPage';\r\nimport listItems from 'flarum/common/helpers/listItems';\r\nimport humanTime from 'flarum/common/helpers/humanTime';\r\nimport textContrastClass from 'flarum/common/helpers/textContrastClass';\r\nimport Link from 'flarum/common/components/Link';\r\nimport classList from 'flarum/common/utils/classList';\r\n\r\nimport BiddingRankContentListItem from \"./components/BiddingRankContentListItem\";\r\nimport BiddingRankContentSubmitModal from './components/BiddingRankContentSubmitModal';\r\nimport LogInModal from \"flarum/components/LogInModal\";\r\n\r\nimport TagsPage from 'flarum/tags/components/TagsPage';\r\nimport sortTags from 'flarum/tags/utils/sortTags';\r\nimport tagIcon from 'flarum/tags/helpers/tagIcon';\r\nimport tagLabel from 'flarum/tags/helpers/tagLabel';\r\n\r\napp.initializers.add('wusong8899-client1-bidding-rank-content', () => {\r\n  app.routes['biddingRank'] = {\r\n    path: '/biddingRank',\r\n    component: BiddingRankContentIndexPage,\r\n  };\r\n\r\n  app.store.models.biddingRankList = BiddingRank;\r\n\r\n  let _this = TagsPage.prototype;\r\n  extend(TagsPage.prototype, 'oninit', function (vnode) {\r\n\r\n    _this.biddingRankLoading = true;\r\n    _this.moreResults = false;\r\n    _this.biddingRankList = [];\r\n    _this.hasBid = false;\r\n    _this.biddingRankHistoryData = {};\r\n\r\n    _this.loadResults();\r\n    \r\n  });\r\n\r\n  TagsPage.prototype.loadResults = function(offset = 0){\r\n    const limit = 10;\r\n    return app.store\r\n      .find(\"biddingRankList\", {\r\n        page: {\r\n          offset,\r\n          limit\r\n        },\r\n      })\r\n      .catch(() => {})\r\n      .then(_this.parseResults.bind(_this));\r\n  }\r\n\r\n  TagsPage.prototype.parseResults = function(results){\r\n    _this.moreResults = !!results.payload.links && !!results.payload.links.next;\r\n    [].push.apply(_this.biddingRankList, results);\r\n    _this.biddingRankLoading = false;\r\n    m.redraw();\r\n\r\n    return results;\r\n  }\r\n\r\n  TagsPage.prototype.submitContent = function(){\r\n    if(app.session.user){\r\n      app.modal.show(BiddingRankContentSubmitModal);\r\n    }else{\r\n      app.modal.show(LogInModal);\r\n    }\r\n  }\r\n\r\n  TagsPage.prototype.editContent = function(){\r\n    const itemData = _this.biddingRankHistoryData;\r\n    app.modal.show(BiddingRankContentSubmitModal,{itemData});\r\n  }\r\n\r\n  TagsPage.prototype.hasMoreResults = function() {\r\n    return _this.moreResults;\r\n  }\r\n\r\n  TagsPage.prototype.loadMore = function() {\r\n    _this.biddingRankLoading = true;\r\n    _this.loadResults(_this.biddingRankList.length);\r\n  }\r\n\r\n  override(TagsPage.prototype, 'view', function (vnode) {\r\n    let rankID = 0;\r\n    let biddingRankLoading;\r\n\r\n    if(_this.biddingRankLoading){\r\n      biddingRankLoading = LoadingIndicator.component({size: \"large\"});\r\n    }\r\n\r\n    if (this.loading) {\r\n      return <LoadingIndicator />;\r\n    }\r\n\r\n    const pinned = this.tags.filter((tag) => tag.position() !== null);\r\n    const cloud = this.tags.filter((tag) => tag.position() === null);\r\n\r\n    return (\r\n      <div className=\"TagsPage\">\r\n        {IndexPage.prototype.hero()}\r\n        <div className=\"container\">\r\n          <nav className=\"TagsPage-nav IndexPage-nav sideNav\">\r\n            <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\r\n          </nav>\r\n\r\n          <div className=\"TagsPage-content sideNavOffset\">\r\n            <ul className=\"TagTiles\">\r\n              {pinned.map((tag) => {\r\n                const lastPostedDiscussion = tag.lastPostedDiscussion();\r\n                const children = sortTags(tag.children() || []);\r\n\r\n\r\n                return (\r\n                  <li className={classList('TagTile', { colored: tag.color() }, textContrastClass(tag.color()))}>\r\n                    <Link className=\"TagTile-info\" href={app.route.tag(tag)}>\r\n                      {tag.icon() && tagIcon(tag, {}, { useColor: false })}\r\n                      <h3 className=\"TagTile-name\">{tag.name()}</h3>\r\n                      <p className=\"TagTile-description\">{tag.description()}</p>\r\n                      {children ? (\r\n                        <div className=\"TagTile-children\">\r\n                          {children.map((child) => [<Link href={app.route.tag(child)}>{child.name()}</Link>, ' '])}\r\n                        </div>\r\n                      ) : (\r\n                        ''\r\n                      )}\r\n                    </Link>\r\n                    {lastPostedDiscussion ? (\r\n                      <Link\r\n                        className=\"TagTile-lastPostedDiscussion\"\r\n                        href={app.route.discussion(lastPostedDiscussion, lastPostedDiscussion.lastPostNumber())}\r\n                      >\r\n                        <span className=\"TagTile-lastPostedDiscussion-title\">{lastPostedDiscussion.title()}</span>\r\n                        {humanTime(lastPostedDiscussion.lastPostedAt())}\r\n                      </Link>\r\n                    ) : (\r\n                      <span className=\"TagTile-lastPostedDiscussion\" />\r\n                    )}\r\n                  </li>\r\n                );\r\n              })}\r\n            </ul>\r\n\r\n            {cloud.length ? <div className=\"TagCloud\">{cloud.map((tag) => [tagLabel(tag, { link: true }), ' '])}</div> : ''}\r\n          </div>\r\n\r\n\r\n          <div className=\"BiddingRangContainer\">\r\n            <div className=\"BiddingRankListTitle selectTitle\" style=\"height:40px;padding-top: 0px!important;;padding-bottom: 0px !important;\">\r\n              <div class=\"switch-btns\"><div class=\"btns-container\"><button style=\"width: 106px;\" type=\"button\" class=\"u-btn\"><img src=\"data:image/png;base64,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\" style=\"width: 15px;margin-right: 4px;vertical-align: -2px;\" /><div class=\"u-btn-text\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-content\")}</div></button><div id=\"buttonSelectedBackground\" class=\"selected-bg\" style=\"left: 0px; top: 0px; opacity: 1;\"></div></div></div>\r\n            </div>\r\n            <div style=\"bottom: 50px;z-index: 1;width: 100%;height:10px;position: absolute;top: 13px;right: -25px;\">\r\n              {!_this.biddingRankLoading && _this.hasBid===false && (\r\n                <Button style=\"position: absolute;right: 25px;font-weight: normal !important;background: #e6601b !important;\" className={'Button Button--primary'} onclick={() => this.submitContent()}>\r\n                  {app.translator.trans(\"wusong8899-bidding-rank.forum.submit-content\")}\r\n                </Button>\r\n              )}\r\n\r\n              {!_this.biddingRankLoading && _this.hasBid===true && (\r\n                <Button style=\"position: absolute;right: 25px;font-weight: normal !important;background: #e6601b !important;\" className={'Button Button--primary'} onclick={() => this.editContent()}>\r\n                  {app.translator.trans(\"wusong8899-bidding-rank.forum.edit-content\")}\r\n                </Button>\r\n              )}\r\n            </div>\r\n\r\n            <div class=\"BiddingRankListHeader\">\r\n              <div class=\"BiddingRankListHeaderContent\" style=\"padding-left: 8px;\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-list-title\")}</div>\r\n              <div class=\"BiddingRankListHeaderMoney\" style=\"margin-left: -5px;color:white !important\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-list-bid\")}</div>\r\n            </div>\r\n\r\n            <div className=\"BiddingRankList\">\r\n              {_this.biddingRankList.map((biddingRankListItem) => {\r\n                rankID++;\r\n\r\n                return (\r\n                  <div className=\"BiddingRankListItems\">\r\n                    {BiddingRankContentListItem.component({ biddingRankListItem, rankID })}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n\r\n            {!_this.biddingRankLoading && _this.biddingRankList.length===0 && (\r\n              <div>\r\n                <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-bidding-rank.forum.list-empty\")}</div>\r\n              </div>\r\n            )}\r\n\r\n            {_this.hasMoreResults() && (\r\n              <div style=\"text-align:center;padding:20px\">\r\n                <Button className={'Button Button--primary'} disabled={_this.biddingRankLoading} loading={_this.biddingRankLoading} onclick={() => _this.loadMore()}>\r\n                  {app.translator.trans('wusong8899-money-leaderboard.forum.leaderboard-load-more')}\r\n                </Button>\r\n              </div>\r\n            )}\r\n\r\n            {biddingRankLoading && <div className=\"MoneyLeaderboard-loadMore\">{biddingRankLoading}</div>}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  });\r\n\r\n  // addSidebarMenu();\r\n});"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "flarum", "core", "compat", "_setPrototypeOf", "t", "e", "setPrototypeOf", "bind", "__proto__", "_inherits<PERSON><PERSON>e", "create", "constructor", "BiddingRankContentDetailsModal", "_Modal", "apply", "arguments", "_proto", "oninit", "vnode", "this", "biddingRankListItem", "attrs", "className", "title", "app", "trans", "content", "_this", "moneyName", "attribute", "bidValue", "bidUser", "fromUser", "bidURL", "url", "bidContent", "userAvatar", "replace", "avatar", "m", "style", "onclick", "openURL", "window", "open", "focus", "Modal", "isDismissibleViaBackdropClick", "isDismissibleViaCloseButton", "BiddingRankContentSubmitSuccessModal", "<PERSON><PERSON>", "loading", "location", "reload", "BiddingRankContentEditSuccessModal", "BiddingRankContentSubmitModal", "itemData", "settingType", "bid", "Stream", "bidTitle", "bidRaise", "type", "min", "disabled", "required", "bidi", "maxlength", "hide", "onsubmit", "_this2", "preventDefault", "userMoney", "user", "show", "<PERSON><PERSON>", "save", "then", "response", "createRecord", "result", "pushPayload", "data", "attributes", "money", "BiddingRankContentListItem", "_Component", "view", "_this$attrs", "rankID", "forum", "userBidText", "session", "id", "translator", "listItemStyle", "itemClicked", "bidUserID", "<PERSON><PERSON><PERSON><PERSON>", "showDetails", "modal", "Component", "BiddingRankContentIndexPage", "_Page", "moreResults", "biddingRankList", "hasBid", "biddingRankHistoryData", "loadResults", "LoadingIndicator", "size", "IndexPage", "hero", "listItems", "sidebarItems", "toArray", "class", "src", "submitContent", "map", "component", "length", "hasMoreResults", "loadMore", "LogInModal", "parseResults", "results", "payload", "links", "next", "push", "redraw", "offset", "store", "find", "page", "limit", "Page", "BiddingRank", "_Model", "Model", "assign", "user_id", "add", "path", "models", "TagsPage", "extend", "biddingRankLoading", "override", "pinned", "tags", "filter", "tag", "position", "cloud", "lastPostedDiscussion", "children", "sortTags", "classList", "colored", "color", "textContrastClass", "Link", "href", "icon", "tagIcon", "useColor", "name", "description", "child", "discussion", "lastPostNumber", "humanTime", "lastPostedAt", "tagLabel", "link"], "sourceRoot": ""}