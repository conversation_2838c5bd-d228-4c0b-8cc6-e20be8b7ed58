{"version": 3, "file": "admin.js", "mappings": ";MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,mBCAlF,SAASI,EAAgBC,EAAGC,GAC1B,OAAOF,EAAkBT,OAAOY,eAAiBZ,OAAOY,eAAeC,OAAS,SAAUH,EAAGC,GAC3F,OAAOD,EAAEI,UAAYH,EAAGD,CAC1B,EAAGD,EAAgBC,EAAGC,EACxB,CCHA,SAASI,EAAeL,EAAGX,GACzBW,EAAEJ,UAAYN,OAAOgB,OAAOjB,EAAEO,WAAYI,EAAEJ,UAAUW,YAAcP,EAAGE,EAAeF,EAAGX,EAC3F,CCHqCmB,OAAOC,KAAKC,OAAe,OCAhE,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAnBF,OAAOC,KAAKC,OAAO,+BAAxD,MCAM,EAA+BF,OAAOC,KAAKC,OAAO,qB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,oB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,gB,aCInCC,EAAkB,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAC,MAAA,KAAAC,YAAA,KAAAT,EAAAM,EAAAC,GAAA,IAAAG,EAAAJ,EAAAf,UAmGpC,OAnGoCmB,EAGrCC,OAAA,SAAOC,GACLL,EAAAhB,UAAMoB,OAAMlB,KAAC,KAAAmB,GACbC,KAAKC,mBAAqBD,KAAKE,MAAMD,mBACrCD,KAAKG,YAAc,MAEhBH,KAAKC,oBACND,KAAKG,YAAc,OACnBH,KAAKI,SAAWC,IAAOL,KAAKC,mBAAmBK,QAC/CN,KAAKO,QAAUF,IAAOL,KAAKC,mBAAmBO,WAE9CR,KAAKI,SAAWC,IAAO,IACvBL,KAAKO,QAAUF,IAAO,IAE1B,EAACR,EAEDY,UAAA,WACE,MAAO,eACT,EAACZ,EAEDa,MAAA,WACE,MAA0B,QAAnBV,KAAKG,YAAoBQ,IAAIC,WAAWC,MAAM,kDAAkDF,IAAIC,WAAWC,MAAM,kDAC9H,EAAChB,EAEDiB,QAAA,WAAU,IAAAC,EAAA,KACR,OACEC,EAAA,OAAKP,UAAU,cACbO,EAAA,OAAKP,UAAU,QACbO,EAAA,OAAKP,UAAU,aAAaQ,MAAM,uBAChCD,EAAA,WACEA,EAAA,OAAKE,MAAM,yBAAyBP,IAAIC,WAAWC,MAAM,oDACzDG,EAAA,SAAOG,UAAU,MAAMC,UAAQ,EAACX,UAAU,cAAcY,KAAMrB,KAAKI,WACnEY,EAAA,OAAKE,MAAM,yBAAyBP,IAAIC,WAAWC,MAAM,mDACzDG,EAAA,SAAOG,UAAU,MAAMC,UAAQ,EAACX,UAAU,cAAcY,KAAMrB,KAAKO,YAIvES,EAAA,OAAKP,UAAU,aAAaQ,MAAM,uBAC/BK,IAAAA,UACC,CACEb,UAAW,yBACXc,KAAM,SACNC,QAASxB,KAAKwB,SAEG,QAAnBxB,KAAKG,YAAoBQ,IAAIC,WAAWC,MAAM,yCAAyCF,IAAIC,WAAWC,MAAM,2CAC5G,IACDS,IAAAA,UACC,CACEb,UAAW,8BACXe,QAASxB,KAAKwB,QACdC,QAAS,WACPV,EAAKW,MACP,GAEFf,IAAIC,WAAWC,MAAM,0CAOjC,EAAChB,EAED8B,SAAA,SAAS5C,GAAG,IAAA6C,EAAA,KACV7C,EAAE8C,iBAEF7B,KAAKwB,SAAU,EAEO,SAAnBxB,KAAKG,YACNH,KAAKC,mBAAmB6B,KAAK,CACzBxB,KAAKN,KAAKI,WACV2B,IAAI/B,KAAKO,YAEZyB,KACC,kBAAMJ,EAAKF,MAAM,EACjB,SAACO,GACCL,EAAKJ,SAAU,EACfI,EAAKM,aAAaD,EACpB,GAGFtB,IAAIwB,MACDC,aAAa,kBACbN,KAAK,CACJxB,KAAKN,KAAKI,WACV2B,IAAI/B,KAAKO,YAEVyB,KACC,SAACK,GACCC,SAASC,QACX,GACD,MACM,SAACxD,GACN6C,EAAKJ,SAAU,EACfI,EAAKM,aAAaG,eACpB,EAEN,EAAC5C,CAAA,CAnGoC,CAAS+C,KAA3B/C,EACZgD,eAAgB,ECLzB,MAAM,EAA+BnD,OAAOC,KAAKC,OAAkB,U,aCG9CkD,EAAqB,SAAAhD,GAAA,SAAAgD,IAAA,OAAAhD,EAAAC,MAAA,KAAAC,YAAA,KAAAT,EAAAuD,EAAAhD,GAAA,IAAAG,EAAA6C,EAAAhE,UAyDvC,OAzDuCmB,EAGxCC,OAAA,SAAOC,GACLL,EAAAhB,UAAMoB,OAAMlB,KAAC,KAAAmB,GACbC,KAAKC,mBAAqBD,KAAKE,MAAMD,mBACrCD,KAAKwB,SAAU,CACjB,EAAC3B,EAEDY,UAAA,WACE,MAAO,cACT,EAACZ,EAEDa,MAAA,WACE,OAAOC,IAAIC,WAAWC,MAAM,iEAC9B,EAAChB,EAEDiB,QAAA,WAAU,IAAAC,EAAA,KAGR,OACEC,EAAA,OAAKP,UAAU,cACbO,EAAA,OAAKP,UAAU,aAAaQ,MAAM,uBAC/BK,IAAAA,UACC,CACEb,UAAW,yBACXc,KAAM,SACNC,QAASxB,KAAKwB,SAEhBb,IAAIC,WAAWC,MAAM,yCACrB,IACDS,IAAAA,UACC,CACEb,UAAW,8BACXe,QAASxB,KAAKwB,QACdC,QAAS,WACPV,EAAKW,MACP,GAEFf,IAAIC,WAAWC,MAAM,yCAK/B,EAAChB,EAED8B,SAAA,SAAS5C,GACPA,EAAE8C,iBAEF7B,KAAKwB,SAAU,EAEfxB,KAAKC,mBAAkB,SACtB+B,KACC,SAACC,GACCK,SAASC,QACX,EAEJ,EAACG,CAAA,CAzDuC,CAASF,KAA9BE,EACZD,eAAgB,ECDmC,IAEvCE,EAAkB,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAjD,MAAA,KAAAC,YAAA,KAAAT,EAAAwD,EAAAC,GAAA,IAAA/C,EAAA8C,EAAAjE,UAsCpC,OAtCoCmB,EACrCgD,KAAA,WAAO,IAAA9B,EAAA,KACEd,EAAsBD,KAAKE,MAA3BD,mBACD6C,EAAS7C,EAAmB8C,KAC5BC,EAAW/C,EAAmBK,OAC9B2C,EAAUhD,EAAmBO,QAGnC,OAFiBP,EAAmBiD,OAGlClC,EAAA,OAAKC,MAAM,6EACTD,EAAA,WACEA,EAAA,OAAKC,MAAM,qBACTD,EAACM,IAAM,CAACb,UAAW,yBAA0BgB,QAAS,WAAF,OAAQV,EAAKoC,SAASlD,EAAmB,GAC1FU,IAAIC,WAAWC,MAAM,oDACf,IAETG,EAACM,IAAM,CAACL,MAAM,+BAA+BR,UAAW,wBAAyBgB,QAAS,WAAF,OAAQV,EAAKqC,WAAWnD,EAAmB,GAChIU,IAAIC,WAAWC,MAAM,sDACf,KAETG,EAAA,SAAIL,IAAIC,WAAWC,MAAM,iDAAiD,MACzEiC,EAAO,MACR9B,EAAA,SAAIL,IAAIC,WAAWC,MAAM,mDAAmD,MAC3EmC,EAAS,MACVhC,EAAA,SAAIL,IAAIC,WAAWC,MAAM,kDAAkD,MAC1EoC,EAAQ,MAKnB,EAACpD,EAEDsD,SAAA,SAASlD,GACPU,IAAI0C,MAAMC,KAAK7D,EAAoB,CAACQ,mBAAAA,GACtC,EAACJ,EAEDuD,WAAA,SAAWnD,GACTU,IAAI0C,MAAMC,KAAKZ,EAAuB,CAACzC,mBAAAA,GACzC,EAAC0C,CAAA,CAtCoC,CAASY,KCChD,SAASC,EAAQC,EAAQC,GACvB,IAAIC,EAAOvF,OAAOuF,KAAKF,GACvB,GAAIrF,OAAOwF,sBAAuB,CAChC,IAAIC,EAAUzF,OAAOwF,sBAAsBH,GACvCC,IACFG,EAAUA,EAAQC,OAAO,SAAUC,GACjC,OAAO3F,OAAO4F,yBAAyBP,EAAQM,GAAKzF,UACtD,IAEFqF,EAAKM,KAAKtE,MAAMgE,EAAME,EACxB,CACA,OAAOF,CACT,CACA,SAASO,EAAeC,GACtB,IAAK,IAAIC,EAAI,EAAGA,EAAIxE,UAAUyE,OAAQD,IAAK,CACzC,IAAIE,EAAyB,MAAhB1E,UAAUwE,GAAaxE,UAAUwE,GAAK,CAAC,EAChDA,EAAI,EACNZ,EAAQpF,OAAOkG,IAAS,GAAMC,QAAQ,SAAUrG,GAC9CsG,EAAgBL,EAAQjG,EAAKoG,EAAOpG,GACtC,GACSE,OAAOqG,0BAChBrG,OAAOsG,iBAAiBP,EAAQ/F,OAAOqG,0BAA0BH,IAEjEd,EAAQpF,OAAOkG,IAASC,QAAQ,SAAUrG,GACxCE,OAAOC,eAAe8F,EAAQjG,EAAKE,OAAO4F,yBAAyBM,EAAQpG,GAC7E,EAEJ,CACA,OAAOiG,CACT,CACA,SAASQ,EAAQnG,GAYf,OAREmG,EADoB,mBAAXC,QAAoD,iBAApBA,OAAOC,SACtC,SAAUrG,GAClB,cAAcA,CAChB,EAEU,SAAUA,GAClB,OAAOA,GAAyB,mBAAXoG,QAAyBpG,EAAIa,cAAgBuF,QAAUpG,IAAQoG,OAAOlG,UAAY,gBAAkBF,CAC3H,EAEKmG,EAAQnG,EACjB,CACA,SAASgG,EAAgBhG,EAAKN,EAAK4G,GAWjC,OAVI5G,KAAOM,EACTJ,OAAOC,eAAeG,EAAKN,EAAK,CAC9B4G,MAAOA,EACPxG,YAAY,EACZyG,cAAc,EACdC,UAAU,IAGZxG,EAAIN,GAAO4G,EAENtG,CACT,CACA,SAASyG,IAYP,OAXAA,EAAW7G,OAAO8G,QAAU,SAAUf,GACpC,IAAK,IAAIC,EAAI,EAAGA,EAAIxE,UAAUyE,OAAQD,IAAK,CACzC,IAAIE,EAAS1E,UAAUwE,GACvB,IAAK,IAAIlG,KAAOoG,EACVlG,OAAOM,UAAUC,eAAeC,KAAK0F,EAAQpG,KAC/CiG,EAAOjG,GAAOoG,EAAOpG,GAG3B,CACA,OAAOiG,CACT,EACOc,EAAStF,MAAMK,KAAMJ,UAC9B,CAwDA,SAASuF,EAAUC,GACjB,GAAsB,oBAAXC,QAA0BA,OAAOC,UAC1C,QAAuBA,UAAUH,UAAUI,MAAMH,EAErD,CACA,IAAII,EAAaL,EAAU,yDACvBM,EAAON,EAAU,SACjBO,EAAUP,EAAU,YACpBQ,EAASR,EAAU,aAAeA,EAAU,aAAeA,EAAU,YACrES,EAAMT,EAAU,mBAChBU,EAAmBV,EAAU,YAAcA,EAAU,YAErDW,EAAc,CAChBC,SAAS,EACTC,SAAS,GAEX,SAASC,EAAGC,EAAIC,EAAOC,GACrBF,EAAGG,iBAAiBF,EAAOC,GAAKZ,GAAcM,EAChD,CACA,SAASQ,EAAIJ,EAAIC,EAAOC,GACtBF,EAAGK,oBAAoBJ,EAAOC,GAAKZ,GAAcM,EACnD,CACA,SAASU,EAAyBN,EAAeO,GAC/C,GAAKA,EAAL,CAEA,GADgB,MAAhBA,EAAS,KAAeA,EAAWA,EAASC,UAAU,IAClDR,EACF,IACE,GAAIA,EAAGM,QACL,OAAON,EAAGM,QAAQC,GACb,GAAIP,EAAGS,kBACZ,OAAOT,EAAGS,kBAAkBF,GACvB,GAAIP,EAAGU,sBACZ,OAAOV,EAAGU,sBAAsBH,EAEpC,CAAE,MAAOI,GACP,OAAO,CACT,CAEF,OAAO,CAfc,CAgBvB,CACA,SAASC,EAAgBZ,GACvB,OAAOA,EAAGa,MAAQb,IAAOc,UAAYd,EAAGa,KAAKE,SAAWf,EAAGa,KAAOb,EAAGgB,UACvE,CACA,SAASC,EAAyBjB,EAAeO,EAA0BW,EAAKC,GAC9E,GAAInB,EAAI,CACNkB,EAAMA,GAAOJ,SACb,EAAG,CACD,GAAgB,MAAZP,IAAqC,MAAhBA,EAAS,GAAaP,EAAGgB,aAAeE,GAAOZ,EAAQN,EAAIO,GAAYD,EAAQN,EAAIO,KAAcY,GAAcnB,IAAOkB,EAC7I,OAAOlB,EAET,GAAIA,IAAOkB,EAAK,KAElB,OAASlB,EAAKY,EAAgBZ,GAChC,CACA,OAAO,IACT,CACA,IA2SIoB,EA3SAC,EAAU,OACd,SAASC,EAAYtB,EAAI5F,EAAMmH,GAC7B,GAAIvB,GAAM5F,EACR,GAAI4F,EAAGwB,UACLxB,EAAGwB,UAAUD,EAAQ,MAAQ,UAAUnH,OAClC,CACL,IAAIG,GAAa,IAAMyF,EAAGzF,UAAY,KAAKkH,QAAQJ,EAAS,KAAKI,QAAQ,IAAMrH,EAAO,IAAK,KAC3F4F,EAAGzF,WAAaA,GAAagH,EAAQ,IAAMnH,EAAO,KAAKqH,QAAQJ,EAAS,IAC1E,CAEJ,CACA,SAASK,EAAI1B,EAAIzH,EAAMoJ,GACrB,IAAI5G,EAAQiF,GAAMA,EAAGjF,MACrB,GAAIA,EAAO,CACT,QAAY,IAAR4G,EAMF,OALIb,SAASc,aAAed,SAASc,YAAYC,iBAC/CF,EAAMb,SAASc,YAAYC,iBAAiB7B,EAAI,IACvCA,EAAG8B,eACZH,EAAM3B,EAAG8B,mBAEK,IAATvJ,EAAkBoJ,EAAMA,EAAIpJ,GAE7BA,KAAQwC,IAAsC,IAA5BxC,EAAKwJ,QAAQ,YACnCxJ,EAAO,WAAaA,GAEtBwC,EAAMxC,GAAQoJ,GAAsB,iBAARA,EAAmB,GAAK,KAExD,CACF,CACA,SAASK,EAAOhC,EAAIiC,GAClB,IAAIC,EAAoB,GACxB,GAAkB,iBAAPlC,EACTkC,EAAoBlC,OAEpB,EAAG,CACD,IAAImC,EAAYT,EAAI1B,EAAI,aACpBmC,GAA2B,SAAdA,IACfD,EAAoBC,EAAY,IAAMD,EAG1C,QAAUD,IAAajC,EAAKA,EAAGgB,aAEjC,IAAIoB,EAAWjD,OAAOkD,WAAalD,OAAOmD,iBAAmBnD,OAAOoD,WAAapD,OAAOqD,YAExF,OAAOJ,GAAY,IAAIA,EAASF,EAClC,CACA,SAASO,EAAKvB,EAAKwB,EAAS/D,GAC1B,GAAIuC,EAAK,CACP,IAAIyB,EAAOzB,EAAI0B,qBAAqBF,GAClCxE,EAAI,EACJ2E,EAAIF,EAAKxE,OACX,GAAIQ,EACF,KAAOT,EAAI2E,EAAG3E,IACZS,EAASgE,EAAKzE,GAAIA,GAGtB,OAAOyE,CACT,CACA,MAAO,EACT,CACA,SAASG,IAEP,OADuBhC,SAASiC,kBAIvBjC,SAASkC,eAEpB,CAWA,SAASC,EAAQjD,EAAIkD,EAA2BC,EAA2BC,EAAWC,GACpF,GAAKrD,EAAGsD,uBAAyBtD,IAAOb,OAAxC,CACA,IAAIoE,EAAQC,EAAKC,EAAMC,EAAQC,EAAOC,EAAQC,EAiB9C,GAhBI7D,IAAOb,QAAUa,EAAGgB,YAAchB,IAAO8C,KAE3CU,GADAD,EAASvD,EAAGsD,yBACCE,IACbC,EAAOF,EAAOE,KACdC,EAASH,EAAOG,OAChBC,EAAQJ,EAAOI,MACfC,EAASL,EAAOK,OAChBC,EAAQN,EAAOM,QAEfL,EAAM,EACNC,EAAO,EACPC,EAASvE,OAAO2E,YAChBH,EAAQxE,OAAO4E,WACfH,EAASzE,OAAO2E,YAChBD,EAAQ1E,OAAO4E,aAEZb,GAA6BC,IAA8BnD,IAAOb,SAErEkE,EAAYA,GAAarD,EAAGgB,YAIvB1B,GACH,GACE,GAAI+D,GAAaA,EAAUC,wBAA0D,SAAhC5B,EAAI2B,EAAW,cAA2BF,GAA4D,WAA/BzB,EAAI2B,EAAW,aAA2B,CACpK,IAAIW,EAAgBX,EAAUC,wBAG9BE,GAAOQ,EAAcR,IAAMS,SAASvC,EAAI2B,EAAW,qBACnDI,GAAQO,EAAcP,KAAOQ,SAASvC,EAAI2B,EAAW,sBACrDK,EAASF,EAAMD,EAAOK,OACtBD,EAAQF,EAAOF,EAAOM,MACtB,KACF,QAEOR,EAAYA,EAAUrC,YAGnC,GAAIoC,GAAapD,IAAOb,OAAQ,CAE9B,IAAI+E,EAAWlC,EAAOqB,GAAarD,GACjCmE,EAASD,GAAYA,EAASrM,EAC9BuM,EAASF,GAAYA,EAAStM,EAC5BsM,IAKFR,GAJAF,GAAOY,IAGPR,GAAUQ,GAEVT,GAJAF,GAAQU,IACRN,GAASM,GAKb,CACA,MAAO,CACLX,IAAKA,EACLC,KAAMA,EACNC,OAAQA,EACRC,MAAOA,EACPE,MAAOA,EACPD,OAAQA,EA5D4C,CA8DxD,CASA,SAASS,EAAerE,EAAIsE,EAAQC,GAKlC,IAJA,IAAIC,EAASC,EAA2BzE,GAAI,GAC1C0E,EAAYzB,EAAQjD,GAAIsE,GAGnBE,GAAQ,CACb,IAAIG,EAAgB1B,EAAQuB,GAAQD,GAOpC,KALmB,QAAfA,GAAuC,SAAfA,EAChBG,GAAaC,EAEbD,GAAaC,GAEX,OAAOH,EACrB,GAAIA,IAAW1B,IAA6B,MAC5C0B,EAASC,EAA2BD,GAAQ,EAC9C,CACA,OAAO,CACT,CAUA,SAASI,EAAS5E,EAAI6E,EAAUC,EAASC,GAIvC,IAHA,IAAIC,EAAe,EACjB9G,EAAI,EACJ+G,EAAWjF,EAAGiF,SACT/G,EAAI+G,EAAS9G,QAAQ,CAC1B,GAAkC,SAA9B8G,EAAS/G,GAAGnD,MAAMmK,SAAsBD,EAAS/G,KAAOiH,GAASC,QAAUL,GAAiBE,EAAS/G,KAAOiH,GAASE,UAAYpE,EAAQgE,EAAS/G,GAAI4G,EAAQQ,UAAWtF,GAAI,GAAQ,CACvL,GAAIgF,IAAiBH,EACnB,OAAOI,EAAS/G,GAElB8G,GACF,CACA9G,GACF,CACA,OAAO,IACT,CAQA,SAASqH,EAAUvF,EAAIO,GAErB,IADA,IAAIiF,EAAOxF,EAAGyF,iBACPD,IAASA,IAASL,GAASC,OAAkC,SAAzB1D,EAAI8D,EAAM,YAAyBjF,IAAaD,EAAQkF,EAAMjF,KACvGiF,EAAOA,EAAKE,uBAEd,OAAOF,GAAQ,IACjB,CASA,SAASG,EAAM3F,EAAIO,GACjB,IAAIoF,EAAQ,EACZ,IAAK3F,IAAOA,EAAGgB,WACb,OAAQ,EAIV,KAAOhB,EAAKA,EAAG0F,wBACqB,aAA9B1F,EAAG4F,SAASC,eAAgC7F,IAAOmF,GAASW,OAAWvF,IAAYD,EAAQN,EAAIO,IACjGoF,IAGJ,OAAOA,CACT,CAQA,SAASI,EAAwB/F,GAC/B,IAAIgG,EAAa,EACfC,EAAY,EACZC,EAAcpD,IAChB,GAAI9C,EACF,EAAG,CACD,IAAIkE,EAAWlC,EAAOhC,GACpBmE,EAASD,EAASrM,EAClBuM,EAASF,EAAStM,EACpBoO,GAAchG,EAAGmG,WAAahC,EAC9B8B,GAAajG,EAAGoG,UAAYhC,CAC9B,OAASpE,IAAOkG,IAAgBlG,EAAKA,EAAGgB,aAE1C,MAAO,CAACgF,EAAYC,EACtB,CAiBA,SAASxB,EAA2BzE,EAAIqG,GAEtC,IAAKrG,IAAOA,EAAGsD,sBAAuB,OAAOR,IAC7C,IAAIwD,EAAOtG,EACPuG,GAAU,EACd,GAEE,GAAID,EAAKE,YAAcF,EAAKG,aAAeH,EAAKI,aAAeJ,EAAKK,aAAc,CAChF,IAAIC,EAAUlF,EAAI4E,GAClB,GAAIA,EAAKE,YAAcF,EAAKG,cAAqC,QAArBG,EAAQC,WAA4C,UAArBD,EAAQC,YAA0BP,EAAKI,aAAeJ,EAAKK,eAAsC,QAArBC,EAAQE,WAA4C,UAArBF,EAAQE,WAAwB,CACpN,IAAKR,EAAKhD,uBAAyBgD,IAASxF,SAASiG,KAAM,OAAOjE,IAClE,GAAIyD,GAAWF,EAAa,OAAOC,EACnCC,GAAU,CACZ,CACF,QAEOD,EAAOA,EAAKtF,YACrB,OAAO8B,GACT,CAWA,SAASkE,EAAYC,EAAOC,GAC1B,OAAOC,KAAKC,MAAMH,EAAMzD,OAAS2D,KAAKC,MAAMF,EAAM1D,MAAQ2D,KAAKC,MAAMH,EAAMxD,QAAU0D,KAAKC,MAAMF,EAAMzD,OAAS0D,KAAKC,MAAMH,EAAMrD,UAAYuD,KAAKC,MAAMF,EAAMtD,SAAWuD,KAAKC,MAAMH,EAAMpD,SAAWsD,KAAKC,MAAMF,EAAMrD,MACvN,CAEA,SAASwD,EAASC,EAAUC,GAC1B,OAAO,WACL,IAAKnG,EAAkB,CACrB,IAAIoG,EAAO9N,UAES,IAAhB8N,EAAKrJ,OACPmJ,EAAS5O,KAFDoB,KAEa0N,EAAK,IAE1BF,EAAS7N,MAJDK,KAIc0N,GAExBpG,EAAmBqG,WAAW,WAC5BrG,OAAmB,CACrB,EAAGmG,EACL,CACF,CACF,CAKA,SAASG,EAAS1H,EAAI2H,EAAGC,GACvB5H,EAAGmG,YAAcwB,EACjB3H,EAAGoG,WAAawB,CAClB,CACA,SAAS9B,EAAM9F,GACb,IAAI6H,EAAU1I,OAAO0I,QACjBC,EAAI3I,OAAO4I,QAAU5I,OAAO6I,MAChC,OAAIH,GAAWA,EAAQI,IACdJ,EAAQI,IAAIjI,GAAIkI,WAAU,GACxBJ,EACFA,EAAE9H,GAAI8F,OAAM,GAAM,GAElB9F,EAAGkI,WAAU,EAExB,CAeA,SAASC,GAAkC9E,EAAWyB,EAASsD,GAC7D,IAAIC,EAAO,CAAC,EAcZ,OAbAC,MAAMC,KAAKlF,EAAU4B,UAAU5G,QAAQ,SAAUmK,GAC/C,IAAIC,EAAYC,EAAWC,EAAaC,EACxC,GAAK3H,EAAQuH,EAAO1D,EAAQQ,UAAWjC,GAAW,KAAUmF,EAAMK,UAAYL,IAAUJ,EAAxF,CACA,IAAIU,EAAY7F,EAAQuF,GACxBH,EAAK5E,KAAO0D,KAAK4B,IAAiC,QAA5BN,EAAaJ,EAAK5E,YAAiC,IAAfgF,EAAwBA,EAAaO,IAAUF,EAAUrF,MACnH4E,EAAK7E,IAAM2D,KAAK4B,IAA+B,QAA1BL,EAAYL,EAAK7E,WAA+B,IAAdkF,EAAuBA,EAAYM,IAAUF,EAAUtF,KAC9G6E,EAAK1E,MAAQwD,KAAK8B,IAAmC,QAA9BN,EAAcN,EAAK1E,aAAmC,IAAhBgF,EAAyBA,GAAc,IAAWG,EAAUnF,OACzH0E,EAAK3E,OAASyD,KAAK8B,IAAqC,QAAhCL,EAAeP,EAAK3E,cAAqC,IAAjBkF,EAA0BA,GAAe,IAAWE,EAAUpF,OALvB,CAMzG,GACA2E,EAAKxE,MAAQwE,EAAK1E,MAAQ0E,EAAK5E,KAC/B4E,EAAKzE,OAASyE,EAAK3E,OAAS2E,EAAK7E,IACjC6E,EAAKV,EAAIU,EAAK5E,KACd4E,EAAKT,EAAIS,EAAK7E,IACP6E,CACT,CACA,IAAIa,GAAU,YAAa,IAAIC,MAAOC,UA2ItC,IAAIC,GAAU,GACVC,GAAW,CACbC,qBAAqB,GAEnBC,GAAgB,CAClBC,MAAO,SAAeC,GAEpB,IAAK,IAAIC,KAAUL,GACbA,GAAS7Q,eAAekR,MAAaA,KAAUD,KACjDA,EAAOC,GAAUL,GAASK,IAG9BN,GAAQhL,QAAQ,SAAUuL,GACxB,GAAIA,EAAEC,aAAeH,EAAOG,WAC1B,KAAM,iCAAiCC,OAAOJ,EAAOG,WAAY,kBAErE,GACAR,GAAQtL,KAAK2L,EACf,EACAK,YAAa,SAAqBC,EAAWC,EAAUC,GACrD,IAAIrP,EAAQf,KACZA,KAAKqQ,eAAgB,EACrBD,EAAIE,OAAS,WACXvP,EAAMsP,eAAgB,CACxB,EACA,IAAIE,EAAkBL,EAAY,SAClCX,GAAQhL,QAAQ,SAAUqL,GACnBO,EAASP,EAAOG,cAEjBI,EAASP,EAAOG,YAAYQ,IAC9BJ,EAASP,EAAOG,YAAYQ,GAAiBrM,EAAe,CAC1DiM,SAAUA,GACTC,IAKDD,EAASnF,QAAQ4E,EAAOG,aAAeI,EAASP,EAAOG,YAAYG,IACrEC,EAASP,EAAOG,YAAYG,GAAWhM,EAAe,CACpDiM,SAAUA,GACTC,IAEP,EACF,EACAI,kBAAmB,SAA2BL,EAAUjK,EAAIsJ,EAAUxE,GAYpE,IAAK,IAAI6E,KAXTN,GAAQhL,QAAQ,SAAUqL,GACxB,IAAIG,EAAaH,EAAOG,WACxB,GAAKI,EAASnF,QAAQ+E,IAAgBH,EAAOH,oBAA7C,CACA,IAAIgB,EAAc,IAAIb,EAAOO,EAAUjK,EAAIiK,EAASnF,SACpDyF,EAAYN,SAAWA,EACvBM,EAAYzF,QAAUmF,EAASnF,QAC/BmF,EAASJ,GAAcU,EAGvBxL,EAASuK,EAAUiB,EAAYjB,SAPyC,CAQ1E,GACmBW,EAASnF,QAC1B,GAAKmF,EAASnF,QAAQrM,eAAekR,GAArC,CACA,IAAIa,EAAW1Q,KAAK2Q,aAAaR,EAAUN,EAAQM,EAASnF,QAAQ6E,SAC5C,IAAba,IACTP,EAASnF,QAAQ6E,GAAUa,EAHyB,CAM1D,EACAE,mBAAoB,SAA4BtQ,EAAM6P,GACpD,IAAIU,EAAkB,CAAC,EAKvB,OAJAtB,GAAQhL,QAAQ,SAAUqL,GACc,mBAA3BA,EAAOiB,iBAClB5L,EAAS4L,EAAiBjB,EAAOiB,gBAAgBjS,KAAKuR,EAASP,EAAOG,YAAazP,GACrF,GACOuQ,CACT,EACAF,aAAc,SAAsBR,EAAU7P,EAAMwE,GAClD,IAAIgM,EAUJ,OATAvB,GAAQhL,QAAQ,SAAUqL,GAEnBO,EAASP,EAAOG,aAGjBH,EAAOmB,iBAA2D,mBAAjCnB,EAAOmB,gBAAgBzQ,KAC1DwQ,EAAgBlB,EAAOmB,gBAAgBzQ,GAAM1B,KAAKuR,EAASP,EAAOG,YAAajL,GAEnF,GACOgM,CACT,GAuDF,IAAIE,GAAY,CAAC,OACbf,GAAc,SAAqBC,EAAWC,GAChD,IAAIc,EAAOrR,UAAUyE,OAAS,QAAsB6M,IAAjBtR,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAC9EuR,EAAgBF,EAAKb,IACrBgB,EA3uBJ,SAAkC9M,EAAQ+M,GACxC,GAAc,MAAV/M,EAAgB,MAAO,CAAC,EAC5B,IACIpG,EAAKkG,EADLD,EAdN,SAAuCG,EAAQ+M,GAC7C,GAAc,MAAV/M,EAAgB,MAAO,CAAC,EAC5B,IAEIpG,EAAKkG,EAFLD,EAAS,CAAC,EACVmN,EAAalT,OAAOuF,KAAKW,GAE7B,IAAKF,EAAI,EAAGA,EAAIkN,EAAWjN,OAAQD,IACjClG,EAAMoT,EAAWlN,GACbiN,EAASpJ,QAAQ/J,IAAQ,IAC7BiG,EAAOjG,GAAOoG,EAAOpG,IAEvB,OAAOiG,CACT,CAGeoN,CAA8BjN,EAAQ+M,GAEnD,GAAIjT,OAAOwF,sBAAuB,CAChC,IAAI4N,EAAmBpT,OAAOwF,sBAAsBU,GACpD,IAAKF,EAAI,EAAGA,EAAIoN,EAAiBnN,OAAQD,IACvClG,EAAMsT,EAAiBpN,GACnBiN,EAASpJ,QAAQ/J,IAAQ,GACxBE,OAAOM,UAAU+S,qBAAqB7S,KAAK0F,EAAQpG,KACxDiG,EAAOjG,GAAOoG,EAAOpG,GAEzB,CACA,OAAOiG,CACT,CA6tBWuN,CAAyBT,EAAMD,IACxCtB,GAAcO,YAAYhR,KAAKoM,GAA/BqE,CAAyCQ,EAAWC,EAAUjM,EAAe,CAC3EyN,OAAQA,GACRC,SAAUA,GACVtD,QAASA,GACTuD,OAAQA,GACRC,OAAQA,GACRC,WAAYA,GACZC,QAASA,GACTC,YAAaA,GACbC,YAAaC,GACbC,YAAaA,GACbC,eAAgBhH,GAASiH,OACzBnB,cAAeA,EACfoB,SAAUA,GACVC,kBAAmBA,GACnBC,SAAUA,GACVC,kBAAmBA,GACnBC,mBAAoBC,GACpBC,qBAAsBC,GACtBC,eAAgB,WACdd,IAAc,CAChB,EACAe,cAAe,WACbf,IAAc,CAChB,EACAgB,sBAAuB,SAA+B3S,GACpD4S,GAAe,CACb/C,SAAUA,EACV7P,KAAMA,EACN6Q,cAAeA,GAEnB,GACCC,GACL,EACA,SAAS8B,GAAeC,IA3FxB,SAAuBlC,GACrB,IAAId,EAAWc,EAAKd,SAClB0B,EAASZ,EAAKY,OACdvR,EAAO2Q,EAAK3Q,KACZ8S,EAAWnC,EAAKmC,SAChBpB,EAAUf,EAAKe,QACfqB,EAAOpC,EAAKoC,KACZC,EAASrC,EAAKqC,OACdf,EAAWtB,EAAKsB,SAChBE,EAAWxB,EAAKwB,SAChBD,EAAoBvB,EAAKuB,kBACzBE,EAAoBzB,EAAKyB,kBACzBvB,EAAgBF,EAAKE,cACrBiB,EAAcnB,EAAKmB,YACnBmB,EAAuBtC,EAAKsC,qBAE9B,GADApD,EAAWA,GAAY0B,GAAUA,EAAOzC,IACxC,CACA,IAAIgB,EACFpF,EAAUmF,EAASnF,QACnBwI,EAAS,KAAOlT,EAAKmT,OAAO,GAAG1H,cAAgBzL,EAAKoT,OAAO,IAEzDrO,OAAOsO,aAAgBnO,GAAeC,GAMxC2K,EAAMpJ,SAAS4M,YAAY,UACvBC,UAAUvT,GAAM,GAAM,GAN1B8P,EAAM,IAAIuD,YAAYrT,EAAM,CAC1BwT,SAAS,EACTC,YAAY,IAMhB3D,EAAI4D,GAAKX,GAAQxB,EACjBzB,EAAI3B,KAAO6E,GAAUzB,EACrBzB,EAAI6D,KAAOb,GAAYvB,EACvBzB,EAAIpE,MAAQgG,EACZ5B,EAAImC,SAAWA,EACfnC,EAAIqC,SAAWA,EACfrC,EAAIoC,kBAAoBA,EACxBpC,EAAIsC,kBAAoBA,EACxBtC,EAAIe,cAAgBA,EACpBf,EAAI8D,SAAW9B,EAAcA,EAAY+B,iBAAcjD,EACvD,IAAIkD,EAAqBlQ,EAAeA,EAAe,CAAC,EAAGqP,GAAuB7D,GAAckB,mBAAmBtQ,EAAM6P,IACzH,IAAK,IAAIN,KAAUuE,EACjBhE,EAAIP,GAAUuE,EAAmBvE,GAE/BgC,GACFA,EAAOwC,cAAcjE,GAEnBpF,EAAQwI,IACVxI,EAAQwI,GAAQ5U,KAAKuR,EAAUC,EAhCZ,CAkCvB,CA0CEiE,CAAcnQ,EAAe,CAC3BkO,YAAaA,GACbJ,QAASA,GACToB,SAAUzB,GACVE,OAAQA,GACRU,SAAUA,GACVC,kBAAmBA,GACnBC,SAAUA,GACVC,kBAAmBA,IAClBS,GACL,CACA,IAAIxB,GACFC,GACAtD,GACAuD,GACAC,GACAC,GACAC,GACAC,GACAM,GACAE,GACAD,GACAE,GACA4B,GACAlC,GAIAmC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAzC,GACA0C,GACAC,GAGAC,GAEAC,GAhBAC,IAAsB,EACtBC,IAAkB,EAClBC,GAAY,GAUZC,IAAwB,EACxBC,IAAyB,EAIzBC,GAAmC,GAGnCC,IAAU,EACVC,GAAoB,GAGlBC,GAAqC,oBAAbzO,SAC1B0O,GAA0B9P,EAC1B+P,GAAmBlQ,GAAQD,EAAa,WAAa,QAErDoQ,GAAmBH,KAAmB5P,IAAqBD,GAAO,cAAeoB,SAAS6O,cAAc,OACxGC,GAA0B,WACxB,GAAKL,GAAL,CAEA,GAAIjQ,EACF,OAAO,EAET,IAAIU,EAAKc,SAAS6O,cAAc,KAEhC,OADA3P,EAAGjF,MAAM8U,QAAU,sBACe,SAA3B7P,EAAGjF,MAAM+U,aAPW,CAQ7B,CAT0B,GAU1BC,GAAmB,SAA0B/P,EAAI8E,GAC/C,IAAIkL,EAAQtO,EAAI1B,GACdiQ,EAAUhM,SAAS+L,EAAMnM,OAASI,SAAS+L,EAAME,aAAejM,SAAS+L,EAAMG,cAAgBlM,SAAS+L,EAAMI,iBAAmBnM,SAAS+L,EAAMK,kBAChJC,EAAS1L,EAAS5E,EAAI,EAAG8E,GACzByL,EAAS3L,EAAS5E,EAAI,EAAG8E,GACzB0L,EAAgBF,GAAU5O,EAAI4O,GAC9BG,EAAiBF,GAAU7O,EAAI6O,GAC/BG,EAAkBF,GAAiBvM,SAASuM,EAAcG,YAAc1M,SAASuM,EAAcI,aAAe3N,EAAQqN,GAAQzM,MAC9HgN,EAAmBJ,GAAkBxM,SAASwM,EAAeE,YAAc1M,SAASwM,EAAeG,aAAe3N,EAAQsN,GAAQ1M,MACpI,GAAsB,SAAlBmM,EAAM9K,QACR,MAA+B,WAAxB8K,EAAMc,eAAsD,mBAAxBd,EAAMc,cAAqC,WAAa,aAErG,GAAsB,SAAlBd,EAAM9K,QACR,OAAO8K,EAAMe,oBAAoBC,MAAM,KAAK7S,QAAU,EAAI,WAAa,aAEzE,GAAImS,GAAUE,EAAqB,OAAgC,SAA3BA,EAAqB,MAAc,CACzE,IAAIS,EAAgD,SAA3BT,EAAqB,MAAe,OAAS,QACtE,OAAOD,GAAoC,SAAzBE,EAAeS,OAAoBT,EAAeS,QAAUD,EAAmC,aAAb,UACtG,CACA,OAAOX,IAAqC,UAA1BE,EAActL,SAAiD,SAA1BsL,EAActL,SAAgD,UAA1BsL,EAActL,SAAiD,SAA1BsL,EAActL,SAAsBwL,GAAmBT,GAAuC,SAA5BD,EAAMP,KAAgCc,GAAsC,SAA5BP,EAAMP,KAAgCiB,EAAkBG,EAAmBZ,GAAW,WAAa,YACvV,EA8BAkB,GAAgB,SAAuBrM,GACrC,SAASsM,EAAKxS,EAAOyS,GACnB,OAAO,SAAUvD,EAAIvF,EAAMkD,EAAQvB,GACjC,IAAIoH,EAAYxD,EAAGhJ,QAAQyM,MAAMnX,MAAQmO,EAAKzD,QAAQyM,MAAMnX,MAAQ0T,EAAGhJ,QAAQyM,MAAMnX,OAASmO,EAAKzD,QAAQyM,MAAMnX,KACjH,GAAa,MAATwE,IAAkByS,GAAQC,GAG5B,OAAO,EACF,GAAa,MAAT1S,IAA2B,IAAVA,EAC1B,OAAO,EACF,GAAIyS,GAAkB,UAAVzS,EACjB,OAAOA,EACF,GAAqB,mBAAVA,EAChB,OAAOwS,EAAKxS,EAAMkP,EAAIvF,EAAMkD,EAAQvB,GAAMmH,EAAnCD,CAAyCtD,EAAIvF,EAAMkD,EAAQvB,GAElE,IAAIsH,GAAcH,EAAOvD,EAAKvF,GAAMzD,QAAQyM,MAAMnX,KAClD,OAAiB,IAAVwE,GAAmC,iBAAVA,GAAsBA,IAAU4S,GAAc5S,EAAM6S,MAAQ7S,EAAMmD,QAAQyP,IAAe,CAE7H,CACF,CACA,IAAID,EAAQ,CAAC,EACTG,EAAgB5M,EAAQyM,MACvBG,GAA2C,UAA1BjT,EAAQiT,KAC5BA,EAAgB,CACdtX,KAAMsX,IAGVH,EAAMnX,KAAOsX,EAActX,KAC3BmX,EAAMI,UAAYP,EAAKM,EAAcL,MAAM,GAC3CE,EAAMK,SAAWR,EAAKM,EAAcG,KACpCN,EAAMO,YAAcJ,EAAcI,YAClChN,EAAQyM,MAAQA,CAClB,EACA7E,GAAsB,YACfkD,IAA2BxH,IAC9B1G,EAAI0G,GAAS,UAAW,OAE5B,EACAwE,GAAwB,YACjBgD,IAA2BxH,IAC9B1G,EAAI0G,GAAS,UAAW,GAE5B,EAGEmH,KAAmB5P,GACrBmB,SAASX,iBAAiB,QAAS,SAAU+J,GAC3C,GAAI8E,GAKF,OAJA9E,EAAIvO,iBACJuO,EAAI6H,iBAAmB7H,EAAI6H,kBAC3B7H,EAAI8H,0BAA4B9H,EAAI8H,2BACpChD,IAAkB,GACX,CAEX,GAAG,GAEL,IAAIiD,GAAgC,SAAuC/H,GACzE,GAAIuB,GAAQ,CACVvB,EAAMA,EAAIgI,QAAUhI,EAAIgI,QAAQ,GAAKhI,EACrC,IAAIiI,GAzE6DxK,EAyEvBuC,EAAIkI,QAzEsBxK,EAyEbsC,EAAImI,QAvE3DpD,GAAUqD,KAAK,SAAUrI,GACvB,IAAIsI,EAAYtI,EAASf,IAASpE,QAAQ0N,qBAC1C,GAAKD,IAAahN,EAAU0E,GAA5B,CACA,IAAI5B,EAAOpF,EAAQgH,GACjBwI,EAAqB9K,GAAKU,EAAK5E,KAAO8O,GAAa5K,GAAKU,EAAK1E,MAAQ4O,EACrEG,EAAmB9K,GAAKS,EAAK7E,IAAM+O,GAAa3K,GAAKS,EAAK3E,OAAS6O,EACrE,OAAIE,GAAsBC,EACjBC,EAAM1I,OADf,CAJ6C,CAO/C,GACO0I,GA8DP,GAAIR,EAAS,CAEX,IAAIlS,EAAQ,CAAC,EACb,IAAK,IAAI/B,KAAKgM,EACRA,EAAIzR,eAAeyF,KACrB+B,EAAM/B,GAAKgM,EAAIhM,IAGnB+B,EAAMhC,OAASgC,EAAM0L,OAASwG,EAC9BlS,EAAMtE,oBAAiB,EACvBsE,EAAM8R,qBAAkB,EACxBI,EAAQjJ,IAAS0J,YAAY3S,EAC/B,CACF,CAvF8B,IAAqC0H,EAAGC,EAChE+K,CAuFR,EACIE,GAAwB,SAA+B3I,GACrDuB,IACFA,GAAOzK,WAAWkI,IAAS4J,iBAAiB5I,EAAIjM,OAEpD,EAOA,SAASkH,GAASnF,EAAI8E,GACpB,IAAM9E,IAAMA,EAAGe,UAA4B,IAAhBf,EAAGe,SAC5B,KAAM,8CAA8C+I,OAAO,CAAC,EAAEiJ,SAASra,KAAKsH,IAE9ElG,KAAKkG,GAAKA,EACVlG,KAAKgL,QAAUA,EAAU/F,EAAS,CAAC,EAAG+F,GAGtC9E,EAAGkJ,IAAWpP,KACd,IA3gBEkZ,EADEC,EA4gBA3J,EAAW,CACbiI,MAAO,KACPvU,MAAM,EACNkW,UAAU,EACVjX,MAAO,KACPkX,OAAQ,KACR7N,UAAW,WAAW8N,KAAKpT,EAAG4F,UAAY,MAAQ,KAClDyN,cAAe,EAEfC,YAAY,EAEZC,sBAAuB,KAEvBC,mBAAmB,EACnBC,UAAW,WACT,OAAO1D,GAAiB/P,EAAIlG,KAAKgL,QACnC,EACA4O,WAAY,iBACZC,YAAa,kBACbC,UAAW,gBACXC,OAAQ,SACRjW,OAAQ,KACRkW,iBAAiB,EACjBC,UAAW,EACXC,OAAQ,KACRC,QAAS,SAAiBC,EAAczI,GACtCyI,EAAaD,QAAQ,OAAQxI,EAAO0I,YACtC,EACAC,YAAY,EACZC,gBAAgB,EAChBC,WAAY,UACZC,MAAO,EACPC,kBAAkB,EAClBC,qBAAsBC,OAAOzQ,SAAWyQ,OAASvV,QAAQ8E,SAAS9E,OAAOwV,iBAAkB,KAAO,EAClGC,eAAe,EACfC,cAAe,oBACfC,gBAAgB,EAChBC,kBAAmB,EACnBC,eAAgB,CACdrN,EAAG,EACHC,EAAG,GAGLqN,gBAA4C,IAA5B9P,GAAS8P,gBAA4B,iBAAkB9V,UAAYM,GAAUC,GAC7F8S,qBAAsB,GAKxB,IAAK,IAAIpY,KAHToP,GAAcc,kBAAkBxQ,KAAMkG,EAAIsJ,GAGzBA,IACblP,KAAQ0K,KAAaA,EAAQ1K,GAAQkP,EAASlP,IAKlD,IAAK,IAAI8F,KAHTiR,GAAcrM,GAGChL,KACQ,MAAjBoG,EAAGqN,OAAO,IAAkC,mBAAbzT,KAAKoG,KACtCpG,KAAKoG,GAAMpG,KAAKoG,GAAInH,KAAKe,OAK7BA,KAAKob,iBAAkBpQ,EAAQ8P,eAAwBlF,GACnD5V,KAAKob,kBAEPpb,KAAKgL,QAAQ2P,oBAAsB,GAIjC3P,EAAQmQ,eACVlV,EAAGC,EAAI,cAAelG,KAAKqb,cAE3BpV,EAAGC,EAAI,YAAalG,KAAKqb,aACzBpV,EAAGC,EAAI,aAAclG,KAAKqb,cAExBrb,KAAKob,kBACPnV,EAAGC,EAAI,WAAYlG,MACnBiG,EAAGC,EAAI,YAAalG,OAEtBmV,GAAUlR,KAAKjE,KAAKkG,IAGpB8E,EAAQ7I,OAAS6I,EAAQ7I,MAAM5D,KAAOyB,KAAKkD,KAAK8H,EAAQ7I,MAAM5D,IAAIyB,OAAS,IAG3EiF,EAASjF,MAjmBLmZ,EAAkB,GAEf,CACLmC,sBAAuB,WACrBnC,EAAkB,GACbnZ,KAAKgL,QAAQiP,WACH,GAAGsB,MAAM3c,KAAKoB,KAAKkG,GAAGiF,UAC5B5G,QAAQ,SAAUmK,GACzB,GAA8B,SAA1B9G,EAAI8G,EAAO,YAAyBA,IAAUrD,GAASC,MAA3D,CACA6N,EAAgBlV,KAAK,CACnBE,OAAQuK,EACRH,KAAMpF,EAAQuF,KAEhB,IAAI8M,EAAWtX,EAAe,CAAC,EAAGiV,EAAgBA,EAAgB9U,OAAS,GAAGkK,MAG9E,GAAIG,EAAM+M,sBAAuB,CAC/B,IAAIC,EAAcxT,EAAOwG,GAAO,GAC5BgN,IACFF,EAAS9R,KAAOgS,EAAYC,EAC5BH,EAAS7R,MAAQ+R,EAAY3c,EAEjC,CACA2P,EAAM8M,SAAWA,CAfuD,CAgB1E,EACF,EACAI,kBAAmB,SAA2BnU,GAC5C0R,EAAgBlV,KAAKwD,EACvB,EACAoU,qBAAsB,SAA8B1X,GAClDgV,EAAgB2C,OA7ItB,SAAuBC,EAAKvd,GAC1B,IAAK,IAAI4F,KAAK2X,EACZ,GAAKA,EAAIpd,eAAeyF,GACxB,IAAK,IAAIlG,KAAOM,EACd,GAAIA,EAAIG,eAAeT,IAAQM,EAAIN,KAAS6d,EAAI3X,GAAGlG,GAAM,OAAO0c,OAAOxW,GAG3E,OAAQ,CACV,CAqI6B4X,CAAc7C,EAAiB,CACpDhV,OAAQA,IACN,EACN,EACA8X,WAAY,SAAoBzO,GAC9B,IAAIzM,EAAQf,KACZ,IAAKA,KAAKgL,QAAQiP,UAGhB,OAFAiC,aAAahD,QACW,mBAAb1L,GAAyBA,KAGtC,IAAI2O,GAAY,EACdC,EAAgB,EAClBjD,EAAgB5U,QAAQ,SAAUkD,GAChC,IAAI4U,EAAO,EACTlY,EAASsD,EAAMtD,OACfqX,EAAWrX,EAAOqX,SAClBc,EAASnT,EAAQhF,GACjBoY,EAAepY,EAAOoY,aACtBC,EAAarY,EAAOqY,WACpBC,EAAgBhV,EAAM8G,KACtBmO,EAAexU,EAAO/D,GAAQ,GAC5BuY,IAEFJ,EAAO5S,KAAOgT,EAAaf,EAC3BW,EAAO3S,MAAQ+S,EAAa3d,GAE9BoF,EAAOmY,OAASA,EACZnY,EAAOsX,uBAELvO,EAAYqP,EAAcD,KAAYpP,EAAYsO,EAAUc,KAE/DG,EAAc/S,IAAM4S,EAAO5S,MAAQ+S,EAAc9S,KAAO2S,EAAO3S,SAAW6R,EAAS9R,IAAM4S,EAAO5S,MAAQ8R,EAAS7R,KAAO2S,EAAO3S,QAE9H0S,EAoEZ,SAA2BI,EAAejB,EAAUc,EAAQtR,GAC1D,OAAOqC,KAAKsP,KAAKtP,KAAKuP,IAAIpB,EAAS9R,IAAM+S,EAAc/S,IAAK,GAAK2D,KAAKuP,IAAIpB,EAAS7R,KAAO8S,EAAc9S,KAAM,IAAM0D,KAAKsP,KAAKtP,KAAKuP,IAAIpB,EAAS9R,IAAM4S,EAAO5S,IAAK,GAAK2D,KAAKuP,IAAIpB,EAAS7R,KAAO2S,EAAO3S,KAAM,IAAMqB,EAAQiP,SAC7N,CAtEmB4C,CAAkBJ,EAAeF,EAAcC,EAAYzb,EAAMiK,UAKvEkC,EAAYoP,EAAQd,KACvBrX,EAAOoY,aAAef,EACtBrX,EAAOqY,WAAaF,EACfD,IACHA,EAAOtb,EAAMiK,QAAQiP,WAEvBlZ,EAAM+b,QAAQ3Y,EAAQsY,EAAeH,EAAQD,IAE3CA,IACFF,GAAY,EACZC,EAAgB/O,KAAK8B,IAAIiN,EAAeC,GACxCH,aAAa/X,EAAO4Y,qBACpB5Y,EAAO4Y,oBAAsBpP,WAAW,WACtCxJ,EAAOiY,cAAgB,EACvBjY,EAAOoY,aAAe,KACtBpY,EAAOqX,SAAW,KAClBrX,EAAOqY,WAAa,KACpBrY,EAAOsX,sBAAwB,IACjC,EAAGY,GACHlY,EAAOsX,sBAAwBY,EAEnC,GACAH,aAAahD,GACRiD,EAGHjD,EAAsBvL,WAAW,WACP,mBAAbH,GAAyBA,GACtC,EAAG4O,GAJqB,mBAAb5O,GAAyBA,IAMtC2L,EAAkB,EACpB,EACA2D,QAAS,SAAiB3Y,EAAQ6Y,EAAaV,EAAQW,GACrD,GAAIA,EAAU,CACZrV,EAAIzD,EAAQ,aAAc,IAC1ByD,EAAIzD,EAAQ,YAAa,IACzB,IAAIiG,EAAWlC,EAAOlI,KAAKkG,IACzBmE,EAASD,GAAYA,EAASrM,EAC9BuM,EAASF,GAAYA,EAAStM,EAC9Bof,GAAcF,EAAYrT,KAAO2S,EAAO3S,OAASU,GAAU,GAC3D8S,GAAcH,EAAYtT,IAAM4S,EAAO5S,MAAQY,GAAU,GAC3DnG,EAAOiZ,aAAeF,EACtB/Y,EAAOkZ,aAAeF,EACtBvV,EAAIzD,EAAQ,YAAa,eAAiB+Y,EAAa,MAAQC,EAAa,SAC5End,KAAKsd,gBAgBb,SAAiBnZ,GACf,OAAOA,EAAOoZ,WAChB,CAlB+BC,CAAQrZ,GAE/ByD,EAAIzD,EAAQ,aAAc,aAAe8Y,EAAW,MAAQjd,KAAKgL,QAAQkP,OAAS,IAAMla,KAAKgL,QAAQkP,OAAS,KAC9GtS,EAAIzD,EAAQ,YAAa,sBACE,iBAApBA,EAAO4K,UAAyBmN,aAAa/X,EAAO4K,UAC3D5K,EAAO4K,SAAWpB,WAAW,WAC3B/F,EAAIzD,EAAQ,aAAc,IAC1ByD,EAAIzD,EAAQ,YAAa,IACzBA,EAAO4K,UAAW,EAClB5K,EAAOiZ,YAAa,EACpBjZ,EAAOkZ,YAAa,CACtB,EAAGJ,EACL,CACF,IAoeJ,CAiiCA,SAASQ,GAAQnK,EAAQD,EAAM1B,EAAQ+L,EAAUtK,EAAUuK,EAAYxM,EAAeyM,GACpF,IAAIxN,EAGFyN,EAFA1N,EAAWmD,EAAOlE,IAClB0O,EAAW3N,EAASnF,QAAQ+S,OAwB9B,OArBI1Y,OAAOsO,aAAgBnO,GAAeC,GAMxC2K,EAAMpJ,SAAS4M,YAAY,UACvBC,UAAU,QAAQ,GAAM,GAN5BzD,EAAM,IAAIuD,YAAY,OAAQ,CAC5BG,SAAS,EACTC,YAAY,IAMhB3D,EAAI4D,GAAKX,EACTjD,EAAI3B,KAAO6E,EACXlD,EAAI7E,QAAUoG,EACdvB,EAAI4N,YAAcN,EAClBtN,EAAI6N,QAAU7K,GAAYC,EAC1BjD,EAAI8N,YAAcP,GAAcxU,EAAQkK,GACxCjD,EAAIwN,gBAAkBA,EACtBxN,EAAIe,cAAgBA,EACpBmC,EAAOe,cAAcjE,GACjB0N,IACFD,EAASC,EAASlf,KAAKuR,EAAUC,EAAKe,IAEjC0M,CACT,CACA,SAASM,GAAkBjY,GACzBA,EAAGsF,WAAY,CACjB,CACA,SAAS4S,KACP7I,IAAU,CACZ,CA0EA,SAAS8I,GAAYnY,GAInB,IAHA,IAAIoY,EAAMpY,EAAG0C,QAAU1C,EAAGzF,UAAYyF,EAAGqY,IAAMrY,EAAGsY,KAAOtY,EAAGmU,YAC1DjW,EAAIka,EAAIja,OACRoa,EAAM,EACDra,KACLqa,GAAOH,EAAII,WAAWta,GAExB,OAAOqa,EAAIxF,SAAS,GACtB,CAUA,SAAS0F,GAAUvY,GACjB,OAAOuH,WAAWvH,EAAI,EACxB,CACA,SAASwY,GAAgB7b,GACvB,OAAOmZ,aAAanZ,EACtB,CAnqCAsI,GAAS3M,UAA4C,CACnDW,YAAagM,GACb2N,iBAAkB,SAA0B7U,GACrCnE,KAAKkG,GAAG2Y,SAAS1a,IAAWA,IAAWnE,KAAKkG,KAC/C2O,GAAa,KAEjB,EACAiK,cAAe,SAAuB1O,EAAKjM,GACzC,MAAyC,mBAA3BnE,KAAKgL,QAAQ2O,UAA2B3Z,KAAKgL,QAAQ2O,UAAU/a,KAAKoB,KAAMoQ,EAAKjM,EAAQwN,IAAU3R,KAAKgL,QAAQ2O,SAC9H,EACA0B,YAAa,SAA6CjL,GACxD,GAAKA,EAAI2D,WAAT,CACA,IAAIhT,EAAQf,KACVkG,EAAKlG,KAAKkG,GACV8E,EAAUhL,KAAKgL,QACfgP,EAAkBhP,EAAQgP,gBAC1BzY,EAAO6O,EAAI7O,KACXwd,EAAQ3O,EAAIgI,SAAWhI,EAAIgI,QAAQ,IAAMhI,EAAI4O,aAAmC,UAApB5O,EAAI4O,aAA2B5O,EAC3FjM,GAAU4a,GAAS3O,GAAKjM,OACxB8a,EAAiB7O,EAAIjM,OAAO+a,aAAe9O,EAAI+O,MAAQ/O,EAAI+O,KAAK,IAAM/O,EAAIgP,cAAgBhP,EAAIgP,eAAe,KAAOjb,EACpHL,EAASkH,EAAQlH,OAInB,GA6nCJ,SAAgCub,GAC9B7J,GAAkBnR,OAAS,EAG3B,IAFA,IAAIib,EAASD,EAAKvW,qBAAqB,SACnCyW,EAAMD,EAAOjb,OACVkb,KAAO,CACZ,IAAIrZ,EAAKoZ,EAAOC,GAChBrZ,EAAGsZ,SAAWhK,GAAkBvR,KAAKiC,EACvC,CACF,CAxoCIuZ,CAAuBvZ,IAGnByL,MAGA,wBAAwB2H,KAAK/X,IAAwB,IAAf6O,EAAIsP,QAAgB1U,EAAQoO,YAKlE6F,EAAeU,oBAKd3f,KAAKob,kBAAmBzV,IAAUxB,GAA2C,WAAjCA,EAAOyE,QAAQmD,mBAGhE5H,EAASgD,EAAQhD,EAAQ6G,EAAQQ,UAAWtF,GAAI,KAClC/B,EAAO4K,UAGjBgD,KAAe5N,GAAnB,CAUA,GAJAoO,GAAW1G,EAAM1H,GACjBqO,GAAoB3G,EAAM1H,EAAQ6G,EAAQQ,WAGpB,mBAAX1H,GACT,GAAIA,EAAOlF,KAAKoB,KAAMoQ,EAAKjM,EAAQnE,MAajC,OAZAkT,GAAe,CACb/C,SAAUpP,EACV8Q,OAAQoN,EACR3e,KAAM,SACN8S,SAAUjP,EACVkP,KAAMnN,EACNoN,OAAQpN,IAEV+J,GAAY,SAAUlP,EAAO,CAC3BqP,IAAKA,SAEP4J,GAAmB5J,EAAIvO,uBAGpB,GAAIiC,IACTA,EAASA,EAAOoT,MAAM,KAAKsB,KAAK,SAAUoH,GAExC,GADAA,EAAWzY,EAAQ8X,EAAgBW,EAASC,OAAQ3Z,GAAI,GAatD,OAXAgN,GAAe,CACb/C,SAAUpP,EACV8Q,OAAQ+N,EACRtf,KAAM,SACN8S,SAAUjP,EACVmP,OAAQpN,EACRmN,KAAMnN,IAER+J,GAAY,SAAUlP,EAAO,CAC3BqP,IAAKA,KAEA,CAEX,IAGE,YADA4J,GAAmB5J,EAAIvO,kBAIvBmJ,EAAQqO,SAAWlS,EAAQ8X,EAAgBjU,EAAQqO,OAAQnT,GAAI,IAKnElG,KAAK8f,kBAAkB1P,EAAK2O,EAAO5a,EAnDnC,CApC2B,CAwF7B,EACA2b,kBAAmB,SAAwC1P,EAAiB2O,EAAyB5a,GACnG,IAIE4b,EAJEhf,EAAQf,KACVkG,EAAKnF,EAAMmF,GACX8E,EAAUjK,EAAMiK,QAChBgV,EAAgB9Z,EAAG8Z,cAErB,GAAI7b,IAAWwN,IAAUxN,EAAO+C,aAAehB,EAAI,CACjD,IAAIwX,EAAWvU,EAAQhF,GA0EvB,GAzEA0N,GAAS3L,EAET0L,IADAD,GAASxN,GACS+C,WAClB4K,GAASH,GAAOsO,YAChBlO,GAAa5N,EACbmQ,GAActJ,EAAQyM,MACtBpM,GAASE,QAAUoG,GACnB4C,GAAS,CACPpQ,OAAQwN,GACR2G,SAAUyG,GAAS3O,GAAKkI,QACxBC,SAAUwG,GAAS3O,GAAKmI,SAE1B5D,GAAkBJ,GAAO+D,QAAUoF,EAAS/T,KAC5CiL,GAAiBL,GAAOgE,QAAUmF,EAAShU,IAC3C1J,KAAKkgB,QAAUnB,GAAS3O,GAAKkI,QAC7BtY,KAAKmgB,QAAUpB,GAAS3O,GAAKmI,QAC7B5G,GAAO1Q,MAAM,eAAiB,MAC9B8e,EAAc,WACZ9P,GAAY,aAAclP,EAAO,CAC/BqP,IAAKA,IAEH/E,GAASgF,cACXtP,EAAMqf,WAKRrf,EAAMsf,6BACD3a,GAAW3E,EAAMqa,kBACpBzJ,GAAOnG,WAAY,GAIrBzK,EAAMuf,kBAAkBlQ,EAAK2O,GAG7B7L,GAAe,CACb/C,SAAUpP,EACVT,KAAM,SACN6Q,cAAef,IAIjB5I,EAAYmK,GAAQ3G,EAAQ6O,aAAa,GAC3C,EAGA7O,EAAQ+O,OAAO7C,MAAM,KAAK3S,QAAQ,SAAUqb,GAC1CjX,EAAKgJ,GAAQiO,EAASC,OAAQ1B,GAChC,GACAlY,EAAG+Z,EAAe,WAAY7H,IAC9BlS,EAAG+Z,EAAe,YAAa7H,IAC/BlS,EAAG+Z,EAAe,YAAa7H,IAC3BnN,EAAQmQ,gBACVlV,EAAG+Z,EAAe,YAAajf,EAAMqf,UAEpCpgB,KAAKob,iBAAmBnV,EAAG+Z,EAAe,gBAAiBjf,EAAMqf,WAElEna,EAAG+Z,EAAe,UAAWjf,EAAMqf,SACnCna,EAAG+Z,EAAe,WAAYjf,EAAMqf,SACpCna,EAAG+Z,EAAe,cAAejf,EAAMqf,UAIrC1a,GAAW1F,KAAKob,kBAClBpb,KAAKgL,QAAQ2P,oBAAsB,EACnChJ,GAAOnG,WAAY,GAErByE,GAAY,aAAcjQ,KAAM,CAC9BoQ,IAAKA,KAIHpF,EAAQyP,OAAWzP,EAAQ0P,mBAAoBqE,GAAY/e,KAAKob,kBAAqB3V,GAAQD,GAqB/Fua,QArB6G,CAC7G,GAAI1U,GAASgF,cAEX,YADArQ,KAAKogB,UAMHpV,EAAQmQ,gBACVlV,EAAG+Z,EAAe,YAAajf,EAAMwf,qBACrCta,EAAG+Z,EAAe,gBAAiBjf,EAAMwf,uBAEzCta,EAAG+Z,EAAe,UAAWjf,EAAMwf,qBACnCta,EAAG+Z,EAAe,WAAYjf,EAAMwf,qBACpCta,EAAG+Z,EAAe,cAAejf,EAAMwf,sBAEzCta,EAAG+Z,EAAe,YAAajf,EAAMyf,8BACrCva,EAAG+Z,EAAe,YAAajf,EAAMyf,8BACrCxV,EAAQmQ,gBAAkBlV,EAAG+Z,EAAe,cAAejf,EAAMyf,8BACjEzf,EAAM0f,gBAAkB9S,WAAWoS,EAAa/U,EAAQyP,MAC1D,CAGF,CACF,EACA+F,6BAA8B,SAAsEzhB,GAClG,IAAIggB,EAAQhgB,EAAEqZ,QAAUrZ,EAAEqZ,QAAQ,GAAKrZ,EACnCsO,KAAK8B,IAAI9B,KAAKqT,IAAI3B,EAAMzG,QAAUtY,KAAKkgB,QAAS7S,KAAKqT,IAAI3B,EAAMxG,QAAUvY,KAAKmgB,UAAY9S,KAAKsT,MAAM3gB,KAAKgL,QAAQ2P,qBAAuB3a,KAAKob,iBAAmB/V,OAAOwV,kBAAoB,KAC9L7a,KAAKugB,qBAET,EACAA,oBAAqB,WACnB5O,IAAUwM,GAAkBxM,IAC5BuK,aAAalc,KAAKygB,iBAClBzgB,KAAKqgB,2BACP,EACAA,0BAA2B,WACzB,IAAIL,EAAgBhgB,KAAKkG,GAAG8Z,cAC5B1Z,EAAI0Z,EAAe,UAAWhgB,KAAKugB,qBACnCja,EAAI0Z,EAAe,WAAYhgB,KAAKugB,qBACpCja,EAAI0Z,EAAe,cAAehgB,KAAKugB,qBACvCja,EAAI0Z,EAAe,YAAahgB,KAAKugB,qBACrCja,EAAI0Z,EAAe,gBAAiBhgB,KAAKugB,qBACzCja,EAAI0Z,EAAe,YAAahgB,KAAKwgB,8BACrCla,EAAI0Z,EAAe,YAAahgB,KAAKwgB,8BACrCla,EAAI0Z,EAAe,cAAehgB,KAAKwgB,6BACzC,EACAF,kBAAmB,SAAwClQ,EAAiB2O,GAC1EA,EAAQA,GAA4B,SAAnB3O,EAAI4O,aAA0B5O,GAC1CpQ,KAAKob,iBAAmB2D,EACvB/e,KAAKgL,QAAQmQ,eACflV,EAAGe,SAAU,cAAehH,KAAK4gB,cAEjC3a,EAAGe,SADM+X,EACI,YAEA,YAFa/e,KAAK4gB,eAKjC3a,EAAG0L,GAAQ,UAAW3R,MACtBiG,EAAG4L,GAAQ,YAAa7R,KAAK6gB,eAE/B,IACM7Z,SAAS8Z,UACXnC,GAAU,WACR3X,SAAS8Z,UAAUC,OACrB,GAEA1b,OAAO2b,eAAeC,iBAE1B,CAAE,MAAOC,GAAM,CACjB,EACAC,aAAc,SAAsBC,EAAUhR,GAE5C,GADA6E,IAAsB,EAClBpD,IAAUF,GAAQ,CACpB1B,GAAY,cAAejQ,KAAM,CAC/BoQ,IAAKA,IAEHpQ,KAAKob,iBACPnV,EAAGe,SAAU,WAAY+R,IAE3B,IAAI/N,EAAUhL,KAAKgL,SAGlBoW,GAAY5Z,EAAYmK,GAAQ3G,EAAQ8O,WAAW,GACpDtS,EAAYmK,GAAQ3G,EAAQ4O,YAAY,GACxCvO,GAASiH,OAAStS,KAClBohB,GAAYphB,KAAKqhB,eAGjBnO,GAAe,CACb/C,SAAUnQ,KACVM,KAAM,QACN6Q,cAAef,GAEnB,MACEpQ,KAAKshB,UAET,EACAC,iBAAkB,WAChB,GAAI/M,GAAU,CACZxU,KAAKkgB,OAAS1L,GAAS8D,QACvBtY,KAAKmgB,OAAS3L,GAAS+D,QACvB3F,KAGA,IAFA,IAAIzO,EAAS6C,SAASwa,iBAAiBhN,GAAS8D,QAAS9D,GAAS+D,SAC9D7N,EAASvG,EACNA,GAAUA,EAAO+a,aACtB/a,EAASA,EAAO+a,WAAWsC,iBAAiBhN,GAAS8D,QAAS9D,GAAS+D,YACxD7N,GACfA,EAASvG,EAGX,GADAwN,GAAOzK,WAAWkI,IAAS4J,iBAAiB7U,GACxCuG,EACF,EAAG,CACD,GAAIA,EAAO0E,KAEE1E,EAAO0E,IAAS0J,YAAY,CACrCR,QAAS9D,GAAS8D,QAClBC,QAAS/D,GAAS+D,QAClBpU,OAAQA,EACR0N,OAAQnH,MAEO1K,KAAKgL,QAAQuP,eAC5B,MAGJpW,EAASuG,CACX,OAC8BA,EAAS5D,EAAgB4D,IAEzDoI,IACF,CACF,EACA8N,aAAc,SAAsCxQ,GAClD,GAAImE,GAAQ,CACV,IAAIvJ,EAAUhL,KAAKgL,QACjBiQ,EAAoBjQ,EAAQiQ,kBAC5BC,EAAiBlQ,EAAQkQ,eACzB6D,EAAQ3O,EAAIgI,QAAUhI,EAAIgI,QAAQ,GAAKhI,EACvCqR,EAAcnT,IAAWpG,EAAOoG,IAAS,GACzCjE,EAASiE,IAAWmT,GAAeA,EAAY1jB,EAC/CuM,EAASgE,IAAWmT,GAAeA,EAAY3jB,EAC/C4jB,EAAuBhM,IAA2BV,IAAuB/I,EAAwB+I,IACjG2M,GAAM5C,EAAMzG,QAAU/D,GAAO+D,QAAU4C,EAAerN,IAAMxD,GAAU,IAAMqX,EAAuBA,EAAqB,GAAKpM,GAAiC,GAAK,IAAMjL,GAAU,GACnLuX,GAAM7C,EAAMxG,QAAUhE,GAAOgE,QAAU2C,EAAepN,IAAMxD,GAAU,IAAMoX,EAAuBA,EAAqB,GAAKpM,GAAiC,GAAK,IAAMhL,GAAU,GAGrL,IAAKe,GAASiH,SAAW2C,GAAqB,CAC5C,GAAIgG,GAAqB5N,KAAK8B,IAAI9B,KAAKqT,IAAI3B,EAAMzG,QAAUtY,KAAKkgB,QAAS7S,KAAKqT,IAAI3B,EAAMxG,QAAUvY,KAAKmgB,SAAWlF,EAChH,OAEFjb,KAAK6gB,aAAazQ,GAAK,EACzB,CACA,GAAI9B,GAAS,CACPmT,GACFA,EAAY1iB,GAAK4iB,GAAMlN,IAAU,GACjCgN,EAAY9F,GAAKiG,GAAMlN,IAAU,IAEjC+M,EAAc,CACZ1jB,EAAG,EACH8jB,EAAG,EACHC,EAAG,EACHhkB,EAAG,EACHiB,EAAG4iB,EACHhG,EAAGiG,GAGP,IAAIG,EAAY,UAAU/R,OAAOyR,EAAY1jB,EAAG,KAAKiS,OAAOyR,EAAYI,EAAG,KAAK7R,OAAOyR,EAAYK,EAAG,KAAK9R,OAAOyR,EAAY3jB,EAAG,KAAKkS,OAAOyR,EAAY1iB,EAAG,KAAKiR,OAAOyR,EAAY9F,EAAG,KACvL/T,EAAI0G,GAAS,kBAAmByT,GAChCna,EAAI0G,GAAS,eAAgByT,GAC7Bna,EAAI0G,GAAS,cAAeyT,GAC5Bna,EAAI0G,GAAS,YAAayT,GAC1BtN,GAASkN,EACTjN,GAASkN,EACTpN,GAAWuK,CACb,CACA3O,EAAI2D,YAAc3D,EAAIvO,gBACxB,CACF,EACAwf,aAAc,WAGZ,IAAK/S,GAAS,CACZ,IAAI/E,EAAYvJ,KAAKgL,QAAQgQ,eAAiBhU,SAASiG,KAAO4E,GAC5DtD,EAAOpF,EAAQwI,IAAQ,EAAM+D,IAAyB,EAAMnM,GAC5DyB,EAAUhL,KAAKgL,QAGjB,GAAI0K,GAAyB,CAG3B,IADAV,GAAsBzL,EAC0B,WAAzC3B,EAAIoN,GAAqB,aAAsE,SAA1CpN,EAAIoN,GAAqB,cAA2BA,KAAwBhO,UACtIgO,GAAsBA,GAAoB9N,WAExC8N,KAAwBhO,SAASiG,MAAQ+H,KAAwBhO,SAASkC,iBACxE8L,KAAwBhO,WAAUgO,GAAsBhM,KAC5DuF,EAAK7E,KAAOsL,GAAoB1I,UAChCiC,EAAK5E,MAAQqL,GAAoB3I,YAEjC2I,GAAsBhM,IAExBsM,GAAmCrJ,EAAwB+I,GAC7D,CAEAxN,EADA8G,GAAUqD,GAAOvD,WAAU,GACNpD,EAAQ4O,YAAY,GACzCpS,EAAY8G,GAAStD,EAAQ+P,eAAe,GAC5CvT,EAAY8G,GAAStD,EAAQ8O,WAAW,GACxClS,EAAI0G,GAAS,aAAc,IAC3B1G,EAAI0G,GAAS,YAAa,IAC1B1G,EAAI0G,GAAS,aAAc,cAC3B1G,EAAI0G,GAAS,SAAU,GACvB1G,EAAI0G,GAAS,MAAOC,EAAK7E,KACzB9B,EAAI0G,GAAS,OAAQC,EAAK5E,MAC1B/B,EAAI0G,GAAS,QAASC,EAAKxE,OAC3BnC,EAAI0G,GAAS,SAAUC,EAAKzE,QAC5BlC,EAAI0G,GAAS,UAAW,OACxB1G,EAAI0G,GAAS,WAAYoH,GAA0B,WAAa,SAChE9N,EAAI0G,GAAS,SAAU,UACvB1G,EAAI0G,GAAS,gBAAiB,QAC9BjD,GAASC,MAAQgD,GACjB/E,EAAUyY,YAAY1T,IAGtB1G,EAAI0G,GAAS,mBAAoBqG,GAAkBxK,SAASmE,GAAQrN,MAAM8I,OAAS,IAAM,KAAO6K,GAAiBzK,SAASmE,GAAQrN,MAAM6I,QAAU,IAAM,IAC1J,CACF,EACA+W,aAAc,SAAiCzQ,EAAiBgR,GAC9D,IAAIrgB,EAAQf,KACRoa,EAAehK,EAAIgK,aACnBpP,EAAUjK,EAAMiK,QACpBiF,GAAY,YAAajQ,KAAM,CAC7BoQ,IAAKA,IAEH/E,GAASgF,cACXrQ,KAAKogB,WAGPnQ,GAAY,aAAcjQ,MACrBqL,GAASgF,iBACZ2B,GAAUhG,EAAM2F,KACRsQ,gBAAgB,MACxBjQ,GAAQxG,WAAY,EACpBwG,GAAQ/Q,MAAM,eAAiB,GAC/BjB,KAAKkiB,aACL1a,EAAYwK,GAAShS,KAAKgL,QAAQ6O,aAAa,GAC/CxO,GAASW,MAAQgG,IAInBjR,EAAMohB,QAAUxD,GAAU,WACxB1O,GAAY,QAASlP,GACjBsK,GAASgF,gBACRtP,EAAMiK,QAAQ0O,mBACjB7H,GAAOuQ,aAAapQ,GAASL,IAE/B5Q,EAAMmhB,aACNhP,GAAe,CACb/C,SAAUpP,EACVT,KAAM,UAEV,IACC8gB,GAAY5Z,EAAYmK,GAAQ3G,EAAQ8O,WAAW,GAGhDsH,GACFlM,IAAkB,EAClBnU,EAAMshB,QAAUC,YAAYvhB,EAAMwgB,iBAAkB,MAGpDjb,EAAIU,SAAU,UAAWjG,EAAMqf,SAC/B9Z,EAAIU,SAAU,WAAYjG,EAAMqf,SAChC9Z,EAAIU,SAAU,cAAejG,EAAMqf,SAC/BhG,IACFA,EAAamI,cAAgB,OAC7BvX,EAAQmP,SAAWnP,EAAQmP,QAAQvb,KAAKmC,EAAOqZ,EAAczI,KAE/D1L,EAAGe,SAAU,OAAQjG,GAGrB6G,EAAI+J,GAAQ,YAAa,kBAE3BsD,IAAsB,EACtBlU,EAAMyhB,aAAe7D,GAAU5d,EAAMogB,aAAaliB,KAAK8B,EAAOqgB,EAAUhR,IACxEnK,EAAGe,SAAU,cAAejG,GAC5BoR,IAAQ,EACR9M,OAAO2b,eAAeC,kBAClBtb,GACFiC,EAAIZ,SAASiG,KAAM,cAAe,QAEtC,EAEA6L,YAAa,SAAgC1I,GAC3C,IAEEsN,EACAC,EACA8E,EAOAC,EAXExc,EAAKlG,KAAKkG,GACZ/B,EAASiM,EAAIjM,OAIb6G,EAAUhL,KAAKgL,QACfyM,EAAQzM,EAAQyM,MAChBpF,EAAiBhH,GAASiH,OAC1BqQ,EAAUrO,KAAgBmD,EAC1BmL,EAAU5X,EAAQ9H,KAClB2f,EAAezQ,IAAeC,EAE9BtR,EAAQf,KACR8iB,GAAiB,EACnB,IAAIvN,GAAJ,CAoGA,QAL2B,IAAvBnF,EAAIvO,gBACNuO,EAAI2D,YAAc3D,EAAIvO,iBAExBsC,EAASgD,EAAQhD,EAAQ6G,EAAQQ,UAAWtF,GAAI,GAChD6c,EAAc,YACV1X,GAASgF,cAAe,OAAOyS,EACnC,GAAInR,GAAOkN,SAASzO,EAAIjM,SAAWA,EAAO4K,UAAY5K,EAAOiZ,YAAcjZ,EAAOkZ,YAActc,EAAMiiB,wBAA0B7e,EAC9H,OAAO8e,GAAU,GAGnB,GADA/N,IAAkB,EACd7C,IAAmBrH,EAAQoO,WAAauJ,EAAUC,IAAYH,EAAS7Q,KAAaC,IACtFO,KAAgBpS,OAASA,KAAKmU,YAAcG,GAAYuD,UAAU7X,KAAMqS,EAAgBV,GAAQvB,KAASqH,EAAMK,SAAS9X,KAAMqS,EAAgBV,GAAQvB,IAAO,CAI7J,GAHAsS,EAA+C,aAApC1iB,KAAK8e,cAAc1O,EAAKjM,GACnCuZ,EAAWvU,EAAQwI,IACnBoR,EAAc,iBACV1X,GAASgF,cAAe,OAAOyS,EACnC,GAAIL,EAYF,OAXA7Q,GAAWC,GACX9L,IACA/F,KAAKkiB,aACLa,EAAc,UACT1X,GAASgF,gBACRyB,GACFD,GAAOuQ,aAAazQ,GAAQG,IAE5BD,GAAOmQ,YAAYrQ,KAGhBsR,GAAU,GAEnB,IAAIC,EAAczX,EAAUvF,EAAI8E,EAAQQ,WACxC,IAAK0X,GAqeX,SAAsB9S,EAAKsS,EAAUvS,GACnC,IAAIgT,EAAaha,EAAQsC,EAAU0E,EAASjK,GAAIiK,EAASnF,QAAQQ,YAC7D4X,EAAsB/U,GAAkC8B,EAASjK,GAAIiK,EAASnF,QAASsD,IAE3F,OAAOoU,EAAWtS,EAAIkI,QAAU8K,EAAoBvZ,MADvC,IACyDuG,EAAImI,QAAU4K,EAAWvZ,QAAUwG,EAAIkI,QAAU6K,EAAWxZ,KAAOyG,EAAImI,QAAU6K,EAAoBxZ,OAD9J,IACiLwG,EAAIkI,QAAU6K,EAAWtZ,OAASuG,EAAImI,QAAU4K,EAAWzZ,GAC3P,CA1e0B2Z,CAAajT,EAAKsS,EAAU1iB,QAAUkjB,EAAYnU,SAAU,CAI9E,GAAImU,IAAgBvR,GAClB,OAAOsR,GAAU,GAUnB,GANIC,GAAehd,IAAOkK,EAAIjM,SAC5BA,EAAS+e,GAEP/e,IACFwZ,EAAaxU,EAAQhF,KAE0D,IAA7EsZ,GAAQ5L,GAAQ3L,EAAIyL,GAAQ+L,EAAUvZ,EAAQwZ,EAAYvN,IAAOjM,GAWnE,OAVA4B,IACImd,GAAeA,EAAYjD,YAE7B/Z,EAAGkc,aAAazQ,GAAQuR,EAAYjD,aAEpC/Z,EAAG8b,YAAYrQ,IAEjBC,GAAW1L,EAEXod,IACOL,GAAU,EAErB,MAAO,GAAIC,GAmcjB,SAAuB9S,EAAKsS,EAAUvS,GACpC,IAAIoT,EAAcpa,EAAQ2B,EAASqF,EAASjK,GAAI,EAAGiK,EAASnF,SAAS,IACjEoY,EAAsB/U,GAAkC8B,EAASjK,GAAIiK,EAASnF,QAASsD,IAE3F,OAAOoU,EAAWtS,EAAIkI,QAAU8K,EAAoBzZ,KADvC,IACwDyG,EAAImI,QAAUgL,EAAY7Z,KAAO0G,EAAIkI,QAAUiL,EAAY1Z,MAAQuG,EAAImI,QAAU6K,EAAoB1Z,IAD7J,IAC6K0G,EAAImI,QAAUgL,EAAY3Z,QAAUwG,EAAIkI,QAAUiL,EAAY5Z,IAC1P,CAxcgC6Z,CAAcpT,EAAKsS,EAAU1iB,MAAO,CAE5D,IAAIyjB,EAAa3Y,EAAS5E,EAAI,EAAG8E,GAAS,GAC1C,GAAIyY,IAAe9R,GACjB,OAAOsR,GAAU,GAInB,GADAtF,EAAaxU,EADbhF,EAASsf,IAEqE,IAA1EhG,GAAQ5L,GAAQ3L,EAAIyL,GAAQ+L,EAAUvZ,EAAQwZ,EAAYvN,GAAK,GAMjE,OALArK,IACAG,EAAGkc,aAAazQ,GAAQ8R,GACxB7R,GAAW1L,EAEXod,IACOL,GAAU,EAErB,MAAO,GAAI9e,EAAO+C,aAAehB,EAAI,CACnCyX,EAAaxU,EAAQhF,GACrB,IAAIwV,EACF+J,EAYEC,EAXFC,EAAiBjS,GAAOzK,aAAehB,EACvC2d,GAx2Ba,SAA4BnG,EAAUC,EAAY+E,GACrE,IAAIoB,EAAcpB,EAAWhF,EAAS/T,KAAO+T,EAAShU,IACpDqa,EAAcrB,EAAWhF,EAAS7T,MAAQ6T,EAAS9T,OACnDoa,EAAkBtB,EAAWhF,EAAS3T,MAAQ2T,EAAS5T,OACvDma,EAAcvB,EAAW/E,EAAWhU,KAAOgU,EAAWjU,IACtDwa,EAAcxB,EAAW/E,EAAW9T,MAAQ8T,EAAW/T,OACvDua,EAAkBzB,EAAW/E,EAAW5T,MAAQ4T,EAAW7T,OAC7D,OAAOga,IAAgBG,GAAeF,IAAgBG,GAAeJ,EAAcE,EAAkB,IAAMC,EAAcE,EAAkB,CAC7I,CAg2B2BC,CAAmBzS,GAAO5C,UAAY4C,GAAO2K,QAAUoB,EAAUvZ,EAAO4K,UAAY5K,EAAOmY,QAAUqB,EAAY+E,GACpI2B,EAAQ3B,EAAW,MAAQ,OAC3B4B,EAAkB/Z,EAAepG,EAAQ,MAAO,QAAUoG,EAAeoH,GAAQ,MAAO,OACxF4S,EAAeD,EAAkBA,EAAgBhY,eAAY,EAQ/D,GAPIuI,KAAe1Q,IACjBuf,EAAwB/F,EAAW0G,GACnCjP,IAAwB,EACxBC,IAA0BwO,GAAmB7Y,EAAQwO,YAAcoK,GAErEjK,EAibR,SAA2BvJ,EAAKjM,EAAQwZ,EAAY+E,EAAUnJ,EAAeE,EAAuBD,EAAYgL,GAC9G,IAAIC,EAAc/B,EAAWtS,EAAImI,QAAUnI,EAAIkI,QAC7CoM,EAAehC,EAAW/E,EAAW7T,OAAS6T,EAAW5T,MACzD4a,EAAWjC,EAAW/E,EAAWjU,IAAMiU,EAAWhU,KAClDib,EAAWlC,EAAW/E,EAAW/T,OAAS+T,EAAW9T,MACrDgb,GAAS,EACX,IAAKrL,EAEH,GAAIgL,GAAgBzP,GAAqB2P,EAAenL,GAOtD,IAJKnE,KAA4C,IAAlBN,GAAsB2P,EAAcE,EAAWD,EAAejL,EAAwB,EAAIgL,EAAcG,EAAWF,EAAejL,EAAwB,KAEvLrE,IAAwB,GAErBA,GAOHyP,GAAS,OALT,GAAsB,IAAlB/P,GAAsB2P,EAAcE,EAAW5P,GACjD0P,EAAcG,EAAW7P,GACzB,OAAQD,QAOZ,GAAI2P,EAAcE,EAAWD,GAAgB,EAAInL,GAAiB,GAAKkL,EAAcG,EAAWF,GAAgB,EAAInL,GAAiB,EACnI,OAoBR,SAA6BpV,GAC3B,OAAI0H,EAAM8F,IAAU9F,EAAM1H,GACjB,GAEC,CAEZ,CA1Be2gB,CAAoB3gB,GAKjC,OADA0gB,EAASA,GAAUrL,KAGbiL,EAAcE,EAAWD,EAAejL,EAAwB,GAAKgL,EAAcG,EAAWF,EAAejL,EAAwB,GAChIgL,EAAcE,EAAWD,EAAe,EAAI,GAAK,EAGrD,CACT,CAxdoBK,CAAkB3U,EAAKjM,EAAQwZ,EAAY+E,EAAUmB,EAAkB,EAAI7Y,EAAQuO,cAAgD,MAAjCvO,EAAQyO,sBAAgCzO,EAAQuO,cAAgBvO,EAAQyO,sBAAuBpE,GAAwBR,KAAe1Q,GAElO,IAAdwV,EAAiB,CAEnB,IAAIqL,EAAYnZ,EAAM8F,IACtB,GACEqT,GAAarL,EACbgK,EAAU/R,GAASzG,SAAS6Z,SACrBrB,IAAwC,SAA5B/b,EAAI+b,EAAS,YAAyBA,IAAYrV,IACzE,CAEA,GAAkB,IAAdqL,GAAmBgK,IAAYxf,EACjC,OAAO8e,GAAU,GAEnBpO,GAAa1Q,EACb2Q,GAAgB6E,EAChB,IAAIsG,EAAc9b,EAAO8gB,mBACvBC,GAAQ,EAENC,EAAa1H,GAAQ5L,GAAQ3L,EAAIyL,GAAQ+L,EAAUvZ,EAAQwZ,EAAYvN,EAD3E8U,EAAsB,IAAdvL,GAER,IAAmB,IAAfwL,EAwBF,OAvBmB,IAAfA,IAAoC,IAAhBA,IACtBD,EAAuB,IAAfC,GAEV5P,IAAU,EACV5H,WAAWyQ,GAAW,IACtBrY,IACImf,IAAUjF,EACZ/Z,EAAG8b,YAAYrQ,IAEfxN,EAAO+C,WAAWkb,aAAazQ,GAAQuT,EAAQjF,EAAc9b,GAI3DmgB,GACF1W,EAAS0W,EAAiB,EAAGC,EAAeD,EAAgBhY,WAE9DsF,GAAWD,GAAOzK,gBAGYgK,IAA1BwS,GAAwCrO,KAC1CN,GAAqB1H,KAAKqT,IAAIgD,EAAwBva,EAAQhF,GAAQkgB,KAExEf,IACOL,GAAU,EAErB,CACA,GAAI/c,EAAG2Y,SAASlN,IACd,OAAOsR,GAAU,EAErB,CACA,OAAO,CA3OY,CACnB,SAASF,EAAcziB,EAAM8kB,GAC3BnV,GAAY3P,EAAMS,EAAOmD,EAAe,CACtCkM,IAAKA,EACLuS,QAASA,EACT0C,KAAM3C,EAAW,WAAa,aAC9BD,OAAQA,EACR/E,SAAUA,EACVC,WAAYA,EACZiF,QAASA,EACTC,aAAcA,EACd1e,OAAQA,EACR8e,UAAWA,EACXlF,OAAQ,SAAgB5Z,EAAQ+gB,GAC9B,OAAOzH,GAAQ5L,GAAQ3L,EAAIyL,GAAQ+L,EAAUvZ,EAAQgF,EAAQhF,GAASiM,EAAK8U,EAC7E,EACA5B,QAASA,GACR8B,GACL,CAGA,SAASrf,IACPgd,EAAc,4BACdhiB,EAAMua,wBACFva,IAAU8hB,GACZA,EAAavH,uBAEjB,CAGA,SAAS2H,EAAUqC,GAiDjB,OAhDAvC,EAAc,oBAAqB,CACjCuC,UAAWA,IAETA,IAEE3C,EACFtQ,EAAe6P,aAEf7P,EAAekT,WAAWxkB,GAExBA,IAAU8hB,IAEZrb,EAAYmK,GAAQS,GAAcA,GAAYpH,QAAQ4O,WAAavH,EAAerH,QAAQ4O,YAAY,GACtGpS,EAAYmK,GAAQ3G,EAAQ4O,YAAY,IAEtCxH,KAAgBrR,GAASA,IAAUsK,GAASiH,OAC9CF,GAAcrR,EACLA,IAAUsK,GAASiH,QAAUF,KACtCA,GAAc,MAIZyQ,IAAiB9hB,IACnBA,EAAMiiB,sBAAwB7e,GAEhCpD,EAAMkb,WAAW,WACf8G,EAAc,6BACdhiB,EAAMiiB,sBAAwB,IAChC,GACIjiB,IAAU8hB,IACZA,EAAa5G,aACb4G,EAAaG,sBAAwB,QAKrC7e,IAAWwN,KAAWA,GAAO5C,UAAY5K,IAAW+B,IAAO/B,EAAO4K,YACpE8F,GAAa,MAIV7J,EAAQuP,gBAAmBnK,EAAIyB,QAAU1N,IAAW6C,WACvD2K,GAAOzK,WAAWkI,IAAS4J,iBAAiB5I,EAAIjM,SAG/CmhB,GAAanN,GAA8B/H,KAE7CpF,EAAQuP,gBAAkBnK,EAAI6H,iBAAmB7H,EAAI6H,kBAC/C6K,GAAiB,CAC1B,CAGA,SAASQ,IACP7Q,GAAW5G,EAAM8F,IACjBe,GAAoB7G,EAAM8F,GAAQ3G,EAAQQ,WAC1C0H,GAAe,CACb/C,SAAUpP,EACVT,KAAM,SACN+S,KAAMnN,EACNuM,SAAUA,GACVC,kBAAmBA,GACnBvB,cAAef,GAEnB,CA8IF,EACA4S,sBAAuB,KACvBwC,eAAgB,WACdlf,EAAIU,SAAU,YAAahH,KAAK4gB,cAChCta,EAAIU,SAAU,YAAahH,KAAK4gB,cAChCta,EAAIU,SAAU,cAAehH,KAAK4gB,cAClCta,EAAIU,SAAU,WAAYmR,IAC1B7R,EAAIU,SAAU,YAAamR,IAC3B7R,EAAIU,SAAU,YAAamR,GAC7B,EACAsN,aAAc,WACZ,IAAIzF,EAAgBhgB,KAAKkG,GAAG8Z,cAC5B1Z,EAAI0Z,EAAe,UAAWhgB,KAAKogB,SACnC9Z,EAAI0Z,EAAe,WAAYhgB,KAAKogB,SACpC9Z,EAAI0Z,EAAe,YAAahgB,KAAKogB,SACrC9Z,EAAI0Z,EAAe,gBAAiBhgB,KAAKogB,SACzC9Z,EAAI0Z,EAAe,cAAehgB,KAAKogB,SACvC9Z,EAAIU,SAAU,cAAehH,KAC/B,EACAogB,QAAS,SAA4BhQ,GACnC,IAAIlK,EAAKlG,KAAKkG,GACZ8E,EAAUhL,KAAKgL,QAGjByH,GAAW5G,EAAM8F,IACjBe,GAAoB7G,EAAM8F,GAAQ3G,EAAQQ,WAC1CyE,GAAY,OAAQjQ,KAAM,CACxBoQ,IAAKA,IAEPwB,GAAWD,IAAUA,GAAOzK,WAG5BuL,GAAW5G,EAAM8F,IACjBe,GAAoB7G,EAAM8F,GAAQ3G,EAAQQ,WACtCH,GAASgF,gBAIb4E,IAAsB,EACtBI,IAAyB,EACzBD,IAAwB,EACxBsQ,cAAc1lB,KAAKqiB,SACnBnG,aAAalc,KAAKygB,iBAClB7B,GAAgB5e,KAAKmiB,SACrBvD,GAAgB5e,KAAKwiB,cAGjBxiB,KAAKob,kBACP9U,EAAIU,SAAU,OAAQhH,MACtBsG,EAAIJ,EAAI,YAAalG,KAAK6gB,eAE5B7gB,KAAKwlB,iBACLxlB,KAAKylB,eACD9f,GACFiC,EAAIZ,SAASiG,KAAM,cAAe,IAEpCrF,EAAI+J,GAAQ,YAAa,IACrBvB,IACE+B,KACF/B,EAAI2D,YAAc3D,EAAIvO,kBACrBmJ,EAAQsP,YAAclK,EAAI6H,mBAE7B3J,IAAWA,GAAQpH,YAAcoH,GAAQpH,WAAWye,YAAYrX,KAC5DuD,KAAWD,IAAYQ,IAA2C,UAA5BA,GAAY+B,cAEpDnC,IAAWA,GAAQ9K,YAAc8K,GAAQ9K,WAAWye,YAAY3T,IAE9DL,KACE3R,KAAKob,iBACP9U,EAAIqL,GAAQ,UAAW3R,MAEzBme,GAAkBxM,IAClBA,GAAO1Q,MAAM,eAAiB,GAI1BkR,KAAU8C,IACZzN,EAAYmK,GAAQS,GAAcA,GAAYpH,QAAQ4O,WAAa5Z,KAAKgL,QAAQ4O,YAAY,GAE9FpS,EAAYmK,GAAQ3R,KAAKgL,QAAQ6O,aAAa,GAG9C3G,GAAe,CACb/C,SAAUnQ,KACVM,KAAM,WACN+S,KAAMzB,GACNa,SAAU,KACVC,kBAAmB,KACnBvB,cAAef,IAEbyB,KAAWD,IACTa,IAAY,IAEdS,GAAe,CACbrB,OAAQD,GACRtR,KAAM,MACN+S,KAAMzB,GACN0B,OAAQzB,GACRV,cAAef,IAIjB8C,GAAe,CACb/C,SAAUnQ,KACVM,KAAM,SACN+S,KAAMzB,GACNT,cAAef,IAIjB8C,GAAe,CACbrB,OAAQD,GACRtR,KAAM,OACN+S,KAAMzB,GACN0B,OAAQzB,GACRV,cAAef,IAEjB8C,GAAe,CACb/C,SAAUnQ,KACVM,KAAM,OACN+S,KAAMzB,GACNT,cAAef,KAGnBgC,IAAeA,GAAYtQ,QAEvB2Q,KAAaF,IACXE,IAAY,IAEdS,GAAe,CACb/C,SAAUnQ,KACVM,KAAM,SACN+S,KAAMzB,GACNT,cAAef,IAEjB8C,GAAe,CACb/C,SAAUnQ,KACVM,KAAM,OACN+S,KAAMzB,GACNT,cAAef,KAKnB/E,GAASiH,SAEK,MAAZG,KAAkC,IAAdA,KACtBA,GAAWF,GACXG,GAAoBF,IAEtBU,GAAe,CACb/C,SAAUnQ,KACVM,KAAM,MACN+S,KAAMzB,GACNT,cAAef,IAIjBpQ,KAAK8B,WA3HT9B,KAAKshB,UAgIT,EACAA,SAAU,WACRrR,GAAY,UAAWjQ,MACvB6R,GAASF,GAASC,GAAWtD,GAAUwD,GAASE,GAAUD,GAAaE,GAAcsC,GAASC,GAAWrC,GAAQM,GAAWC,GAAoBH,GAAWC,GAAoBqC,GAAaC,GAAgB1C,GAAckC,GAAcjJ,GAASE,QAAUF,GAASC,MAAQD,GAASW,MAAQX,GAASiH,OAAS,KAC/SkD,GAAkBjR,QAAQ,SAAU2B,GAClCA,EAAGsZ,SAAU,CACf,GACAhK,GAAkBnR,OAASoQ,GAASC,GAAS,CAC/C,EACAkR,YAAa,SAAgCxV,GAC3C,OAAQA,EAAI7O,MACV,IAAK,OACL,IAAK,UACHvB,KAAKogB,QAAQhQ,GACb,MACF,IAAK,YACL,IAAK,WACCuB,KACF3R,KAAK8Y,YAAY1I,GAmJ3B,SAAoCA,GAC9BA,EAAIgK,eACNhK,EAAIgK,aAAayL,WAAa,QAEhCzV,EAAI2D,YAAc3D,EAAIvO,gBACxB,CAvJUikB,CAAgB1V,IAElB,MACF,IAAK,cACHA,EAAIvO,iBAGV,EAKAkkB,QAAS,WAOP,IANA,IACE7f,EADE8f,EAAQ,GAEV7a,EAAWnL,KAAKkG,GAAGiF,SACnB/G,EAAI,EACJ2E,EAAIoC,EAAS9G,OACb2G,EAAUhL,KAAKgL,QACV5G,EAAI2E,EAAG3E,IAER+C,EADJjB,EAAKiF,EAAS/G,GACE4G,EAAQQ,UAAWxL,KAAKkG,IAAI,IAC1C8f,EAAM/hB,KAAKiC,EAAG+f,aAAajb,EAAQwP,aAAe6D,GAAYnY,IAGlE,OAAO8f,CACT,EAKA9iB,KAAM,SAAc8iB,EAAOE,GACzB,IAAIC,EAAQ,CAAC,EACXtU,EAAS7R,KAAKkG,GAChBlG,KAAK+lB,UAAUxhB,QAAQ,SAAUxB,EAAIqB,GACnC,IAAI8B,EAAK2L,EAAO1G,SAAS/G,GACrB+C,EAAQjB,EAAIlG,KAAKgL,QAAQQ,UAAWqG,GAAQ,KAC9CsU,EAAMpjB,GAAMmD,EAEhB,EAAGlG,MACHkmB,GAAgBlmB,KAAKsb,wBACrB0K,EAAMzhB,QAAQ,SAAUxB,GAClBojB,EAAMpjB,KACR8O,EAAO8T,YAAYQ,EAAMpjB,IACzB8O,EAAOmQ,YAAYmE,EAAMpjB,IAE7B,GACAmjB,GAAgBlmB,KAAKic,YACvB,EAIAna,KAAM,WACJ,IAAIK,EAAQnC,KAAKgL,QAAQ7I,MACzBA,GAASA,EAAMikB,KAAOjkB,EAAMikB,IAAIpmB,KAClC,EAOAmH,QAAS,SAAmBjB,EAAIO,GAC9B,OAAOU,EAAQjB,EAAIO,GAAYzG,KAAKgL,QAAQQ,UAAWxL,KAAKkG,IAAI,EAClE,EAOA2J,OAAQ,SAAgBvP,EAAMwE,GAC5B,IAAIkG,EAAUhL,KAAKgL,QACnB,QAAc,IAAVlG,EACF,OAAOkG,EAAQ1K,GAEf,IAAIwQ,EAAgBpB,GAAciB,aAAa3Q,KAAMM,EAAMwE,GAEzDkG,EAAQ1K,QADmB,IAAlBwQ,EACOA,EAEAhM,EAEL,UAATxE,GACF+W,GAAcrM,EAGpB,EAIAqb,QAAS,WACPpW,GAAY,UAAWjQ,MACvB,IAAIkG,EAAKlG,KAAKkG,GACdA,EAAGkJ,IAAW,KACd9I,EAAIJ,EAAI,YAAalG,KAAKqb,aAC1B/U,EAAIJ,EAAI,aAAclG,KAAKqb,aAC3B/U,EAAIJ,EAAI,cAAelG,KAAKqb,aACxBrb,KAAKob,kBACP9U,EAAIJ,EAAI,WAAYlG,MACpBsG,EAAIJ,EAAI,YAAalG,OAGvBwO,MAAM9P,UAAU6F,QAAQ3F,KAAKsH,EAAGogB,iBAAiB,eAAgB,SAAUpgB,GACzEA,EAAG+b,gBAAgB,YACrB,GACAjiB,KAAKogB,UACLpgB,KAAKqgB,4BACLlL,GAAU2G,OAAO3G,GAAUlN,QAAQjI,KAAKkG,IAAK,GAC7ClG,KAAKkG,GAAKA,EAAK,IACjB,EACAgc,WAAY,WACV,IAAKjQ,GAAa,CAEhB,GADAhC,GAAY,YAAajQ,MACrBqL,GAASgF,cAAe,OAC5BzI,EAAIoK,GAAS,UAAW,QACpBhS,KAAKgL,QAAQ0O,mBAAqB1H,GAAQ9K,YAC5C8K,GAAQ9K,WAAWye,YAAY3T,IAEjCC,IAAc,CAChB,CACF,EACAsT,WAAY,SAAoBnT,GAC9B,GAAgC,UAA5BA,EAAY+B,aAIhB,GAAIlC,GAAa,CAEf,GADAhC,GAAY,YAAajQ,MACrBqL,GAASgF,cAAe,OAGxBsB,GAAOzK,YAAc2K,IAAW7R,KAAKgL,QAAQyM,MAAMO,YAE5ClG,GACTD,GAAOuQ,aAAapQ,GAASF,IAE7BD,GAAOmQ,YAAYhQ,IAJnBH,GAAOuQ,aAAapQ,GAASL,IAM3B3R,KAAKgL,QAAQyM,MAAMO,aACrBhY,KAAK8c,QAAQnL,GAAQK,IAEvBpK,EAAIoK,GAAS,UAAW,IACxBC,IAAc,CAChB,OApBEjS,KAAKkiB,YAqBT,GA8IEzM,IACFxP,EAAGe,SAAU,YAAa,SAAUoJ,IAC7B/E,GAASiH,QAAU2C,KAAwB7E,EAAI2D,YAClD3D,EAAIvO,gBAER,GAIFwJ,GAASkb,MAAQ,CACftgB,GAAIA,EACJK,IAAKA,EACLsB,IAAKA,EACLe,KAAMA,EACN6d,GAAI,SAAYtgB,EAAIO,GAClB,QAASU,EAAQjB,EAAIO,EAAUP,GAAI,EACrC,EACAugB,OA72DF,SAAgBC,EAAKnI,GACnB,GAAImI,GAAOnI,EACT,IAAK,IAAIrgB,KAAOqgB,EACVA,EAAI5f,eAAeT,KACrBwoB,EAAIxoB,GAAOqgB,EAAIrgB,IAIrB,OAAOwoB,CACT,EAq2DEnZ,SAAUA,EACVpG,QAASA,EACTK,YAAaA,EACbwE,MAAOA,EACPH,MAAOA,EACP8a,SAAUhI,GACViI,eAAgBhI,GAChBiI,gBAAiB5Q,GACjBnL,SAAUA,EACVsE,QAASA,IAQX/D,GAAS9M,IAAM,SAAUuoB,GACvB,OAAOA,EAAQ1X,GACjB,EAMA/D,GAASsE,MAAQ,WACf,IAAK,IAAIoX,EAAOnnB,UAAUyE,OAAQkL,EAAU,IAAIf,MAAMuY,GAAOC,EAAO,EAAGA,EAAOD,EAAMC,IAClFzX,EAAQyX,GAAQpnB,UAAUonB,GAExBzX,EAAQ,GAAGlQ,cAAgBmP,QAAOe,EAAUA,EAAQ,IACxDA,EAAQhL,QAAQ,SAAUqL,GACxB,IAAKA,EAAOlR,YAAckR,EAAOlR,UAAUW,YACzC,KAAM,gEAAgE2Q,OAAO,CAAC,EAAEiJ,SAASra,KAAKgR,IAE5FA,EAAO2W,QAAOlb,GAASkb,MAAQriB,EAAeA,EAAe,CAAC,EAAGmH,GAASkb,OAAQ3W,EAAO2W,QAC7F7W,GAAcC,MAAMC,EACtB,EACF,EAOAvE,GAASjM,OAAS,SAAU8G,EAAI8E,GAC9B,OAAO,IAAIK,GAASnF,EAAI8E,EAC1B,EAGAK,GAAS4b,QAvvEK,SAyvEd,IACEC,GACAC,GAEAC,GACAC,GACAC,GACAC,GAPEC,GAAc,GAGhBC,IAAY,EA4Gd,SAASC,KACPF,GAAYjjB,QAAQ,SAAUojB,GAC5BjC,cAAciC,EAAWC,IAC3B,GACAJ,GAAc,EAChB,CACA,SAASK,KACPnC,cAAc6B,GAChB,CACA,IAAII,GAAapa,EAAS,SAAU6C,EAAKpF,EAAS6G,EAAQiW,GAExD,GAAK9c,EAAQ+c,OAAb,CACA,IAMEC,EANEna,GAAKuC,EAAIgI,QAAUhI,EAAIgI,QAAQ,GAAKhI,GAAKkI,QAC3CxK,GAAKsC,EAAIgI,QAAUhI,EAAIgI,QAAQ,GAAKhI,GAAKmI,QACzC0P,EAAOjd,EAAQkd,kBACfC,EAAQnd,EAAQod,YAChBhc,EAAcpD,IACZqf,GAAqB,EAIrBlB,KAAiBtV,IACnBsV,GAAetV,EACf6V,KACAR,GAAWlc,EAAQ+c,OACnBC,EAAiBhd,EAAQsd,UACR,IAAbpB,KACFA,GAAWvc,EAA2BkH,GAAQ,KAGlD,IAAI0W,EAAY,EACZC,EAAgBtB,GACpB,EAAG,CACD,IAAIhhB,EAAKsiB,EACPja,EAAOpF,EAAQjD,GACfwD,EAAM6E,EAAK7E,IACXE,EAAS2E,EAAK3E,OACdD,EAAO4E,EAAK5E,KACZE,EAAQ0E,EAAK1E,MACbE,EAAQwE,EAAKxE,MACbD,EAASyE,EAAKzE,OACd2e,OAAa,EACbC,OAAa,EACb/b,EAAczG,EAAGyG,YACjBE,EAAe3G,EAAG2G,aAClBqJ,EAAQtO,EAAI1B,GACZyiB,EAAaziB,EAAGmG,WAChBuc,EAAa1iB,EAAGoG,UACdpG,IAAOkG,GACTqc,EAAa1e,EAAQ4C,IAAoC,SAApBuJ,EAAMnJ,WAA4C,WAApBmJ,EAAMnJ,WAA8C,YAApBmJ,EAAMnJ,WACzG2b,EAAa5e,EAAS+C,IAAqC,SAApBqJ,EAAMlJ,WAA4C,WAApBkJ,EAAMlJ,WAA8C,YAApBkJ,EAAMlJ,aAE3Gyb,EAAa1e,EAAQ4C,IAAoC,SAApBuJ,EAAMnJ,WAA4C,WAApBmJ,EAAMnJ,WACzE2b,EAAa5e,EAAS+C,IAAqC,SAApBqJ,EAAMlJ,WAA4C,WAApBkJ,EAAMlJ,YAE7E,IAAI6b,EAAKJ,IAAepb,KAAKqT,IAAI7W,EAAQgE,IAAMoa,GAAQU,EAAa5e,EAAQ4C,IAAgBU,KAAKqT,IAAI/W,EAAOkE,IAAMoa,KAAUU,GACxHG,EAAKJ,IAAerb,KAAKqT,IAAI9W,EAASkE,IAAMma,GAAQW,EAAa9e,EAAS+C,IAAiBQ,KAAKqT,IAAIhX,EAAMoE,IAAMma,KAAUW,GAC9H,IAAKpB,GAAYe,GACf,IAAK,IAAInkB,EAAI,EAAGA,GAAKmkB,EAAWnkB,IACzBojB,GAAYpjB,KACfojB,GAAYpjB,GAAK,CAAC,GAIpBojB,GAAYe,GAAWM,IAAMA,GAAMrB,GAAYe,GAAWO,IAAMA,GAAMtB,GAAYe,GAAWriB,KAAOA,IACtGshB,GAAYe,GAAWriB,GAAKA,EAC5BshB,GAAYe,GAAWM,GAAKA,EAC5BrB,GAAYe,GAAWO,GAAKA,EAC5BpD,cAAc8B,GAAYe,GAAWX,KAC3B,GAANiB,GAAiB,GAANC,IACbT,GAAqB,EAErBb,GAAYe,GAAWX,IAAMtF,YAAY,WAEnCwF,GAA6B,IAAf9nB,KAAK+oB,OACrB1d,GAASiH,OAAOsO,aAAa0G,IAE/B,IAAI0B,EAAgBxB,GAAYxnB,KAAK+oB,OAAOD,GAAKtB,GAAYxnB,KAAK+oB,OAAOD,GAAKX,EAAQ,EAClFc,EAAgBzB,GAAYxnB,KAAK+oB,OAAOF,GAAKrB,GAAYxnB,KAAK+oB,OAAOF,GAAKV,EAAQ,EACxD,mBAAnBH,GACoI,aAAzIA,EAAeppB,KAAKyM,GAASE,QAAQrE,WAAWkI,IAAU6Z,EAAeD,EAAe5Y,EAAKkX,GAAYE,GAAYxnB,KAAK+oB,OAAO7iB,KAIvI0H,EAAS4Z,GAAYxnB,KAAK+oB,OAAO7iB,GAAI+iB,EAAeD,EACtD,EAAE/pB,KAAK,CACL8pB,MAAOR,IACL,MAGRA,GACF,OAASvd,EAAQke,cAAgBV,IAAkBpc,IAAgBoc,EAAgB7d,EAA2B6d,GAAe,KAC7Hf,GAAYY,CAjFe,CAkF7B,EAAG,IAECc,GAAO,SAAclY,GACvB,IAAIE,EAAgBF,EAAKE,cACvBiB,EAAcnB,EAAKmB,YACnBT,EAASV,EAAKU,OACdU,EAAiBpB,EAAKoB,eACtBY,EAAwBhC,EAAKgC,sBAC7BN,EAAqB1B,EAAK0B,mBAC1BE,EAAuB5B,EAAK4B,qBAC9B,GAAK1B,EAAL,CACA,IAAIiY,EAAahX,GAAeC,EAChCM,IACA,IAAIoM,EAAQ5N,EAAckY,gBAAkBlY,EAAckY,eAAehlB,OAAS8M,EAAckY,eAAe,GAAKlY,EAChHhN,EAAS6C,SAASwa,iBAAiBzC,EAAMzG,QAASyG,EAAMxG,SAC5D1F,IACIuW,IAAeA,EAAWljB,GAAG2Y,SAAS1a,KACxC8O,EAAsB,SACtBjT,KAAKspB,QAAQ,CACX3X,OAAQA,EACRS,YAAaA,IAVS,CAa5B,EACA,SAASmX,KAAU,CA8BnB,SAASC,KAAU,CA7BnBD,GAAO7qB,UAAY,CACjB+qB,WAAY,KACZC,UAAW,SAAmBC,GAC5B,IAAInX,EAAoBmX,EAAMnX,kBAC9BxS,KAAKypB,WAAajX,CACpB,EACA8W,QAAS,SAAiBM,GACxB,IAAIjY,EAASiY,EAAMjY,OACjBS,EAAcwX,EAAMxX,YACtBpS,KAAKmQ,SAASmL,wBACVlJ,GACFA,EAAYkJ,wBAEd,IAAI2E,EAAcnV,EAAS9K,KAAKmQ,SAASjK,GAAIlG,KAAKypB,WAAYzpB,KAAKgL,SAC/DiV,EACFjgB,KAAKmQ,SAASjK,GAAGkc,aAAazQ,EAAQsO,GAEtCjgB,KAAKmQ,SAASjK,GAAG8b,YAAYrQ,GAE/B3R,KAAKmQ,SAAS8L,aACV7J,GACFA,EAAY6J,YAEhB,EACAkN,KAAMA,IAERlkB,EAASskB,GAAQ,CACfxZ,WAAY,kBAGdyZ,GAAO9qB,UAAY,CACjB4qB,QAAS,SAAiBO,GACxB,IAAIlY,EAASkY,EAAMlY,OAEfmY,EADYD,EAAMzX,aACcpS,KAAKmQ,SACzC2Z,EAAexO,wBACf3J,EAAOzK,YAAcyK,EAAOzK,WAAWye,YAAYhU,GACnDmY,EAAe7N,YACjB,EACAkN,KAAMA,IAERlkB,EAASukB,GAAQ,CACfzZ,WAAY,kBAmqBd1E,GAASsE,MAAM,IA16Bf,WACE,SAASoa,IAUP,IAAK,IAAI3jB,KATTpG,KAAKwP,SAAW,CACduY,QAAQ,EACRiC,yBAAyB,EACzB9B,kBAAmB,GACnBE,YAAa,GACbc,cAAc,GAIDlpB,KACQ,MAAjBoG,EAAGqN,OAAO,IAAkC,mBAAbzT,KAAKoG,KACtCpG,KAAKoG,GAAMpG,KAAKoG,GAAInH,KAAKe,MAG/B,CAkFA,OAjFA+pB,EAAWrrB,UAAY,CACrBwT,YAAa,SAAqBjB,GAChC,IAAIE,EAAgBF,EAAKE,cACrBnR,KAAKmQ,SAASiL,gBAChBnV,EAAGe,SAAU,WAAYhH,KAAKiqB,mBAE1BjqB,KAAKgL,QAAQmQ,eACflV,EAAGe,SAAU,cAAehH,KAAKkqB,2BACxB/Y,EAAciH,QACvBnS,EAAGe,SAAU,YAAahH,KAAKkqB,2BAE/BjkB,EAAGe,SAAU,YAAahH,KAAKkqB,0BAGrC,EACAC,kBAAmB,SAA2BR,GAC5C,IAAIxY,EAAgBwY,EAAMxY,cAErBnR,KAAKgL,QAAQof,gBAAmBjZ,EAAcU,QACjD7R,KAAKiqB,kBAAkB9Y,EAE3B,EACAgY,KAAM,WACAnpB,KAAKmQ,SAASiL,gBAChB9U,EAAIU,SAAU,WAAYhH,KAAKiqB,oBAE/B3jB,EAAIU,SAAU,cAAehH,KAAKkqB,2BAClC5jB,EAAIU,SAAU,YAAahH,KAAKkqB,2BAChC5jB,EAAIU,SAAU,YAAahH,KAAKkqB,4BAElCrC,KACAH,KA17DJxL,aAAa5U,GACbA,OAAmB,CA27DjB,EACA+iB,QAAS,WACP/C,GAAaH,GAAeD,GAAWO,GAAYF,GAA6BH,GAAkBC,GAAkB,KACpHG,GAAYnjB,OAAS,CACvB,EACA6lB,0BAA2B,SAAmC9Z,GAC5DpQ,KAAKiqB,kBAAkB7Z,GAAK,EAC9B,EACA6Z,kBAAmB,SAA2B7Z,EAAKgR,GACjD,IAAIrgB,EAAQf,KACR6N,GAAKuC,EAAIgI,QAAUhI,EAAIgI,QAAQ,GAAKhI,GAAKkI,QAC3CxK,GAAKsC,EAAIgI,QAAUhI,EAAIgI,QAAQ,GAAKhI,GAAKmI,QACzC/L,EAAOxF,SAASwa,iBAAiB3T,EAAGC,GAOtC,GANAwZ,GAAalX,EAMTgR,GAAYphB,KAAKgL,QAAQgf,yBAA2BvkB,GAAQD,GAAcG,EAAQ,CACpFgiB,GAAWvX,EAAKpQ,KAAKgL,QAASwB,EAAM4U,GAGpC,IAAIkJ,EAAiB3f,EAA2B6B,GAAM,IAClDib,IAAeF,IAA8B1Z,IAAMuZ,IAAmBtZ,IAAMuZ,KAC9EE,IAA8BM,KAE9BN,GAA6BjF,YAAY,WACvC,IAAIiI,EAAU5f,EAA2B3D,SAASwa,iBAAiB3T,EAAGC,IAAI,GACtEyc,IAAYD,IACdA,EAAiBC,EACjB7C,MAEFC,GAAWvX,EAAKrP,EAAMiK,QAASuf,EAASnJ,EAC1C,EAAG,IACHgG,GAAkBvZ,EAClBwZ,GAAkBvZ,EAEtB,KAAO,CAEL,IAAK9N,KAAKgL,QAAQke,cAAgBve,EAA2B6B,GAAM,KAAUxD,IAE3E,YADA0e,KAGFC,GAAWvX,EAAKpQ,KAAKgL,QAASL,EAA2B6B,GAAM,IAAQ,EACzE,CACF,GAEKvH,EAAS8kB,EAAY,CAC1Bha,WAAY,SACZN,qBAAqB,GAEzB,GAq0BApE,GAASsE,MAAM6Z,GAAQD,IAEvB,YC1yGkC,IAEbiB,GAAY,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAA9qB,MAAA,KAAAC,YAAA,KAAAT,EAAAqrB,EAAAC,GAAA,IAAA5qB,EAAA2qB,EAAA9rB,UA6E9B,OA7E8BmB,EAC/BC,OAAA,SAAOI,GACLuqB,EAAA/rB,UAAMoB,OAAMlB,KAAC,KAAAsB,GACbF,KAAKwB,SAAU,EACfxB,KAAKqC,eAAiB,GACtBrC,KAAK0qB,aACP,EAAC7qB,EAED8qB,SAAA,WAAU,IAAA5pB,EAAA,KACJmF,EAAKc,SAAS4jB,eAAe,2BAClBvf,GAASjM,OAAO8G,EAAG,CAC5B+T,UAAW,IACXV,cAAe,IACfsR,MAAO,SAAC9rB,GAAC,OAAKgC,EAAK+pB,WAAW/rB,EAAE,GAExC,EAACc,EAEDiB,QAAA,WACE,OACEE,EAAA,OAAKP,UAAU,2CACbO,EAAA,OAAKP,UAAU,aAEbO,EAAA,OAAKC,MAAO,CAAC8pB,cAAe,SAC1B/pB,EAACM,IAAM,CAACb,UAAW,SAAUgB,QAAS,WAAF,OAAQd,IAAI0C,MAAMC,KAAK7D,EAAmB,GAC3EkB,IAAIC,WAAWC,MAAM,2CAI1BG,EAAA,MAAI+B,GAAG,0BAA0B9B,MAAO,CAAC+pB,QAAS,MAAOC,cAAe,QAASC,SAAUlrB,KAAK2qB,SAAS1rB,KAAKe,OAC3GA,KAAKqC,eAAe8oB,IAAI,SAAClrB,GACxB,OACEe,EAAA,MAAIoqB,OAAQnrB,EAAmB8C,KAAM9B,MAAO,CAACoqB,UAAW,MAAOC,WAAY,mBACxE3oB,EAAmB4oB,UAAU,CAAEtrB,mBAAAA,IAGtC,KAMV,EAACJ,EAEDirB,WAAA,SAAW/rB,GAIT,GAHiBA,EAAE0T,WACF1T,EAAEwT,SAEI,CAIrB,IAHA,IAAMpH,EAAWpM,EAAE0P,KAAKtD,SAClBqgB,EAAiB,CAAC,EAEhBpnB,EAAE,EAAEA,EAAE+G,EAAS9G,OAAOD,IAAI,CAChC,IAAMsK,EAAQvD,EAAS/G,GAGvBonB,EAFexd,EAAEU,GAAO+c,KAAK,WAEJrnB,CAC3B,CAEAzD,IAAI+qB,QAAQ,CACV3pB,IAAQpB,IAAIgrB,MAAMC,UAAU,UAAS,wBACrCC,OAAQ,OACR5e,KAAM,CAAEue,eAAAA,IAEZ,CACF,EAAC3rB,EAEDisB,aAAA,SAAaC,GAGX,MAFA,GAAG9nB,KAAKtE,MAAMK,KAAKqC,eAAgB0pB,GACnC/qB,EAAEgrB,SACKD,CACT,EAAClsB,EAED6qB,YAAA,WACE,OAAO/pB,IAAIwB,MACRwG,KAAK,kBAAiB,MAChB,WAAO,GACb3G,KAAKhC,KAAK8rB,aAAa7sB,KAAKe,MACjC,EAACwqB,CAAA,CA7E8B,CAASyB,KCR1C,MAAM,GAA+B3sB,OAAOC,KAAKC,OAAc,M,eCE1C0sB,GAAU,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAxsB,MAAA,KAAAC,YAAA,YAAAT,EAAA+sB,EAAAC,GAAAD,CAAA,EAASE,MACxChuB,OAAO8G,OAAOgnB,GAAWxtB,UAAW,CAClCqE,GAAIqpB,KAAAA,UAAgB,MACpB9rB,KAAM8rB,KAAAA,UAAgB,QACtB5rB,MAAO4rB,KAAAA,UAAgB,SACvBlpB,KAAMkpB,KAAAA,UAAgB,UCHxBzrB,IAAI0rB,aAAaC,IAAI,iCAAkC,WACrD3rB,IAAIwB,MAAMoqB,OAAOlqB,eAAiB6pB,GAClCvrB,IAAI6rB,cAAa,IAAK,kCAAkCC,aAAajC,GACvE,E", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/ExtensionPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LoadingIndicator']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Modal']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['utils/Stream']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/components/LinksQueueAddModal.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Component']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/components/LinksQueueDeleteModal.js", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/components/LinksQueueListItem.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/.pnpm/sortablejs@1.15.6/node_modules/sortablejs/modular/sortable.esm.js", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/components/SettingsPage.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Model']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/LinksQueue.js", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/ExtensionPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Modal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['utils/Stream'];", "import Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\nimport Stream from 'flarum/utils/Stream';\n\nexport default class LinksQueueAddModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.LinksQueueItemData = this.attrs.LinksQueueItemData;\n    this.settingType = \"add\";\n\n    if(this.LinksQueueItemData){\n      this.settingType = \"edit\";\n      this.itemName = Stream(this.LinksQueueItemData.name());\n      this.itemUrl = Stream(this.LinksQueueItemData.links());\n    }else{\n      this.itemName = Stream(\"\");\n      this.itemUrl = Stream(\"\");\n    }\n  }\n\n  className() {\n    return 'Modal--Medium';\n  }\n\n  title() {\n    return this.settingType===\"add\"?app.translator.trans('wusong8899-links-queue.admin.settings.item-add'):app.translator.trans('wusong8899-links-queue.admin.settings.item-edit');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            <div>\n              <div class=\"GuaGuaLeSettingsLabel\">{app.translator.trans('wusong8899-links-queue.admin.settings.item-name')}</div>\n              <input maxlength=\"255\" required className=\"FormControl\" bidi={this.itemName} />\n              <div class=\"GuaGuaLeSettingsLabel\">{app.translator.trans('wusong8899-links-queue.admin.settings.item-url')}</div>\n              <input maxlength=\"500\" required className=\"FormControl\" bidi={this.itemUrl} />\n            </div>\n          </div>\n\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                type: 'submit',\n                loading: this.loading,\n              },\n              this.settingType===\"add\"?app.translator.trans('wusong8899-links-queue.admin.data-add'):app.translator.trans('wusong8899-links-queue.admin.data-save')\n            )}&nbsp;\n            {Button.component(\n              {\n                className: 'Button guagualeButton--gray',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n                }\n              },\n              app.translator.trans('wusong8899-links-queue.admin.cancel')\n            )}\n          </div>\n\n        </div>\n      </div>\n    );\n  }\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    this.loading = true;\n\n    if(this.settingType===\"edit\"){\n      this.LinksQueueItemData.save({\n          name:this.itemName(),\n          url:this.itemUrl(),\n      })\n      .then(\n        () => this.hide(),\n        (response) => {\n          this.loading = false;\n          this.handleErrors(response);\n        }\n      );\n    }else{\n      app.store\n        .createRecord(\"linksQueueList\")\n        .save({\n          name:this.itemName(),\n          url:this.itemUrl(),\n        })\n        .then(\n          (linksQueueList) => {\n            location.reload();\n          }\n        )\n        .catch((e) => {\n          this.loading = false;\n          this.handleErrors(linksQueueList);\n        });\n    }\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Component'];", "import Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class LinksQueueDeleteModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.LinksQueueItemData = this.attrs.LinksQueueItemData;\n    this.loading = false;\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-links-queue.admin.settings.item-delete-confirmation');\n  }\n\n  content() {\n    //\n\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form-group\" style=\"text-align: center;\">\n          {Button.component(\n            {\n              className: 'Button Button--primary',\n              type: 'submit',\n              loading: this.loading,\n            },\n            app.translator.trans('wusong8899-links-queue.admin.confirm')\n          )}&nbsp;\n          {Button.component(\n            {\n              className: 'Button guagualeButton--gray',\n              loading: this.loading,\n              onclick: () => {\n                this.hide();\n              }\n            },\n            app.translator.trans('wusong8899-links-queue.admin.cancel')\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    this.loading = true;\n\n    this.LinksQueueItemData.delete()\n    .then(\n      (response) => {\n        location.reload();\n      }\n    );\n  }\n}\n", "import Component from \"flarum/Component\";\nimport Button from 'flarum/components/Button';\nimport LinksQueueAddModal from './LinksQueueAddModal';\nimport LinksQueueDeleteModal from './LinksQueueDeleteModal';\n\nexport default class LinksQueueListItem extends Component {\n  view() {\n    const {LinksQueueItemData} = this.attrs;\n    const linkID = LinksQueueItemData.id();\n    const linkName = LinksQueueItemData.name();\n    const linkUrl = LinksQueueItemData.links();\n    const linkSort = LinksQueueItemData.sort();\n\n    return (\n      <div style=\"border: 1px dotted var(--control-color);padding: 10px;border-radius: 4px;\">\n        <div>\n          <div style=\"padding-top: 5px;\">\n            <Button className={'Button Button--primary'} onclick={() => this.editItem(LinksQueueItemData)}>\n              {app.translator.trans('wusong8899-links-queue.admin.settings.item-edit')}\n            </Button>\n            &nbsp;\n            <Button style=\"font-weight:bold;width:66px;\" className={'Button Button--danger'} onclick={() => this.deleteItem(LinksQueueItemData)}>\n              {app.translator.trans('wusong8899-links-queue.admin.settings.item-delete')}\n            </Button>&nbsp;&nbsp;\n\n            <b>{app.translator.trans('wusong8899-links-queue.admin.settings.item-id')}: </b>\n            {linkID}&nbsp;|&nbsp;\n            <b>{app.translator.trans('wusong8899-links-queue.admin.settings.item-name')}: </b>\n            {linkName}&nbsp;|&nbsp;\n            <b>{app.translator.trans('wusong8899-links-queue.admin.settings.item-url')}: </b>\n            {linkUrl}&nbsp;\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  editItem(LinksQueueItemData) {\n    app.modal.show(LinksQueueAddModal, {LinksQueueItemData})\n  }\n\n  deleteItem(LinksQueueItemData) {\n    app.modal.show(LinksQueueDeleteModal, {LinksQueueItemData})\n  }\n}\n", "/**!\n * Sortable 1.15.6\n * <AUTHOR>   <<EMAIL>>\n * <AUTHOR>    <<EMAIL>>\n * @license MIT\n */\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar version = \"1.15.6\";\n\nfunction userAgent(pattern) {\n  if (typeof window !== 'undefined' && window.navigator) {\n    return !! /*@__PURE__*/navigator.userAgent.match(pattern);\n  }\n}\nvar IE11OrLess = userAgent(/(?:Trident.*rv[ :]?11\\.|msie|iemobile|Windows Phone)/i);\nvar Edge = userAgent(/Edge/i);\nvar FireFox = userAgent(/firefox/i);\nvar Safari = userAgent(/safari/i) && !userAgent(/chrome/i) && !userAgent(/android/i);\nvar IOS = userAgent(/iP(ad|od|hone)/i);\nvar ChromeForAndroid = userAgent(/chrome/i) && userAgent(/android/i);\n\nvar captureMode = {\n  capture: false,\n  passive: false\n};\nfunction on(el, event, fn) {\n  el.addEventListener(event, fn, !IE11OrLess && captureMode);\n}\nfunction off(el, event, fn) {\n  el.removeEventListener(event, fn, !IE11OrLess && captureMode);\n}\nfunction matches( /**HTMLElement*/el, /**String*/selector) {\n  if (!selector) return;\n  selector[0] === '>' && (selector = selector.substring(1));\n  if (el) {\n    try {\n      if (el.matches) {\n        return el.matches(selector);\n      } else if (el.msMatchesSelector) {\n        return el.msMatchesSelector(selector);\n      } else if (el.webkitMatchesSelector) {\n        return el.webkitMatchesSelector(selector);\n      }\n    } catch (_) {\n      return false;\n    }\n  }\n  return false;\n}\nfunction getParentOrHost(el) {\n  return el.host && el !== document && el.host.nodeType ? el.host : el.parentNode;\n}\nfunction closest( /**HTMLElement*/el, /**String*/selector, /**HTMLElement*/ctx, includeCTX) {\n  if (el) {\n    ctx = ctx || document;\n    do {\n      if (selector != null && (selector[0] === '>' ? el.parentNode === ctx && matches(el, selector) : matches(el, selector)) || includeCTX && el === ctx) {\n        return el;\n      }\n      if (el === ctx) break;\n      /* jshint boss:true */\n    } while (el = getParentOrHost(el));\n  }\n  return null;\n}\nvar R_SPACE = /\\s+/g;\nfunction toggleClass(el, name, state) {\n  if (el && name) {\n    if (el.classList) {\n      el.classList[state ? 'add' : 'remove'](name);\n    } else {\n      var className = (' ' + el.className + ' ').replace(R_SPACE, ' ').replace(' ' + name + ' ', ' ');\n      el.className = (className + (state ? ' ' + name : '')).replace(R_SPACE, ' ');\n    }\n  }\n}\nfunction css(el, prop, val) {\n  var style = el && el.style;\n  if (style) {\n    if (val === void 0) {\n      if (document.defaultView && document.defaultView.getComputedStyle) {\n        val = document.defaultView.getComputedStyle(el, '');\n      } else if (el.currentStyle) {\n        val = el.currentStyle;\n      }\n      return prop === void 0 ? val : val[prop];\n    } else {\n      if (!(prop in style) && prop.indexOf('webkit') === -1) {\n        prop = '-webkit-' + prop;\n      }\n      style[prop] = val + (typeof val === 'string' ? '' : 'px');\n    }\n  }\n}\nfunction matrix(el, selfOnly) {\n  var appliedTransforms = '';\n  if (typeof el === 'string') {\n    appliedTransforms = el;\n  } else {\n    do {\n      var transform = css(el, 'transform');\n      if (transform && transform !== 'none') {\n        appliedTransforms = transform + ' ' + appliedTransforms;\n      }\n      /* jshint boss:true */\n    } while (!selfOnly && (el = el.parentNode));\n  }\n  var matrixFn = window.DOMMatrix || window.WebKitCSSMatrix || window.CSSMatrix || window.MSCSSMatrix;\n  /*jshint -W056 */\n  return matrixFn && new matrixFn(appliedTransforms);\n}\nfunction find(ctx, tagName, iterator) {\n  if (ctx) {\n    var list = ctx.getElementsByTagName(tagName),\n      i = 0,\n      n = list.length;\n    if (iterator) {\n      for (; i < n; i++) {\n        iterator(list[i], i);\n      }\n    }\n    return list;\n  }\n  return [];\n}\nfunction getWindowScrollingElement() {\n  var scrollingElement = document.scrollingElement;\n  if (scrollingElement) {\n    return scrollingElement;\n  } else {\n    return document.documentElement;\n  }\n}\n\n/**\r\n * Returns the \"bounding client rect\" of given element\r\n * @param  {HTMLElement} el                       The element whose boundingClientRect is wanted\r\n * @param  {[Boolean]} relativeToContainingBlock  Whether the rect should be relative to the containing block of (including) the container\r\n * @param  {[Boolean]} relativeToNonStaticParent  Whether the rect should be relative to the relative parent of (including) the contaienr\r\n * @param  {[Boolean]} undoScale                  Whether the container's scale() should be undone\r\n * @param  {[HTMLElement]} container              The parent the element will be placed in\r\n * @return {Object}                               The boundingClientRect of el, with specified adjustments\r\n */\nfunction getRect(el, relativeToContainingBlock, relativeToNonStaticParent, undoScale, container) {\n  if (!el.getBoundingClientRect && el !== window) return;\n  var elRect, top, left, bottom, right, height, width;\n  if (el !== window && el.parentNode && el !== getWindowScrollingElement()) {\n    elRect = el.getBoundingClientRect();\n    top = elRect.top;\n    left = elRect.left;\n    bottom = elRect.bottom;\n    right = elRect.right;\n    height = elRect.height;\n    width = elRect.width;\n  } else {\n    top = 0;\n    left = 0;\n    bottom = window.innerHeight;\n    right = window.innerWidth;\n    height = window.innerHeight;\n    width = window.innerWidth;\n  }\n  if ((relativeToContainingBlock || relativeToNonStaticParent) && el !== window) {\n    // Adjust for translate()\n    container = container || el.parentNode;\n\n    // solves #1123 (see: https://stackoverflow.com/a/37953806/6088312)\n    // Not needed on <= IE11\n    if (!IE11OrLess) {\n      do {\n        if (container && container.getBoundingClientRect && (css(container, 'transform') !== 'none' || relativeToNonStaticParent && css(container, 'position') !== 'static')) {\n          var containerRect = container.getBoundingClientRect();\n\n          // Set relative to edges of padding box of container\n          top -= containerRect.top + parseInt(css(container, 'border-top-width'));\n          left -= containerRect.left + parseInt(css(container, 'border-left-width'));\n          bottom = top + elRect.height;\n          right = left + elRect.width;\n          break;\n        }\n        /* jshint boss:true */\n      } while (container = container.parentNode);\n    }\n  }\n  if (undoScale && el !== window) {\n    // Adjust for scale()\n    var elMatrix = matrix(container || el),\n      scaleX = elMatrix && elMatrix.a,\n      scaleY = elMatrix && elMatrix.d;\n    if (elMatrix) {\n      top /= scaleY;\n      left /= scaleX;\n      width /= scaleX;\n      height /= scaleY;\n      bottom = top + height;\n      right = left + width;\n    }\n  }\n  return {\n    top: top,\n    left: left,\n    bottom: bottom,\n    right: right,\n    width: width,\n    height: height\n  };\n}\n\n/**\r\n * Checks if a side of an element is scrolled past a side of its parents\r\n * @param  {HTMLElement}  el           The element who's side being scrolled out of view is in question\r\n * @param  {String}       elSide       Side of the element in question ('top', 'left', 'right', 'bottom')\r\n * @param  {String}       parentSide   Side of the parent in question ('top', 'left', 'right', 'bottom')\r\n * @return {HTMLElement}               The parent scroll element that the el's side is scrolled past, or null if there is no such element\r\n */\nfunction isScrolledPast(el, elSide, parentSide) {\n  var parent = getParentAutoScrollElement(el, true),\n    elSideVal = getRect(el)[elSide];\n\n  /* jshint boss:true */\n  while (parent) {\n    var parentSideVal = getRect(parent)[parentSide],\n      visible = void 0;\n    if (parentSide === 'top' || parentSide === 'left') {\n      visible = elSideVal >= parentSideVal;\n    } else {\n      visible = elSideVal <= parentSideVal;\n    }\n    if (!visible) return parent;\n    if (parent === getWindowScrollingElement()) break;\n    parent = getParentAutoScrollElement(parent, false);\n  }\n  return false;\n}\n\n/**\r\n * Gets nth child of el, ignoring hidden children, sortable's elements (does not ignore clone if it's visible)\r\n * and non-draggable elements\r\n * @param  {HTMLElement} el       The parent element\r\n * @param  {Number} childNum      The index of the child\r\n * @param  {Object} options       Parent Sortable's options\r\n * @return {HTMLElement}          The child at index childNum, or null if not found\r\n */\nfunction getChild(el, childNum, options, includeDragEl) {\n  var currentChild = 0,\n    i = 0,\n    children = el.children;\n  while (i < children.length) {\n    if (children[i].style.display !== 'none' && children[i] !== Sortable.ghost && (includeDragEl || children[i] !== Sortable.dragged) && closest(children[i], options.draggable, el, false)) {\n      if (currentChild === childNum) {\n        return children[i];\n      }\n      currentChild++;\n    }\n    i++;\n  }\n  return null;\n}\n\n/**\r\n * Gets the last child in the el, ignoring ghostEl or invisible elements (clones)\r\n * @param  {HTMLElement} el       Parent element\r\n * @param  {selector} selector    Any other elements that should be ignored\r\n * @return {HTMLElement}          The last child, ignoring ghostEl\r\n */\nfunction lastChild(el, selector) {\n  var last = el.lastElementChild;\n  while (last && (last === Sortable.ghost || css(last, 'display') === 'none' || selector && !matches(last, selector))) {\n    last = last.previousElementSibling;\n  }\n  return last || null;\n}\n\n/**\r\n * Returns the index of an element within its parent for a selected set of\r\n * elements\r\n * @param  {HTMLElement} el\r\n * @param  {selector} selector\r\n * @return {number}\r\n */\nfunction index(el, selector) {\n  var index = 0;\n  if (!el || !el.parentNode) {\n    return -1;\n  }\n\n  /* jshint boss:true */\n  while (el = el.previousElementSibling) {\n    if (el.nodeName.toUpperCase() !== 'TEMPLATE' && el !== Sortable.clone && (!selector || matches(el, selector))) {\n      index++;\n    }\n  }\n  return index;\n}\n\n/**\r\n * Returns the scroll offset of the given element, added with all the scroll offsets of parent elements.\r\n * The value is returned in real pixels.\r\n * @param  {HTMLElement} el\r\n * @return {Array}             Offsets in the format of [left, top]\r\n */\nfunction getRelativeScrollOffset(el) {\n  var offsetLeft = 0,\n    offsetTop = 0,\n    winScroller = getWindowScrollingElement();\n  if (el) {\n    do {\n      var elMatrix = matrix(el),\n        scaleX = elMatrix.a,\n        scaleY = elMatrix.d;\n      offsetLeft += el.scrollLeft * scaleX;\n      offsetTop += el.scrollTop * scaleY;\n    } while (el !== winScroller && (el = el.parentNode));\n  }\n  return [offsetLeft, offsetTop];\n}\n\n/**\r\n * Returns the index of the object within the given array\r\n * @param  {Array} arr   Array that may or may not hold the object\r\n * @param  {Object} obj  An object that has a key-value pair unique to and identical to a key-value pair in the object you want to find\r\n * @return {Number}      The index of the object in the array, or -1\r\n */\nfunction indexOfObject(arr, obj) {\n  for (var i in arr) {\n    if (!arr.hasOwnProperty(i)) continue;\n    for (var key in obj) {\n      if (obj.hasOwnProperty(key) && obj[key] === arr[i][key]) return Number(i);\n    }\n  }\n  return -1;\n}\nfunction getParentAutoScrollElement(el, includeSelf) {\n  // skip to window\n  if (!el || !el.getBoundingClientRect) return getWindowScrollingElement();\n  var elem = el;\n  var gotSelf = false;\n  do {\n    // we don't need to get elem css if it isn't even overflowing in the first place (performance)\n    if (elem.clientWidth < elem.scrollWidth || elem.clientHeight < elem.scrollHeight) {\n      var elemCSS = css(elem);\n      if (elem.clientWidth < elem.scrollWidth && (elemCSS.overflowX == 'auto' || elemCSS.overflowX == 'scroll') || elem.clientHeight < elem.scrollHeight && (elemCSS.overflowY == 'auto' || elemCSS.overflowY == 'scroll')) {\n        if (!elem.getBoundingClientRect || elem === document.body) return getWindowScrollingElement();\n        if (gotSelf || includeSelf) return elem;\n        gotSelf = true;\n      }\n    }\n    /* jshint boss:true */\n  } while (elem = elem.parentNode);\n  return getWindowScrollingElement();\n}\nfunction extend(dst, src) {\n  if (dst && src) {\n    for (var key in src) {\n      if (src.hasOwnProperty(key)) {\n        dst[key] = src[key];\n      }\n    }\n  }\n  return dst;\n}\nfunction isRectEqual(rect1, rect2) {\n  return Math.round(rect1.top) === Math.round(rect2.top) && Math.round(rect1.left) === Math.round(rect2.left) && Math.round(rect1.height) === Math.round(rect2.height) && Math.round(rect1.width) === Math.round(rect2.width);\n}\nvar _throttleTimeout;\nfunction throttle(callback, ms) {\n  return function () {\n    if (!_throttleTimeout) {\n      var args = arguments,\n        _this = this;\n      if (args.length === 1) {\n        callback.call(_this, args[0]);\n      } else {\n        callback.apply(_this, args);\n      }\n      _throttleTimeout = setTimeout(function () {\n        _throttleTimeout = void 0;\n      }, ms);\n    }\n  };\n}\nfunction cancelThrottle() {\n  clearTimeout(_throttleTimeout);\n  _throttleTimeout = void 0;\n}\nfunction scrollBy(el, x, y) {\n  el.scrollLeft += x;\n  el.scrollTop += y;\n}\nfunction clone(el) {\n  var Polymer = window.Polymer;\n  var $ = window.jQuery || window.Zepto;\n  if (Polymer && Polymer.dom) {\n    return Polymer.dom(el).cloneNode(true);\n  } else if ($) {\n    return $(el).clone(true)[0];\n  } else {\n    return el.cloneNode(true);\n  }\n}\nfunction setRect(el, rect) {\n  css(el, 'position', 'absolute');\n  css(el, 'top', rect.top);\n  css(el, 'left', rect.left);\n  css(el, 'width', rect.width);\n  css(el, 'height', rect.height);\n}\nfunction unsetRect(el) {\n  css(el, 'position', '');\n  css(el, 'top', '');\n  css(el, 'left', '');\n  css(el, 'width', '');\n  css(el, 'height', '');\n}\nfunction getChildContainingRectFromElement(container, options, ghostEl) {\n  var rect = {};\n  Array.from(container.children).forEach(function (child) {\n    var _rect$left, _rect$top, _rect$right, _rect$bottom;\n    if (!closest(child, options.draggable, container, false) || child.animated || child === ghostEl) return;\n    var childRect = getRect(child);\n    rect.left = Math.min((_rect$left = rect.left) !== null && _rect$left !== void 0 ? _rect$left : Infinity, childRect.left);\n    rect.top = Math.min((_rect$top = rect.top) !== null && _rect$top !== void 0 ? _rect$top : Infinity, childRect.top);\n    rect.right = Math.max((_rect$right = rect.right) !== null && _rect$right !== void 0 ? _rect$right : -Infinity, childRect.right);\n    rect.bottom = Math.max((_rect$bottom = rect.bottom) !== null && _rect$bottom !== void 0 ? _rect$bottom : -Infinity, childRect.bottom);\n  });\n  rect.width = rect.right - rect.left;\n  rect.height = rect.bottom - rect.top;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\nvar expando = 'Sortable' + new Date().getTime();\n\nfunction AnimationStateManager() {\n  var animationStates = [],\n    animationCallbackId;\n  return {\n    captureAnimationState: function captureAnimationState() {\n      animationStates = [];\n      if (!this.options.animation) return;\n      var children = [].slice.call(this.el.children);\n      children.forEach(function (child) {\n        if (css(child, 'display') === 'none' || child === Sortable.ghost) return;\n        animationStates.push({\n          target: child,\n          rect: getRect(child)\n        });\n        var fromRect = _objectSpread2({}, animationStates[animationStates.length - 1].rect);\n\n        // If animating: compensate for current animation\n        if (child.thisAnimationDuration) {\n          var childMatrix = matrix(child, true);\n          if (childMatrix) {\n            fromRect.top -= childMatrix.f;\n            fromRect.left -= childMatrix.e;\n          }\n        }\n        child.fromRect = fromRect;\n      });\n    },\n    addAnimationState: function addAnimationState(state) {\n      animationStates.push(state);\n    },\n    removeAnimationState: function removeAnimationState(target) {\n      animationStates.splice(indexOfObject(animationStates, {\n        target: target\n      }), 1);\n    },\n    animateAll: function animateAll(callback) {\n      var _this = this;\n      if (!this.options.animation) {\n        clearTimeout(animationCallbackId);\n        if (typeof callback === 'function') callback();\n        return;\n      }\n      var animating = false,\n        animationTime = 0;\n      animationStates.forEach(function (state) {\n        var time = 0,\n          target = state.target,\n          fromRect = target.fromRect,\n          toRect = getRect(target),\n          prevFromRect = target.prevFromRect,\n          prevToRect = target.prevToRect,\n          animatingRect = state.rect,\n          targetMatrix = matrix(target, true);\n        if (targetMatrix) {\n          // Compensate for current animation\n          toRect.top -= targetMatrix.f;\n          toRect.left -= targetMatrix.e;\n        }\n        target.toRect = toRect;\n        if (target.thisAnimationDuration) {\n          // Could also check if animatingRect is between fromRect and toRect\n          if (isRectEqual(prevFromRect, toRect) && !isRectEqual(fromRect, toRect) &&\n          // Make sure animatingRect is on line between toRect & fromRect\n          (animatingRect.top - toRect.top) / (animatingRect.left - toRect.left) === (fromRect.top - toRect.top) / (fromRect.left - toRect.left)) {\n            // If returning to same place as started from animation and on same axis\n            time = calculateRealTime(animatingRect, prevFromRect, prevToRect, _this.options);\n          }\n        }\n\n        // if fromRect != toRect: animate\n        if (!isRectEqual(toRect, fromRect)) {\n          target.prevFromRect = fromRect;\n          target.prevToRect = toRect;\n          if (!time) {\n            time = _this.options.animation;\n          }\n          _this.animate(target, animatingRect, toRect, time);\n        }\n        if (time) {\n          animating = true;\n          animationTime = Math.max(animationTime, time);\n          clearTimeout(target.animationResetTimer);\n          target.animationResetTimer = setTimeout(function () {\n            target.animationTime = 0;\n            target.prevFromRect = null;\n            target.fromRect = null;\n            target.prevToRect = null;\n            target.thisAnimationDuration = null;\n          }, time);\n          target.thisAnimationDuration = time;\n        }\n      });\n      clearTimeout(animationCallbackId);\n      if (!animating) {\n        if (typeof callback === 'function') callback();\n      } else {\n        animationCallbackId = setTimeout(function () {\n          if (typeof callback === 'function') callback();\n        }, animationTime);\n      }\n      animationStates = [];\n    },\n    animate: function animate(target, currentRect, toRect, duration) {\n      if (duration) {\n        css(target, 'transition', '');\n        css(target, 'transform', '');\n        var elMatrix = matrix(this.el),\n          scaleX = elMatrix && elMatrix.a,\n          scaleY = elMatrix && elMatrix.d,\n          translateX = (currentRect.left - toRect.left) / (scaleX || 1),\n          translateY = (currentRect.top - toRect.top) / (scaleY || 1);\n        target.animatingX = !!translateX;\n        target.animatingY = !!translateY;\n        css(target, 'transform', 'translate3d(' + translateX + 'px,' + translateY + 'px,0)');\n        this.forRepaintDummy = repaint(target); // repaint\n\n        css(target, 'transition', 'transform ' + duration + 'ms' + (this.options.easing ? ' ' + this.options.easing : ''));\n        css(target, 'transform', 'translate3d(0,0,0)');\n        typeof target.animated === 'number' && clearTimeout(target.animated);\n        target.animated = setTimeout(function () {\n          css(target, 'transition', '');\n          css(target, 'transform', '');\n          target.animated = false;\n          target.animatingX = false;\n          target.animatingY = false;\n        }, duration);\n      }\n    }\n  };\n}\nfunction repaint(target) {\n  return target.offsetWidth;\n}\nfunction calculateRealTime(animatingRect, fromRect, toRect, options) {\n  return Math.sqrt(Math.pow(fromRect.top - animatingRect.top, 2) + Math.pow(fromRect.left - animatingRect.left, 2)) / Math.sqrt(Math.pow(fromRect.top - toRect.top, 2) + Math.pow(fromRect.left - toRect.left, 2)) * options.animation;\n}\n\nvar plugins = [];\nvar defaults = {\n  initializeByDefault: true\n};\nvar PluginManager = {\n  mount: function mount(plugin) {\n    // Set default static properties\n    for (var option in defaults) {\n      if (defaults.hasOwnProperty(option) && !(option in plugin)) {\n        plugin[option] = defaults[option];\n      }\n    }\n    plugins.forEach(function (p) {\n      if (p.pluginName === plugin.pluginName) {\n        throw \"Sortable: Cannot mount plugin \".concat(plugin.pluginName, \" more than once\");\n      }\n    });\n    plugins.push(plugin);\n  },\n  pluginEvent: function pluginEvent(eventName, sortable, evt) {\n    var _this = this;\n    this.eventCanceled = false;\n    evt.cancel = function () {\n      _this.eventCanceled = true;\n    };\n    var eventNameGlobal = eventName + 'Global';\n    plugins.forEach(function (plugin) {\n      if (!sortable[plugin.pluginName]) return;\n      // Fire global events if it exists in this sortable\n      if (sortable[plugin.pluginName][eventNameGlobal]) {\n        sortable[plugin.pluginName][eventNameGlobal](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n\n      // Only fire plugin event if plugin is enabled in this sortable,\n      // and plugin has event defined\n      if (sortable.options[plugin.pluginName] && sortable[plugin.pluginName][eventName]) {\n        sortable[plugin.pluginName][eventName](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n    });\n  },\n  initializePlugins: function initializePlugins(sortable, el, defaults, options) {\n    plugins.forEach(function (plugin) {\n      var pluginName = plugin.pluginName;\n      if (!sortable.options[pluginName] && !plugin.initializeByDefault) return;\n      var initialized = new plugin(sortable, el, sortable.options);\n      initialized.sortable = sortable;\n      initialized.options = sortable.options;\n      sortable[pluginName] = initialized;\n\n      // Add default options from plugin\n      _extends(defaults, initialized.defaults);\n    });\n    for (var option in sortable.options) {\n      if (!sortable.options.hasOwnProperty(option)) continue;\n      var modified = this.modifyOption(sortable, option, sortable.options[option]);\n      if (typeof modified !== 'undefined') {\n        sortable.options[option] = modified;\n      }\n    }\n  },\n  getEventProperties: function getEventProperties(name, sortable) {\n    var eventProperties = {};\n    plugins.forEach(function (plugin) {\n      if (typeof plugin.eventProperties !== 'function') return;\n      _extends(eventProperties, plugin.eventProperties.call(sortable[plugin.pluginName], name));\n    });\n    return eventProperties;\n  },\n  modifyOption: function modifyOption(sortable, name, value) {\n    var modifiedValue;\n    plugins.forEach(function (plugin) {\n      // Plugin must exist on the Sortable\n      if (!sortable[plugin.pluginName]) return;\n\n      // If static option listener exists for this option, call in the context of the Sortable's instance of this plugin\n      if (plugin.optionListeners && typeof plugin.optionListeners[name] === 'function') {\n        modifiedValue = plugin.optionListeners[name].call(sortable[plugin.pluginName], value);\n      }\n    });\n    return modifiedValue;\n  }\n};\n\nfunction dispatchEvent(_ref) {\n  var sortable = _ref.sortable,\n    rootEl = _ref.rootEl,\n    name = _ref.name,\n    targetEl = _ref.targetEl,\n    cloneEl = _ref.cloneEl,\n    toEl = _ref.toEl,\n    fromEl = _ref.fromEl,\n    oldIndex = _ref.oldIndex,\n    newIndex = _ref.newIndex,\n    oldDraggableIndex = _ref.oldDraggableIndex,\n    newDraggableIndex = _ref.newDraggableIndex,\n    originalEvent = _ref.originalEvent,\n    putSortable = _ref.putSortable,\n    extraEventProperties = _ref.extraEventProperties;\n  sortable = sortable || rootEl && rootEl[expando];\n  if (!sortable) return;\n  var evt,\n    options = sortable.options,\n    onName = 'on' + name.charAt(0).toUpperCase() + name.substr(1);\n  // Support for new CustomEvent feature\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent(name, {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent(name, true, true);\n  }\n  evt.to = toEl || rootEl;\n  evt.from = fromEl || rootEl;\n  evt.item = targetEl || rootEl;\n  evt.clone = cloneEl;\n  evt.oldIndex = oldIndex;\n  evt.newIndex = newIndex;\n  evt.oldDraggableIndex = oldDraggableIndex;\n  evt.newDraggableIndex = newDraggableIndex;\n  evt.originalEvent = originalEvent;\n  evt.pullMode = putSortable ? putSortable.lastPutMode : undefined;\n  var allEventProperties = _objectSpread2(_objectSpread2({}, extraEventProperties), PluginManager.getEventProperties(name, sortable));\n  for (var option in allEventProperties) {\n    evt[option] = allEventProperties[option];\n  }\n  if (rootEl) {\n    rootEl.dispatchEvent(evt);\n  }\n  if (options[onName]) {\n    options[onName].call(sortable, evt);\n  }\n}\n\nvar _excluded = [\"evt\"];\nvar pluginEvent = function pluginEvent(eventName, sortable) {\n  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n    originalEvent = _ref.evt,\n    data = _objectWithoutProperties(_ref, _excluded);\n  PluginManager.pluginEvent.bind(Sortable)(eventName, sortable, _objectSpread2({\n    dragEl: dragEl,\n    parentEl: parentEl,\n    ghostEl: ghostEl,\n    rootEl: rootEl,\n    nextEl: nextEl,\n    lastDownEl: lastDownEl,\n    cloneEl: cloneEl,\n    cloneHidden: cloneHidden,\n    dragStarted: moved,\n    putSortable: putSortable,\n    activeSortable: Sortable.active,\n    originalEvent: originalEvent,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex,\n    hideGhostForTarget: _hideGhostForTarget,\n    unhideGhostForTarget: _unhideGhostForTarget,\n    cloneNowHidden: function cloneNowHidden() {\n      cloneHidden = true;\n    },\n    cloneNowShown: function cloneNowShown() {\n      cloneHidden = false;\n    },\n    dispatchSortableEvent: function dispatchSortableEvent(name) {\n      _dispatchEvent({\n        sortable: sortable,\n        name: name,\n        originalEvent: originalEvent\n      });\n    }\n  }, data));\n};\nfunction _dispatchEvent(info) {\n  dispatchEvent(_objectSpread2({\n    putSortable: putSortable,\n    cloneEl: cloneEl,\n    targetEl: dragEl,\n    rootEl: rootEl,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex\n  }, info));\n}\nvar dragEl,\n  parentEl,\n  ghostEl,\n  rootEl,\n  nextEl,\n  lastDownEl,\n  cloneEl,\n  cloneHidden,\n  oldIndex,\n  newIndex,\n  oldDraggableIndex,\n  newDraggableIndex,\n  activeGroup,\n  putSortable,\n  awaitingDragStarted = false,\n  ignoreNextClick = false,\n  sortables = [],\n  tapEvt,\n  touchEvt,\n  lastDx,\n  lastDy,\n  tapDistanceLeft,\n  tapDistanceTop,\n  moved,\n  lastTarget,\n  lastDirection,\n  pastFirstInvertThresh = false,\n  isCircumstantialInvert = false,\n  targetMoveDistance,\n  // For positioning ghost absolutely\n  ghostRelativeParent,\n  ghostRelativeParentInitialScroll = [],\n  // (left, top)\n\n  _silent = false,\n  savedInputChecked = [];\n\n/** @const */\nvar documentExists = typeof document !== 'undefined',\n  PositionGhostAbsolutely = IOS,\n  CSSFloatProperty = Edge || IE11OrLess ? 'cssFloat' : 'float',\n  // This will not pass for IE9, because IE9 DnD only works on anchors\n  supportDraggable = documentExists && !ChromeForAndroid && !IOS && 'draggable' in document.createElement('div'),\n  supportCssPointerEvents = function () {\n    if (!documentExists) return;\n    // false when <= IE11\n    if (IE11OrLess) {\n      return false;\n    }\n    var el = document.createElement('x');\n    el.style.cssText = 'pointer-events:auto';\n    return el.style.pointerEvents === 'auto';\n  }(),\n  _detectDirection = function _detectDirection(el, options) {\n    var elCSS = css(el),\n      elWidth = parseInt(elCSS.width) - parseInt(elCSS.paddingLeft) - parseInt(elCSS.paddingRight) - parseInt(elCSS.borderLeftWidth) - parseInt(elCSS.borderRightWidth),\n      child1 = getChild(el, 0, options),\n      child2 = getChild(el, 1, options),\n      firstChildCSS = child1 && css(child1),\n      secondChildCSS = child2 && css(child2),\n      firstChildWidth = firstChildCSS && parseInt(firstChildCSS.marginLeft) + parseInt(firstChildCSS.marginRight) + getRect(child1).width,\n      secondChildWidth = secondChildCSS && parseInt(secondChildCSS.marginLeft) + parseInt(secondChildCSS.marginRight) + getRect(child2).width;\n    if (elCSS.display === 'flex') {\n      return elCSS.flexDirection === 'column' || elCSS.flexDirection === 'column-reverse' ? 'vertical' : 'horizontal';\n    }\n    if (elCSS.display === 'grid') {\n      return elCSS.gridTemplateColumns.split(' ').length <= 1 ? 'vertical' : 'horizontal';\n    }\n    if (child1 && firstChildCSS[\"float\"] && firstChildCSS[\"float\"] !== 'none') {\n      var touchingSideChild2 = firstChildCSS[\"float\"] === 'left' ? 'left' : 'right';\n      return child2 && (secondChildCSS.clear === 'both' || secondChildCSS.clear === touchingSideChild2) ? 'vertical' : 'horizontal';\n    }\n    return child1 && (firstChildCSS.display === 'block' || firstChildCSS.display === 'flex' || firstChildCSS.display === 'table' || firstChildCSS.display === 'grid' || firstChildWidth >= elWidth && elCSS[CSSFloatProperty] === 'none' || child2 && elCSS[CSSFloatProperty] === 'none' && firstChildWidth + secondChildWidth > elWidth) ? 'vertical' : 'horizontal';\n  },\n  _dragElInRowColumn = function _dragElInRowColumn(dragRect, targetRect, vertical) {\n    var dragElS1Opp = vertical ? dragRect.left : dragRect.top,\n      dragElS2Opp = vertical ? dragRect.right : dragRect.bottom,\n      dragElOppLength = vertical ? dragRect.width : dragRect.height,\n      targetS1Opp = vertical ? targetRect.left : targetRect.top,\n      targetS2Opp = vertical ? targetRect.right : targetRect.bottom,\n      targetOppLength = vertical ? targetRect.width : targetRect.height;\n    return dragElS1Opp === targetS1Opp || dragElS2Opp === targetS2Opp || dragElS1Opp + dragElOppLength / 2 === targetS1Opp + targetOppLength / 2;\n  },\n  /**\r\n   * Detects first nearest empty sortable to X and Y position using emptyInsertThreshold.\r\n   * @param  {Number} x      X position\r\n   * @param  {Number} y      Y position\r\n   * @return {HTMLElement}   Element of the first found nearest Sortable\r\n   */\n  _detectNearestEmptySortable = function _detectNearestEmptySortable(x, y) {\n    var ret;\n    sortables.some(function (sortable) {\n      var threshold = sortable[expando].options.emptyInsertThreshold;\n      if (!threshold || lastChild(sortable)) return;\n      var rect = getRect(sortable),\n        insideHorizontally = x >= rect.left - threshold && x <= rect.right + threshold,\n        insideVertically = y >= rect.top - threshold && y <= rect.bottom + threshold;\n      if (insideHorizontally && insideVertically) {\n        return ret = sortable;\n      }\n    });\n    return ret;\n  },\n  _prepareGroup = function _prepareGroup(options) {\n    function toFn(value, pull) {\n      return function (to, from, dragEl, evt) {\n        var sameGroup = to.options.group.name && from.options.group.name && to.options.group.name === from.options.group.name;\n        if (value == null && (pull || sameGroup)) {\n          // Default pull value\n          // Default pull and put value if same group\n          return true;\n        } else if (value == null || value === false) {\n          return false;\n        } else if (pull && value === 'clone') {\n          return value;\n        } else if (typeof value === 'function') {\n          return toFn(value(to, from, dragEl, evt), pull)(to, from, dragEl, evt);\n        } else {\n          var otherGroup = (pull ? to : from).options.group.name;\n          return value === true || typeof value === 'string' && value === otherGroup || value.join && value.indexOf(otherGroup) > -1;\n        }\n      };\n    }\n    var group = {};\n    var originalGroup = options.group;\n    if (!originalGroup || _typeof(originalGroup) != 'object') {\n      originalGroup = {\n        name: originalGroup\n      };\n    }\n    group.name = originalGroup.name;\n    group.checkPull = toFn(originalGroup.pull, true);\n    group.checkPut = toFn(originalGroup.put);\n    group.revertClone = originalGroup.revertClone;\n    options.group = group;\n  },\n  _hideGhostForTarget = function _hideGhostForTarget() {\n    if (!supportCssPointerEvents && ghostEl) {\n      css(ghostEl, 'display', 'none');\n    }\n  },\n  _unhideGhostForTarget = function _unhideGhostForTarget() {\n    if (!supportCssPointerEvents && ghostEl) {\n      css(ghostEl, 'display', '');\n    }\n  };\n\n// #1184 fix - Prevent click event on fallback if dragged but item not changed position\nif (documentExists && !ChromeForAndroid) {\n  document.addEventListener('click', function (evt) {\n    if (ignoreNextClick) {\n      evt.preventDefault();\n      evt.stopPropagation && evt.stopPropagation();\n      evt.stopImmediatePropagation && evt.stopImmediatePropagation();\n      ignoreNextClick = false;\n      return false;\n    }\n  }, true);\n}\nvar nearestEmptyInsertDetectEvent = function nearestEmptyInsertDetectEvent(evt) {\n  if (dragEl) {\n    evt = evt.touches ? evt.touches[0] : evt;\n    var nearest = _detectNearestEmptySortable(evt.clientX, evt.clientY);\n    if (nearest) {\n      // Create imitation event\n      var event = {};\n      for (var i in evt) {\n        if (evt.hasOwnProperty(i)) {\n          event[i] = evt[i];\n        }\n      }\n      event.target = event.rootEl = nearest;\n      event.preventDefault = void 0;\n      event.stopPropagation = void 0;\n      nearest[expando]._onDragOver(event);\n    }\n  }\n};\nvar _checkOutsideTargetEl = function _checkOutsideTargetEl(evt) {\n  if (dragEl) {\n    dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n  }\n};\n\n/**\r\n * @class  Sortable\r\n * @param  {HTMLElement}  el\r\n * @param  {Object}       [options]\r\n */\nfunction Sortable(el, options) {\n  if (!(el && el.nodeType && el.nodeType === 1)) {\n    throw \"Sortable: `el` must be an HTMLElement, not \".concat({}.toString.call(el));\n  }\n  this.el = el; // root element\n  this.options = options = _extends({}, options);\n\n  // Export instance\n  el[expando] = this;\n  var defaults = {\n    group: null,\n    sort: true,\n    disabled: false,\n    store: null,\n    handle: null,\n    draggable: /^[uo]l$/i.test(el.nodeName) ? '>li' : '>*',\n    swapThreshold: 1,\n    // percentage; 0 <= x <= 1\n    invertSwap: false,\n    // invert always\n    invertedSwapThreshold: null,\n    // will be set to same as swapThreshold if default\n    removeCloneOnHide: true,\n    direction: function direction() {\n      return _detectDirection(el, this.options);\n    },\n    ghostClass: 'sortable-ghost',\n    chosenClass: 'sortable-chosen',\n    dragClass: 'sortable-drag',\n    ignore: 'a, img',\n    filter: null,\n    preventOnFilter: true,\n    animation: 0,\n    easing: null,\n    setData: function setData(dataTransfer, dragEl) {\n      dataTransfer.setData('Text', dragEl.textContent);\n    },\n    dropBubble: false,\n    dragoverBubble: false,\n    dataIdAttr: 'data-id',\n    delay: 0,\n    delayOnTouchOnly: false,\n    touchStartThreshold: (Number.parseInt ? Number : window).parseInt(window.devicePixelRatio, 10) || 1,\n    forceFallback: false,\n    fallbackClass: 'sortable-fallback',\n    fallbackOnBody: false,\n    fallbackTolerance: 0,\n    fallbackOffset: {\n      x: 0,\n      y: 0\n    },\n    // Disabled on Safari: #1571; Enabled on Safari IOS: #2244\n    supportPointer: Sortable.supportPointer !== false && 'PointerEvent' in window && (!Safari || IOS),\n    emptyInsertThreshold: 5\n  };\n  PluginManager.initializePlugins(this, el, defaults);\n\n  // Set default options\n  for (var name in defaults) {\n    !(name in options) && (options[name] = defaults[name]);\n  }\n  _prepareGroup(options);\n\n  // Bind all private methods\n  for (var fn in this) {\n    if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n      this[fn] = this[fn].bind(this);\n    }\n  }\n\n  // Setup drag mode\n  this.nativeDraggable = options.forceFallback ? false : supportDraggable;\n  if (this.nativeDraggable) {\n    // Touch start threshold cannot be greater than the native dragstart threshold\n    this.options.touchStartThreshold = 1;\n  }\n\n  // Bind events\n  if (options.supportPointer) {\n    on(el, 'pointerdown', this._onTapStart);\n  } else {\n    on(el, 'mousedown', this._onTapStart);\n    on(el, 'touchstart', this._onTapStart);\n  }\n  if (this.nativeDraggable) {\n    on(el, 'dragover', this);\n    on(el, 'dragenter', this);\n  }\n  sortables.push(this.el);\n\n  // Restore sorting\n  options.store && options.store.get && this.sort(options.store.get(this) || []);\n\n  // Add animation state manager\n  _extends(this, AnimationStateManager());\n}\nSortable.prototype = /** @lends Sortable.prototype */{\n  constructor: Sortable,\n  _isOutsideThisEl: function _isOutsideThisEl(target) {\n    if (!this.el.contains(target) && target !== this.el) {\n      lastTarget = null;\n    }\n  },\n  _getDirection: function _getDirection(evt, target) {\n    return typeof this.options.direction === 'function' ? this.options.direction.call(this, evt, target, dragEl) : this.options.direction;\n  },\n  _onTapStart: function _onTapStart( /** Event|TouchEvent */evt) {\n    if (!evt.cancelable) return;\n    var _this = this,\n      el = this.el,\n      options = this.options,\n      preventOnFilter = options.preventOnFilter,\n      type = evt.type,\n      touch = evt.touches && evt.touches[0] || evt.pointerType && evt.pointerType === 'touch' && evt,\n      target = (touch || evt).target,\n      originalTarget = evt.target.shadowRoot && (evt.path && evt.path[0] || evt.composedPath && evt.composedPath()[0]) || target,\n      filter = options.filter;\n    _saveInputCheckedState(el);\n\n    // Don't trigger start event when an element is been dragged, otherwise the evt.oldindex always wrong when set option.group.\n    if (dragEl) {\n      return;\n    }\n    if (/mousedown|pointerdown/.test(type) && evt.button !== 0 || options.disabled) {\n      return; // only left button and enabled\n    }\n\n    // cancel dnd if original target is content editable\n    if (originalTarget.isContentEditable) {\n      return;\n    }\n\n    // Safari ignores further event handling after mousedown\n    if (!this.nativeDraggable && Safari && target && target.tagName.toUpperCase() === 'SELECT') {\n      return;\n    }\n    target = closest(target, options.draggable, el, false);\n    if (target && target.animated) {\n      return;\n    }\n    if (lastDownEl === target) {\n      // Ignoring duplicate `down`\n      return;\n    }\n\n    // Get the index of the dragged element within its parent\n    oldIndex = index(target);\n    oldDraggableIndex = index(target, options.draggable);\n\n    // Check filter\n    if (typeof filter === 'function') {\n      if (filter.call(this, evt, target, this)) {\n        _dispatchEvent({\n          sortable: _this,\n          rootEl: originalTarget,\n          name: 'filter',\n          targetEl: target,\n          toEl: el,\n          fromEl: el\n        });\n        pluginEvent('filter', _this, {\n          evt: evt\n        });\n        preventOnFilter && evt.preventDefault();\n        return; // cancel dnd\n      }\n    } else if (filter) {\n      filter = filter.split(',').some(function (criteria) {\n        criteria = closest(originalTarget, criteria.trim(), el, false);\n        if (criteria) {\n          _dispatchEvent({\n            sortable: _this,\n            rootEl: criteria,\n            name: 'filter',\n            targetEl: target,\n            fromEl: el,\n            toEl: el\n          });\n          pluginEvent('filter', _this, {\n            evt: evt\n          });\n          return true;\n        }\n      });\n      if (filter) {\n        preventOnFilter && evt.preventDefault();\n        return; // cancel dnd\n      }\n    }\n    if (options.handle && !closest(originalTarget, options.handle, el, false)) {\n      return;\n    }\n\n    // Prepare `dragstart`\n    this._prepareDragStart(evt, touch, target);\n  },\n  _prepareDragStart: function _prepareDragStart( /** Event */evt, /** Touch */touch, /** HTMLElement */target) {\n    var _this = this,\n      el = _this.el,\n      options = _this.options,\n      ownerDocument = el.ownerDocument,\n      dragStartFn;\n    if (target && !dragEl && target.parentNode === el) {\n      var dragRect = getRect(target);\n      rootEl = el;\n      dragEl = target;\n      parentEl = dragEl.parentNode;\n      nextEl = dragEl.nextSibling;\n      lastDownEl = target;\n      activeGroup = options.group;\n      Sortable.dragged = dragEl;\n      tapEvt = {\n        target: dragEl,\n        clientX: (touch || evt).clientX,\n        clientY: (touch || evt).clientY\n      };\n      tapDistanceLeft = tapEvt.clientX - dragRect.left;\n      tapDistanceTop = tapEvt.clientY - dragRect.top;\n      this._lastX = (touch || evt).clientX;\n      this._lastY = (touch || evt).clientY;\n      dragEl.style['will-change'] = 'all';\n      dragStartFn = function dragStartFn() {\n        pluginEvent('delayEnded', _this, {\n          evt: evt\n        });\n        if (Sortable.eventCanceled) {\n          _this._onDrop();\n          return;\n        }\n        // Delayed drag has been triggered\n        // we can re-enable the events: touchmove/mousemove\n        _this._disableDelayedDragEvents();\n        if (!FireFox && _this.nativeDraggable) {\n          dragEl.draggable = true;\n        }\n\n        // Bind the events: dragstart/dragend\n        _this._triggerDragStart(evt, touch);\n\n        // Drag start event\n        _dispatchEvent({\n          sortable: _this,\n          name: 'choose',\n          originalEvent: evt\n        });\n\n        // Chosen item\n        toggleClass(dragEl, options.chosenClass, true);\n      };\n\n      // Disable \"draggable\"\n      options.ignore.split(',').forEach(function (criteria) {\n        find(dragEl, criteria.trim(), _disableDraggable);\n      });\n      on(ownerDocument, 'dragover', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'mousemove', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'touchmove', nearestEmptyInsertDetectEvent);\n      if (options.supportPointer) {\n        on(ownerDocument, 'pointerup', _this._onDrop);\n        // Native D&D triggers pointercancel\n        !this.nativeDraggable && on(ownerDocument, 'pointercancel', _this._onDrop);\n      } else {\n        on(ownerDocument, 'mouseup', _this._onDrop);\n        on(ownerDocument, 'touchend', _this._onDrop);\n        on(ownerDocument, 'touchcancel', _this._onDrop);\n      }\n\n      // Make dragEl draggable (must be before delay for FireFox)\n      if (FireFox && this.nativeDraggable) {\n        this.options.touchStartThreshold = 4;\n        dragEl.draggable = true;\n      }\n      pluginEvent('delayStart', this, {\n        evt: evt\n      });\n\n      // Delay is impossible for native DnD in Edge or IE\n      if (options.delay && (!options.delayOnTouchOnly || touch) && (!this.nativeDraggable || !(Edge || IE11OrLess))) {\n        if (Sortable.eventCanceled) {\n          this._onDrop();\n          return;\n        }\n        // If the user moves the pointer or let go the click or touch\n        // before the delay has been reached:\n        // disable the delayed drag\n        if (options.supportPointer) {\n          on(ownerDocument, 'pointerup', _this._disableDelayedDrag);\n          on(ownerDocument, 'pointercancel', _this._disableDelayedDrag);\n        } else {\n          on(ownerDocument, 'mouseup', _this._disableDelayedDrag);\n          on(ownerDocument, 'touchend', _this._disableDelayedDrag);\n          on(ownerDocument, 'touchcancel', _this._disableDelayedDrag);\n        }\n        on(ownerDocument, 'mousemove', _this._delayedDragTouchMoveHandler);\n        on(ownerDocument, 'touchmove', _this._delayedDragTouchMoveHandler);\n        options.supportPointer && on(ownerDocument, 'pointermove', _this._delayedDragTouchMoveHandler);\n        _this._dragStartTimer = setTimeout(dragStartFn, options.delay);\n      } else {\n        dragStartFn();\n      }\n    }\n  },\n  _delayedDragTouchMoveHandler: function _delayedDragTouchMoveHandler( /** TouchEvent|PointerEvent **/e) {\n    var touch = e.touches ? e.touches[0] : e;\n    if (Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) >= Math.floor(this.options.touchStartThreshold / (this.nativeDraggable && window.devicePixelRatio || 1))) {\n      this._disableDelayedDrag();\n    }\n  },\n  _disableDelayedDrag: function _disableDelayedDrag() {\n    dragEl && _disableDraggable(dragEl);\n    clearTimeout(this._dragStartTimer);\n    this._disableDelayedDragEvents();\n  },\n  _disableDelayedDragEvents: function _disableDelayedDragEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._disableDelayedDrag);\n    off(ownerDocument, 'touchend', this._disableDelayedDrag);\n    off(ownerDocument, 'touchcancel', this._disableDelayedDrag);\n    off(ownerDocument, 'pointerup', this._disableDelayedDrag);\n    off(ownerDocument, 'pointercancel', this._disableDelayedDrag);\n    off(ownerDocument, 'mousemove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'touchmove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'pointermove', this._delayedDragTouchMoveHandler);\n  },\n  _triggerDragStart: function _triggerDragStart( /** Event */evt, /** Touch */touch) {\n    touch = touch || evt.pointerType == 'touch' && evt;\n    if (!this.nativeDraggable || touch) {\n      if (this.options.supportPointer) {\n        on(document, 'pointermove', this._onTouchMove);\n      } else if (touch) {\n        on(document, 'touchmove', this._onTouchMove);\n      } else {\n        on(document, 'mousemove', this._onTouchMove);\n      }\n    } else {\n      on(dragEl, 'dragend', this);\n      on(rootEl, 'dragstart', this._onDragStart);\n    }\n    try {\n      if (document.selection) {\n        _nextTick(function () {\n          document.selection.empty();\n        });\n      } else {\n        window.getSelection().removeAllRanges();\n      }\n    } catch (err) {}\n  },\n  _dragStarted: function _dragStarted(fallback, evt) {\n    awaitingDragStarted = false;\n    if (rootEl && dragEl) {\n      pluginEvent('dragStarted', this, {\n        evt: evt\n      });\n      if (this.nativeDraggable) {\n        on(document, 'dragover', _checkOutsideTargetEl);\n      }\n      var options = this.options;\n\n      // Apply effect\n      !fallback && toggleClass(dragEl, options.dragClass, false);\n      toggleClass(dragEl, options.ghostClass, true);\n      Sortable.active = this;\n      fallback && this._appendGhost();\n\n      // Drag start event\n      _dispatchEvent({\n        sortable: this,\n        name: 'start',\n        originalEvent: evt\n      });\n    } else {\n      this._nulling();\n    }\n  },\n  _emulateDragOver: function _emulateDragOver() {\n    if (touchEvt) {\n      this._lastX = touchEvt.clientX;\n      this._lastY = touchEvt.clientY;\n      _hideGhostForTarget();\n      var target = document.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n      var parent = target;\n      while (target && target.shadowRoot) {\n        target = target.shadowRoot.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n        if (target === parent) break;\n        parent = target;\n      }\n      dragEl.parentNode[expando]._isOutsideThisEl(target);\n      if (parent) {\n        do {\n          if (parent[expando]) {\n            var inserted = void 0;\n            inserted = parent[expando]._onDragOver({\n              clientX: touchEvt.clientX,\n              clientY: touchEvt.clientY,\n              target: target,\n              rootEl: parent\n            });\n            if (inserted && !this.options.dragoverBubble) {\n              break;\n            }\n          }\n          target = parent; // store last element\n        }\n        /* jshint boss:true */ while (parent = getParentOrHost(parent));\n      }\n      _unhideGhostForTarget();\n    }\n  },\n  _onTouchMove: function _onTouchMove( /**TouchEvent*/evt) {\n    if (tapEvt) {\n      var options = this.options,\n        fallbackTolerance = options.fallbackTolerance,\n        fallbackOffset = options.fallbackOffset,\n        touch = evt.touches ? evt.touches[0] : evt,\n        ghostMatrix = ghostEl && matrix(ghostEl, true),\n        scaleX = ghostEl && ghostMatrix && ghostMatrix.a,\n        scaleY = ghostEl && ghostMatrix && ghostMatrix.d,\n        relativeScrollOffset = PositionGhostAbsolutely && ghostRelativeParent && getRelativeScrollOffset(ghostRelativeParent),\n        dx = (touch.clientX - tapEvt.clientX + fallbackOffset.x) / (scaleX || 1) + (relativeScrollOffset ? relativeScrollOffset[0] - ghostRelativeParentInitialScroll[0] : 0) / (scaleX || 1),\n        dy = (touch.clientY - tapEvt.clientY + fallbackOffset.y) / (scaleY || 1) + (relativeScrollOffset ? relativeScrollOffset[1] - ghostRelativeParentInitialScroll[1] : 0) / (scaleY || 1);\n\n      // only set the status to dragging, when we are actually dragging\n      if (!Sortable.active && !awaitingDragStarted) {\n        if (fallbackTolerance && Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) < fallbackTolerance) {\n          return;\n        }\n        this._onDragStart(evt, true);\n      }\n      if (ghostEl) {\n        if (ghostMatrix) {\n          ghostMatrix.e += dx - (lastDx || 0);\n          ghostMatrix.f += dy - (lastDy || 0);\n        } else {\n          ghostMatrix = {\n            a: 1,\n            b: 0,\n            c: 0,\n            d: 1,\n            e: dx,\n            f: dy\n          };\n        }\n        var cssMatrix = \"matrix(\".concat(ghostMatrix.a, \",\").concat(ghostMatrix.b, \",\").concat(ghostMatrix.c, \",\").concat(ghostMatrix.d, \",\").concat(ghostMatrix.e, \",\").concat(ghostMatrix.f, \")\");\n        css(ghostEl, 'webkitTransform', cssMatrix);\n        css(ghostEl, 'mozTransform', cssMatrix);\n        css(ghostEl, 'msTransform', cssMatrix);\n        css(ghostEl, 'transform', cssMatrix);\n        lastDx = dx;\n        lastDy = dy;\n        touchEvt = touch;\n      }\n      evt.cancelable && evt.preventDefault();\n    }\n  },\n  _appendGhost: function _appendGhost() {\n    // Bug if using scale(): https://stackoverflow.com/questions/2637058\n    // Not being adjusted for\n    if (!ghostEl) {\n      var container = this.options.fallbackOnBody ? document.body : rootEl,\n        rect = getRect(dragEl, true, PositionGhostAbsolutely, true, container),\n        options = this.options;\n\n      // Position absolutely\n      if (PositionGhostAbsolutely) {\n        // Get relatively positioned parent\n        ghostRelativeParent = container;\n        while (css(ghostRelativeParent, 'position') === 'static' && css(ghostRelativeParent, 'transform') === 'none' && ghostRelativeParent !== document) {\n          ghostRelativeParent = ghostRelativeParent.parentNode;\n        }\n        if (ghostRelativeParent !== document.body && ghostRelativeParent !== document.documentElement) {\n          if (ghostRelativeParent === document) ghostRelativeParent = getWindowScrollingElement();\n          rect.top += ghostRelativeParent.scrollTop;\n          rect.left += ghostRelativeParent.scrollLeft;\n        } else {\n          ghostRelativeParent = getWindowScrollingElement();\n        }\n        ghostRelativeParentInitialScroll = getRelativeScrollOffset(ghostRelativeParent);\n      }\n      ghostEl = dragEl.cloneNode(true);\n      toggleClass(ghostEl, options.ghostClass, false);\n      toggleClass(ghostEl, options.fallbackClass, true);\n      toggleClass(ghostEl, options.dragClass, true);\n      css(ghostEl, 'transition', '');\n      css(ghostEl, 'transform', '');\n      css(ghostEl, 'box-sizing', 'border-box');\n      css(ghostEl, 'margin', 0);\n      css(ghostEl, 'top', rect.top);\n      css(ghostEl, 'left', rect.left);\n      css(ghostEl, 'width', rect.width);\n      css(ghostEl, 'height', rect.height);\n      css(ghostEl, 'opacity', '0.8');\n      css(ghostEl, 'position', PositionGhostAbsolutely ? 'absolute' : 'fixed');\n      css(ghostEl, 'zIndex', '100000');\n      css(ghostEl, 'pointerEvents', 'none');\n      Sortable.ghost = ghostEl;\n      container.appendChild(ghostEl);\n\n      // Set transform-origin\n      css(ghostEl, 'transform-origin', tapDistanceLeft / parseInt(ghostEl.style.width) * 100 + '% ' + tapDistanceTop / parseInt(ghostEl.style.height) * 100 + '%');\n    }\n  },\n  _onDragStart: function _onDragStart( /**Event*/evt, /**boolean*/fallback) {\n    var _this = this;\n    var dataTransfer = evt.dataTransfer;\n    var options = _this.options;\n    pluginEvent('dragStart', this, {\n      evt: evt\n    });\n    if (Sortable.eventCanceled) {\n      this._onDrop();\n      return;\n    }\n    pluginEvent('setupClone', this);\n    if (!Sortable.eventCanceled) {\n      cloneEl = clone(dragEl);\n      cloneEl.removeAttribute(\"id\");\n      cloneEl.draggable = false;\n      cloneEl.style['will-change'] = '';\n      this._hideClone();\n      toggleClass(cloneEl, this.options.chosenClass, false);\n      Sortable.clone = cloneEl;\n    }\n\n    // #1143: IFrame support workaround\n    _this.cloneId = _nextTick(function () {\n      pluginEvent('clone', _this);\n      if (Sortable.eventCanceled) return;\n      if (!_this.options.removeCloneOnHide) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      }\n      _this._hideClone();\n      _dispatchEvent({\n        sortable: _this,\n        name: 'clone'\n      });\n    });\n    !fallback && toggleClass(dragEl, options.dragClass, true);\n\n    // Set proper drop events\n    if (fallback) {\n      ignoreNextClick = true;\n      _this._loopId = setInterval(_this._emulateDragOver, 50);\n    } else {\n      // Undo what was set in _prepareDragStart before drag started\n      off(document, 'mouseup', _this._onDrop);\n      off(document, 'touchend', _this._onDrop);\n      off(document, 'touchcancel', _this._onDrop);\n      if (dataTransfer) {\n        dataTransfer.effectAllowed = 'move';\n        options.setData && options.setData.call(_this, dataTransfer, dragEl);\n      }\n      on(document, 'drop', _this);\n\n      // #1276 fix:\n      css(dragEl, 'transform', 'translateZ(0)');\n    }\n    awaitingDragStarted = true;\n    _this._dragStartId = _nextTick(_this._dragStarted.bind(_this, fallback, evt));\n    on(document, 'selectstart', _this);\n    moved = true;\n    window.getSelection().removeAllRanges();\n    if (Safari) {\n      css(document.body, 'user-select', 'none');\n    }\n  },\n  // Returns true - if no further action is needed (either inserted or another condition)\n  _onDragOver: function _onDragOver( /**Event*/evt) {\n    var el = this.el,\n      target = evt.target,\n      dragRect,\n      targetRect,\n      revert,\n      options = this.options,\n      group = options.group,\n      activeSortable = Sortable.active,\n      isOwner = activeGroup === group,\n      canSort = options.sort,\n      fromSortable = putSortable || activeSortable,\n      vertical,\n      _this = this,\n      completedFired = false;\n    if (_silent) return;\n    function dragOverEvent(name, extra) {\n      pluginEvent(name, _this, _objectSpread2({\n        evt: evt,\n        isOwner: isOwner,\n        axis: vertical ? 'vertical' : 'horizontal',\n        revert: revert,\n        dragRect: dragRect,\n        targetRect: targetRect,\n        canSort: canSort,\n        fromSortable: fromSortable,\n        target: target,\n        completed: completed,\n        onMove: function onMove(target, after) {\n          return _onMove(rootEl, el, dragEl, dragRect, target, getRect(target), evt, after);\n        },\n        changed: changed\n      }, extra));\n    }\n\n    // Capture animation state\n    function capture() {\n      dragOverEvent('dragOverAnimationCapture');\n      _this.captureAnimationState();\n      if (_this !== fromSortable) {\n        fromSortable.captureAnimationState();\n      }\n    }\n\n    // Return invocation when dragEl is inserted (or completed)\n    function completed(insertion) {\n      dragOverEvent('dragOverCompleted', {\n        insertion: insertion\n      });\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        } else {\n          activeSortable._showClone(_this);\n        }\n        if (_this !== fromSortable) {\n          // Set ghost class to new sortable's ghost class\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : activeSortable.options.ghostClass, false);\n          toggleClass(dragEl, options.ghostClass, true);\n        }\n        if (putSortable !== _this && _this !== Sortable.active) {\n          putSortable = _this;\n        } else if (_this === Sortable.active && putSortable) {\n          putSortable = null;\n        }\n\n        // Animation\n        if (fromSortable === _this) {\n          _this._ignoreWhileAnimating = target;\n        }\n        _this.animateAll(function () {\n          dragOverEvent('dragOverAnimationComplete');\n          _this._ignoreWhileAnimating = null;\n        });\n        if (_this !== fromSortable) {\n          fromSortable.animateAll();\n          fromSortable._ignoreWhileAnimating = null;\n        }\n      }\n\n      // Null lastTarget if it is not inside a previously swapped element\n      if (target === dragEl && !dragEl.animated || target === el && !target.animated) {\n        lastTarget = null;\n      }\n\n      // no bubbling and not fallback\n      if (!options.dragoverBubble && !evt.rootEl && target !== document) {\n        dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n\n        // Do not detect for empty insert if already inserted\n        !insertion && nearestEmptyInsertDetectEvent(evt);\n      }\n      !options.dragoverBubble && evt.stopPropagation && evt.stopPropagation();\n      return completedFired = true;\n    }\n\n    // Call when dragEl has been inserted\n    function changed() {\n      newIndex = index(dragEl);\n      newDraggableIndex = index(dragEl, options.draggable);\n      _dispatchEvent({\n        sortable: _this,\n        name: 'change',\n        toEl: el,\n        newIndex: newIndex,\n        newDraggableIndex: newDraggableIndex,\n        originalEvent: evt\n      });\n    }\n    if (evt.preventDefault !== void 0) {\n      evt.cancelable && evt.preventDefault();\n    }\n    target = closest(target, options.draggable, el, true);\n    dragOverEvent('dragOver');\n    if (Sortable.eventCanceled) return completedFired;\n    if (dragEl.contains(evt.target) || target.animated && target.animatingX && target.animatingY || _this._ignoreWhileAnimating === target) {\n      return completed(false);\n    }\n    ignoreNextClick = false;\n    if (activeSortable && !options.disabled && (isOwner ? canSort || (revert = parentEl !== rootEl) // Reverting item into the original list\n    : putSortable === this || (this.lastPutMode = activeGroup.checkPull(this, activeSortable, dragEl, evt)) && group.checkPut(this, activeSortable, dragEl, evt))) {\n      vertical = this._getDirection(evt, target) === 'vertical';\n      dragRect = getRect(dragEl);\n      dragOverEvent('dragOverValid');\n      if (Sortable.eventCanceled) return completedFired;\n      if (revert) {\n        parentEl = rootEl; // actualization\n        capture();\n        this._hideClone();\n        dragOverEvent('revert');\n        if (!Sortable.eventCanceled) {\n          if (nextEl) {\n            rootEl.insertBefore(dragEl, nextEl);\n          } else {\n            rootEl.appendChild(dragEl);\n          }\n        }\n        return completed(true);\n      }\n      var elLastChild = lastChild(el, options.draggable);\n      if (!elLastChild || _ghostIsLast(evt, vertical, this) && !elLastChild.animated) {\n        // Insert to end of list\n\n        // If already at end of list: Do not insert\n        if (elLastChild === dragEl) {\n          return completed(false);\n        }\n\n        // if there is a last element, it is the target\n        if (elLastChild && el === evt.target) {\n          target = elLastChild;\n        }\n        if (target) {\n          targetRect = getRect(target);\n        }\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, !!target) !== false) {\n          capture();\n          if (elLastChild && elLastChild.nextSibling) {\n            // the last draggable element is not the last node\n            el.insertBefore(dragEl, elLastChild.nextSibling);\n          } else {\n            el.appendChild(dragEl);\n          }\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (elLastChild && _ghostIsFirst(evt, vertical, this)) {\n        // Insert to start of list\n        var firstChild = getChild(el, 0, options, true);\n        if (firstChild === dragEl) {\n          return completed(false);\n        }\n        target = firstChild;\n        targetRect = getRect(target);\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, false) !== false) {\n          capture();\n          el.insertBefore(dragEl, firstChild);\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (target.parentNode === el) {\n        targetRect = getRect(target);\n        var direction = 0,\n          targetBeforeFirstSwap,\n          differentLevel = dragEl.parentNode !== el,\n          differentRowCol = !_dragElInRowColumn(dragEl.animated && dragEl.toRect || dragRect, target.animated && target.toRect || targetRect, vertical),\n          side1 = vertical ? 'top' : 'left',\n          scrolledPastTop = isScrolledPast(target, 'top', 'top') || isScrolledPast(dragEl, 'top', 'top'),\n          scrollBefore = scrolledPastTop ? scrolledPastTop.scrollTop : void 0;\n        if (lastTarget !== target) {\n          targetBeforeFirstSwap = targetRect[side1];\n          pastFirstInvertThresh = false;\n          isCircumstantialInvert = !differentRowCol && options.invertSwap || differentLevel;\n        }\n        direction = _getSwapDirection(evt, target, targetRect, vertical, differentRowCol ? 1 : options.swapThreshold, options.invertedSwapThreshold == null ? options.swapThreshold : options.invertedSwapThreshold, isCircumstantialInvert, lastTarget === target);\n        var sibling;\n        if (direction !== 0) {\n          // Check if target is beside dragEl in respective direction (ignoring hidden elements)\n          var dragIndex = index(dragEl);\n          do {\n            dragIndex -= direction;\n            sibling = parentEl.children[dragIndex];\n          } while (sibling && (css(sibling, 'display') === 'none' || sibling === ghostEl));\n        }\n        // If dragEl is already beside target: Do not insert\n        if (direction === 0 || sibling === target) {\n          return completed(false);\n        }\n        lastTarget = target;\n        lastDirection = direction;\n        var nextSibling = target.nextElementSibling,\n          after = false;\n        after = direction === 1;\n        var moveVector = _onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, after);\n        if (moveVector !== false) {\n          if (moveVector === 1 || moveVector === -1) {\n            after = moveVector === 1;\n          }\n          _silent = true;\n          setTimeout(_unsilent, 30);\n          capture();\n          if (after && !nextSibling) {\n            el.appendChild(dragEl);\n          } else {\n            target.parentNode.insertBefore(dragEl, after ? nextSibling : target);\n          }\n\n          // Undo chrome's scroll adjustment (has no effect on other browsers)\n          if (scrolledPastTop) {\n            scrollBy(scrolledPastTop, 0, scrollBefore - scrolledPastTop.scrollTop);\n          }\n          parentEl = dragEl.parentNode; // actualization\n\n          // must be done before animation\n          if (targetBeforeFirstSwap !== undefined && !isCircumstantialInvert) {\n            targetMoveDistance = Math.abs(targetBeforeFirstSwap - getRect(target)[side1]);\n          }\n          changed();\n          return completed(true);\n        }\n      }\n      if (el.contains(dragEl)) {\n        return completed(false);\n      }\n    }\n    return false;\n  },\n  _ignoreWhileAnimating: null,\n  _offMoveEvents: function _offMoveEvents() {\n    off(document, 'mousemove', this._onTouchMove);\n    off(document, 'touchmove', this._onTouchMove);\n    off(document, 'pointermove', this._onTouchMove);\n    off(document, 'dragover', nearestEmptyInsertDetectEvent);\n    off(document, 'mousemove', nearestEmptyInsertDetectEvent);\n    off(document, 'touchmove', nearestEmptyInsertDetectEvent);\n  },\n  _offUpEvents: function _offUpEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._onDrop);\n    off(ownerDocument, 'touchend', this._onDrop);\n    off(ownerDocument, 'pointerup', this._onDrop);\n    off(ownerDocument, 'pointercancel', this._onDrop);\n    off(ownerDocument, 'touchcancel', this._onDrop);\n    off(document, 'selectstart', this);\n  },\n  _onDrop: function _onDrop( /**Event*/evt) {\n    var el = this.el,\n      options = this.options;\n\n    // Get the index of the dragged element within its parent\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    pluginEvent('drop', this, {\n      evt: evt\n    });\n    parentEl = dragEl && dragEl.parentNode;\n\n    // Get again after plugin event\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    if (Sortable.eventCanceled) {\n      this._nulling();\n      return;\n    }\n    awaitingDragStarted = false;\n    isCircumstantialInvert = false;\n    pastFirstInvertThresh = false;\n    clearInterval(this._loopId);\n    clearTimeout(this._dragStartTimer);\n    _cancelNextTick(this.cloneId);\n    _cancelNextTick(this._dragStartId);\n\n    // Unbind events\n    if (this.nativeDraggable) {\n      off(document, 'drop', this);\n      off(el, 'dragstart', this._onDragStart);\n    }\n    this._offMoveEvents();\n    this._offUpEvents();\n    if (Safari) {\n      css(document.body, 'user-select', '');\n    }\n    css(dragEl, 'transform', '');\n    if (evt) {\n      if (moved) {\n        evt.cancelable && evt.preventDefault();\n        !options.dropBubble && evt.stopPropagation();\n      }\n      ghostEl && ghostEl.parentNode && ghostEl.parentNode.removeChild(ghostEl);\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        // Remove clone(s)\n        cloneEl && cloneEl.parentNode && cloneEl.parentNode.removeChild(cloneEl);\n      }\n      if (dragEl) {\n        if (this.nativeDraggable) {\n          off(dragEl, 'dragend', this);\n        }\n        _disableDraggable(dragEl);\n        dragEl.style['will-change'] = '';\n\n        // Remove classes\n        // ghostClass is added in dragStarted\n        if (moved && !awaitingDragStarted) {\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : this.options.ghostClass, false);\n        }\n        toggleClass(dragEl, this.options.chosenClass, false);\n\n        // Drag stop event\n        _dispatchEvent({\n          sortable: this,\n          name: 'unchoose',\n          toEl: parentEl,\n          newIndex: null,\n          newDraggableIndex: null,\n          originalEvent: evt\n        });\n        if (rootEl !== parentEl) {\n          if (newIndex >= 0) {\n            // Add event\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'add',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n\n            // Remove event\n            _dispatchEvent({\n              sortable: this,\n              name: 'remove',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n\n            // drag from one list and drop into another\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'sort',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n            _dispatchEvent({\n              sortable: this,\n              name: 'sort',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n          }\n          putSortable && putSortable.save();\n        } else {\n          if (newIndex !== oldIndex) {\n            if (newIndex >= 0) {\n              // drag & drop within the same list\n              _dispatchEvent({\n                sortable: this,\n                name: 'update',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n              _dispatchEvent({\n                sortable: this,\n                name: 'sort',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n            }\n          }\n        }\n        if (Sortable.active) {\n          /* jshint eqnull:true */\n          if (newIndex == null || newIndex === -1) {\n            newIndex = oldIndex;\n            newDraggableIndex = oldDraggableIndex;\n          }\n          _dispatchEvent({\n            sortable: this,\n            name: 'end',\n            toEl: parentEl,\n            originalEvent: evt\n          });\n\n          // Save sorting\n          this.save();\n        }\n      }\n    }\n    this._nulling();\n  },\n  _nulling: function _nulling() {\n    pluginEvent('nulling', this);\n    rootEl = dragEl = parentEl = ghostEl = nextEl = cloneEl = lastDownEl = cloneHidden = tapEvt = touchEvt = moved = newIndex = newDraggableIndex = oldIndex = oldDraggableIndex = lastTarget = lastDirection = putSortable = activeGroup = Sortable.dragged = Sortable.ghost = Sortable.clone = Sortable.active = null;\n    savedInputChecked.forEach(function (el) {\n      el.checked = true;\n    });\n    savedInputChecked.length = lastDx = lastDy = 0;\n  },\n  handleEvent: function handleEvent( /**Event*/evt) {\n    switch (evt.type) {\n      case 'drop':\n      case 'dragend':\n        this._onDrop(evt);\n        break;\n      case 'dragenter':\n      case 'dragover':\n        if (dragEl) {\n          this._onDragOver(evt);\n          _globalDragOver(evt);\n        }\n        break;\n      case 'selectstart':\n        evt.preventDefault();\n        break;\n    }\n  },\n  /**\r\n   * Serializes the item into an array of string.\r\n   * @returns {String[]}\r\n   */\n  toArray: function toArray() {\n    var order = [],\n      el,\n      children = this.el.children,\n      i = 0,\n      n = children.length,\n      options = this.options;\n    for (; i < n; i++) {\n      el = children[i];\n      if (closest(el, options.draggable, this.el, false)) {\n        order.push(el.getAttribute(options.dataIdAttr) || _generateId(el));\n      }\n    }\n    return order;\n  },\n  /**\r\n   * Sorts the elements according to the array.\r\n   * @param  {String[]}  order  order of the items\r\n   */\n  sort: function sort(order, useAnimation) {\n    var items = {},\n      rootEl = this.el;\n    this.toArray().forEach(function (id, i) {\n      var el = rootEl.children[i];\n      if (closest(el, this.options.draggable, rootEl, false)) {\n        items[id] = el;\n      }\n    }, this);\n    useAnimation && this.captureAnimationState();\n    order.forEach(function (id) {\n      if (items[id]) {\n        rootEl.removeChild(items[id]);\n        rootEl.appendChild(items[id]);\n      }\n    });\n    useAnimation && this.animateAll();\n  },\n  /**\r\n   * Save the current sorting\r\n   */\n  save: function save() {\n    var store = this.options.store;\n    store && store.set && store.set(this);\n  },\n  /**\r\n   * For each element in the set, get the first element that matches the selector by testing the element itself and traversing up through its ancestors in the DOM tree.\r\n   * @param   {HTMLElement}  el\r\n   * @param   {String}       [selector]  default: `options.draggable`\r\n   * @returns {HTMLElement|null}\r\n   */\n  closest: function closest$1(el, selector) {\n    return closest(el, selector || this.options.draggable, this.el, false);\n  },\n  /**\r\n   * Set/get option\r\n   * @param   {string} name\r\n   * @param   {*}      [value]\r\n   * @returns {*}\r\n   */\n  option: function option(name, value) {\n    var options = this.options;\n    if (value === void 0) {\n      return options[name];\n    } else {\n      var modifiedValue = PluginManager.modifyOption(this, name, value);\n      if (typeof modifiedValue !== 'undefined') {\n        options[name] = modifiedValue;\n      } else {\n        options[name] = value;\n      }\n      if (name === 'group') {\n        _prepareGroup(options);\n      }\n    }\n  },\n  /**\r\n   * Destroy\r\n   */\n  destroy: function destroy() {\n    pluginEvent('destroy', this);\n    var el = this.el;\n    el[expando] = null;\n    off(el, 'mousedown', this._onTapStart);\n    off(el, 'touchstart', this._onTapStart);\n    off(el, 'pointerdown', this._onTapStart);\n    if (this.nativeDraggable) {\n      off(el, 'dragover', this);\n      off(el, 'dragenter', this);\n    }\n    // Remove draggable attributes\n    Array.prototype.forEach.call(el.querySelectorAll('[draggable]'), function (el) {\n      el.removeAttribute('draggable');\n    });\n    this._onDrop();\n    this._disableDelayedDragEvents();\n    sortables.splice(sortables.indexOf(this.el), 1);\n    this.el = el = null;\n  },\n  _hideClone: function _hideClone() {\n    if (!cloneHidden) {\n      pluginEvent('hideClone', this);\n      if (Sortable.eventCanceled) return;\n      css(cloneEl, 'display', 'none');\n      if (this.options.removeCloneOnHide && cloneEl.parentNode) {\n        cloneEl.parentNode.removeChild(cloneEl);\n      }\n      cloneHidden = true;\n    }\n  },\n  _showClone: function _showClone(putSortable) {\n    if (putSortable.lastPutMode !== 'clone') {\n      this._hideClone();\n      return;\n    }\n    if (cloneHidden) {\n      pluginEvent('showClone', this);\n      if (Sortable.eventCanceled) return;\n\n      // show clone at dragEl or original position\n      if (dragEl.parentNode == rootEl && !this.options.group.revertClone) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      } else if (nextEl) {\n        rootEl.insertBefore(cloneEl, nextEl);\n      } else {\n        rootEl.appendChild(cloneEl);\n      }\n      if (this.options.group.revertClone) {\n        this.animate(dragEl, cloneEl);\n      }\n      css(cloneEl, 'display', '');\n      cloneHidden = false;\n    }\n  }\n};\nfunction _globalDragOver( /**Event*/evt) {\n  if (evt.dataTransfer) {\n    evt.dataTransfer.dropEffect = 'move';\n  }\n  evt.cancelable && evt.preventDefault();\n}\nfunction _onMove(fromEl, toEl, dragEl, dragRect, targetEl, targetRect, originalEvent, willInsertAfter) {\n  var evt,\n    sortable = fromEl[expando],\n    onMoveFn = sortable.options.onMove,\n    retVal;\n  // Support for new CustomEvent feature\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent('move', {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent('move', true, true);\n  }\n  evt.to = toEl;\n  evt.from = fromEl;\n  evt.dragged = dragEl;\n  evt.draggedRect = dragRect;\n  evt.related = targetEl || toEl;\n  evt.relatedRect = targetRect || getRect(toEl);\n  evt.willInsertAfter = willInsertAfter;\n  evt.originalEvent = originalEvent;\n  fromEl.dispatchEvent(evt);\n  if (onMoveFn) {\n    retVal = onMoveFn.call(sortable, evt, originalEvent);\n  }\n  return retVal;\n}\nfunction _disableDraggable(el) {\n  el.draggable = false;\n}\nfunction _unsilent() {\n  _silent = false;\n}\nfunction _ghostIsFirst(evt, vertical, sortable) {\n  var firstElRect = getRect(getChild(sortable.el, 0, sortable.options, true));\n  var childContainingRect = getChildContainingRectFromElement(sortable.el, sortable.options, ghostEl);\n  var spacer = 10;\n  return vertical ? evt.clientX < childContainingRect.left - spacer || evt.clientY < firstElRect.top && evt.clientX < firstElRect.right : evt.clientY < childContainingRect.top - spacer || evt.clientY < firstElRect.bottom && evt.clientX < firstElRect.left;\n}\nfunction _ghostIsLast(evt, vertical, sortable) {\n  var lastElRect = getRect(lastChild(sortable.el, sortable.options.draggable));\n  var childContainingRect = getChildContainingRectFromElement(sortable.el, sortable.options, ghostEl);\n  var spacer = 10;\n  return vertical ? evt.clientX > childContainingRect.right + spacer || evt.clientY > lastElRect.bottom && evt.clientX > lastElRect.left : evt.clientY > childContainingRect.bottom + spacer || evt.clientX > lastElRect.right && evt.clientY > lastElRect.top;\n}\nfunction _getSwapDirection(evt, target, targetRect, vertical, swapThreshold, invertedSwapThreshold, invertSwap, isLastTarget) {\n  var mouseOnAxis = vertical ? evt.clientY : evt.clientX,\n    targetLength = vertical ? targetRect.height : targetRect.width,\n    targetS1 = vertical ? targetRect.top : targetRect.left,\n    targetS2 = vertical ? targetRect.bottom : targetRect.right,\n    invert = false;\n  if (!invertSwap) {\n    // Never invert or create dragEl shadow when target movemenet causes mouse to move past the end of regular swapThreshold\n    if (isLastTarget && targetMoveDistance < targetLength * swapThreshold) {\n      // multiplied only by swapThreshold because mouse will already be inside target by (1 - threshold) * targetLength / 2\n      // check if past first invert threshold on side opposite of lastDirection\n      if (!pastFirstInvertThresh && (lastDirection === 1 ? mouseOnAxis > targetS1 + targetLength * invertedSwapThreshold / 2 : mouseOnAxis < targetS2 - targetLength * invertedSwapThreshold / 2)) {\n        // past first invert threshold, do not restrict inverted threshold to dragEl shadow\n        pastFirstInvertThresh = true;\n      }\n      if (!pastFirstInvertThresh) {\n        // dragEl shadow (target move distance shadow)\n        if (lastDirection === 1 ? mouseOnAxis < targetS1 + targetMoveDistance // over dragEl shadow\n        : mouseOnAxis > targetS2 - targetMoveDistance) {\n          return -lastDirection;\n        }\n      } else {\n        invert = true;\n      }\n    } else {\n      // Regular\n      if (mouseOnAxis > targetS1 + targetLength * (1 - swapThreshold) / 2 && mouseOnAxis < targetS2 - targetLength * (1 - swapThreshold) / 2) {\n        return _getInsertDirection(target);\n      }\n    }\n  }\n  invert = invert || invertSwap;\n  if (invert) {\n    // Invert of regular\n    if (mouseOnAxis < targetS1 + targetLength * invertedSwapThreshold / 2 || mouseOnAxis > targetS2 - targetLength * invertedSwapThreshold / 2) {\n      return mouseOnAxis > targetS1 + targetLength / 2 ? 1 : -1;\n    }\n  }\n  return 0;\n}\n\n/**\r\n * Gets the direction dragEl must be swapped relative to target in order to make it\r\n * seem that dragEl has been \"inserted\" into that element's position\r\n * @param  {HTMLElement} target       The target whose position dragEl is being inserted at\r\n * @return {Number}                   Direction dragEl must be swapped\r\n */\nfunction _getInsertDirection(target) {\n  if (index(dragEl) < index(target)) {\n    return 1;\n  } else {\n    return -1;\n  }\n}\n\n/**\r\n * Generate id\r\n * @param   {HTMLElement} el\r\n * @returns {String}\r\n * @private\r\n */\nfunction _generateId(el) {\n  var str = el.tagName + el.className + el.src + el.href + el.textContent,\n    i = str.length,\n    sum = 0;\n  while (i--) {\n    sum += str.charCodeAt(i);\n  }\n  return sum.toString(36);\n}\nfunction _saveInputCheckedState(root) {\n  savedInputChecked.length = 0;\n  var inputs = root.getElementsByTagName('input');\n  var idx = inputs.length;\n  while (idx--) {\n    var el = inputs[idx];\n    el.checked && savedInputChecked.push(el);\n  }\n}\nfunction _nextTick(fn) {\n  return setTimeout(fn, 0);\n}\nfunction _cancelNextTick(id) {\n  return clearTimeout(id);\n}\n\n// Fixed #973:\nif (documentExists) {\n  on(document, 'touchmove', function (evt) {\n    if ((Sortable.active || awaitingDragStarted) && evt.cancelable) {\n      evt.preventDefault();\n    }\n  });\n}\n\n// Export utils\nSortable.utils = {\n  on: on,\n  off: off,\n  css: css,\n  find: find,\n  is: function is(el, selector) {\n    return !!closest(el, selector, el, false);\n  },\n  extend: extend,\n  throttle: throttle,\n  closest: closest,\n  toggleClass: toggleClass,\n  clone: clone,\n  index: index,\n  nextTick: _nextTick,\n  cancelNextTick: _cancelNextTick,\n  detectDirection: _detectDirection,\n  getChild: getChild,\n  expando: expando\n};\n\n/**\r\n * Get the Sortable instance of an element\r\n * @param  {HTMLElement} element The element\r\n * @return {Sortable|undefined}         The instance of Sortable\r\n */\nSortable.get = function (element) {\n  return element[expando];\n};\n\n/**\r\n * Mount a plugin to Sortable\r\n * @param  {...SortablePlugin|SortablePlugin[]} plugins       Plugins being mounted\r\n */\nSortable.mount = function () {\n  for (var _len = arguments.length, plugins = new Array(_len), _key = 0; _key < _len; _key++) {\n    plugins[_key] = arguments[_key];\n  }\n  if (plugins[0].constructor === Array) plugins = plugins[0];\n  plugins.forEach(function (plugin) {\n    if (!plugin.prototype || !plugin.prototype.constructor) {\n      throw \"Sortable: Mounted plugin must be a constructor function, not \".concat({}.toString.call(plugin));\n    }\n    if (plugin.utils) Sortable.utils = _objectSpread2(_objectSpread2({}, Sortable.utils), plugin.utils);\n    PluginManager.mount(plugin);\n  });\n};\n\n/**\r\n * Create sortable instance\r\n * @param {HTMLElement}  el\r\n * @param {Object}      [options]\r\n */\nSortable.create = function (el, options) {\n  return new Sortable(el, options);\n};\n\n// Export\nSortable.version = version;\n\nvar autoScrolls = [],\n  scrollEl,\n  scrollRootEl,\n  scrolling = false,\n  lastAutoScrollX,\n  lastAutoScrollY,\n  touchEvt$1,\n  pointerElemChangedInterval;\nfunction AutoScrollPlugin() {\n  function AutoScroll() {\n    this.defaults = {\n      scroll: true,\n      forceAutoScrollFallback: false,\n      scrollSensitivity: 30,\n      scrollSpeed: 10,\n      bubbleScroll: true\n    };\n\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n  }\n  AutoScroll.prototype = {\n    dragStarted: function dragStarted(_ref) {\n      var originalEvent = _ref.originalEvent;\n      if (this.sortable.nativeDraggable) {\n        on(document, 'dragover', this._handleAutoScroll);\n      } else {\n        if (this.options.supportPointer) {\n          on(document, 'pointermove', this._handleFallbackAutoScroll);\n        } else if (originalEvent.touches) {\n          on(document, 'touchmove', this._handleFallbackAutoScroll);\n        } else {\n          on(document, 'mousemove', this._handleFallbackAutoScroll);\n        }\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref2) {\n      var originalEvent = _ref2.originalEvent;\n      // For when bubbling is canceled and using fallback (fallback 'touchmove' always reached)\n      if (!this.options.dragOverBubble && !originalEvent.rootEl) {\n        this._handleAutoScroll(originalEvent);\n      }\n    },\n    drop: function drop() {\n      if (this.sortable.nativeDraggable) {\n        off(document, 'dragover', this._handleAutoScroll);\n      } else {\n        off(document, 'pointermove', this._handleFallbackAutoScroll);\n        off(document, 'touchmove', this._handleFallbackAutoScroll);\n        off(document, 'mousemove', this._handleFallbackAutoScroll);\n      }\n      clearPointerElemChangedInterval();\n      clearAutoScrolls();\n      cancelThrottle();\n    },\n    nulling: function nulling() {\n      touchEvt$1 = scrollRootEl = scrollEl = scrolling = pointerElemChangedInterval = lastAutoScrollX = lastAutoScrollY = null;\n      autoScrolls.length = 0;\n    },\n    _handleFallbackAutoScroll: function _handleFallbackAutoScroll(evt) {\n      this._handleAutoScroll(evt, true);\n    },\n    _handleAutoScroll: function _handleAutoScroll(evt, fallback) {\n      var _this = this;\n      var x = (evt.touches ? evt.touches[0] : evt).clientX,\n        y = (evt.touches ? evt.touches[0] : evt).clientY,\n        elem = document.elementFromPoint(x, y);\n      touchEvt$1 = evt;\n\n      // IE does not seem to have native autoscroll,\n      // Edge's autoscroll seems too conditional,\n      // MACOS Safari does not have autoscroll,\n      // Firefox and Chrome are good\n      if (fallback || this.options.forceAutoScrollFallback || Edge || IE11OrLess || Safari) {\n        autoScroll(evt, this.options, elem, fallback);\n\n        // Listener for pointer element change\n        var ogElemScroller = getParentAutoScrollElement(elem, true);\n        if (scrolling && (!pointerElemChangedInterval || x !== lastAutoScrollX || y !== lastAutoScrollY)) {\n          pointerElemChangedInterval && clearPointerElemChangedInterval();\n          // Detect for pointer elem change, emulating native DnD behaviour\n          pointerElemChangedInterval = setInterval(function () {\n            var newElem = getParentAutoScrollElement(document.elementFromPoint(x, y), true);\n            if (newElem !== ogElemScroller) {\n              ogElemScroller = newElem;\n              clearAutoScrolls();\n            }\n            autoScroll(evt, _this.options, newElem, fallback);\n          }, 10);\n          lastAutoScrollX = x;\n          lastAutoScrollY = y;\n        }\n      } else {\n        // if DnD is enabled (and browser has good autoscrolling), first autoscroll will already scroll, so get parent autoscroll of first autoscroll\n        if (!this.options.bubbleScroll || getParentAutoScrollElement(elem, true) === getWindowScrollingElement()) {\n          clearAutoScrolls();\n          return;\n        }\n        autoScroll(evt, this.options, getParentAutoScrollElement(elem, false), false);\n      }\n    }\n  };\n  return _extends(AutoScroll, {\n    pluginName: 'scroll',\n    initializeByDefault: true\n  });\n}\nfunction clearAutoScrolls() {\n  autoScrolls.forEach(function (autoScroll) {\n    clearInterval(autoScroll.pid);\n  });\n  autoScrolls = [];\n}\nfunction clearPointerElemChangedInterval() {\n  clearInterval(pointerElemChangedInterval);\n}\nvar autoScroll = throttle(function (evt, options, rootEl, isFallback) {\n  // Bug: https://bugzilla.mozilla.org/show_bug.cgi?id=505521\n  if (!options.scroll) return;\n  var x = (evt.touches ? evt.touches[0] : evt).clientX,\n    y = (evt.touches ? evt.touches[0] : evt).clientY,\n    sens = options.scrollSensitivity,\n    speed = options.scrollSpeed,\n    winScroller = getWindowScrollingElement();\n  var scrollThisInstance = false,\n    scrollCustomFn;\n\n  // New scroll root, set scrollEl\n  if (scrollRootEl !== rootEl) {\n    scrollRootEl = rootEl;\n    clearAutoScrolls();\n    scrollEl = options.scroll;\n    scrollCustomFn = options.scrollFn;\n    if (scrollEl === true) {\n      scrollEl = getParentAutoScrollElement(rootEl, true);\n    }\n  }\n  var layersOut = 0;\n  var currentParent = scrollEl;\n  do {\n    var el = currentParent,\n      rect = getRect(el),\n      top = rect.top,\n      bottom = rect.bottom,\n      left = rect.left,\n      right = rect.right,\n      width = rect.width,\n      height = rect.height,\n      canScrollX = void 0,\n      canScrollY = void 0,\n      scrollWidth = el.scrollWidth,\n      scrollHeight = el.scrollHeight,\n      elCSS = css(el),\n      scrollPosX = el.scrollLeft,\n      scrollPosY = el.scrollTop;\n    if (el === winScroller) {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll' || elCSS.overflowX === 'visible');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll' || elCSS.overflowY === 'visible');\n    } else {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll');\n    }\n    var vx = canScrollX && (Math.abs(right - x) <= sens && scrollPosX + width < scrollWidth) - (Math.abs(left - x) <= sens && !!scrollPosX);\n    var vy = canScrollY && (Math.abs(bottom - y) <= sens && scrollPosY + height < scrollHeight) - (Math.abs(top - y) <= sens && !!scrollPosY);\n    if (!autoScrolls[layersOut]) {\n      for (var i = 0; i <= layersOut; i++) {\n        if (!autoScrolls[i]) {\n          autoScrolls[i] = {};\n        }\n      }\n    }\n    if (autoScrolls[layersOut].vx != vx || autoScrolls[layersOut].vy != vy || autoScrolls[layersOut].el !== el) {\n      autoScrolls[layersOut].el = el;\n      autoScrolls[layersOut].vx = vx;\n      autoScrolls[layersOut].vy = vy;\n      clearInterval(autoScrolls[layersOut].pid);\n      if (vx != 0 || vy != 0) {\n        scrollThisInstance = true;\n        /* jshint loopfunc:true */\n        autoScrolls[layersOut].pid = setInterval(function () {\n          // emulate drag over during autoscroll (fallback), emulating native DnD behaviour\n          if (isFallback && this.layer === 0) {\n            Sortable.active._onTouchMove(touchEvt$1); // To move ghost if it is positioned absolutely\n          }\n          var scrollOffsetY = autoScrolls[this.layer].vy ? autoScrolls[this.layer].vy * speed : 0;\n          var scrollOffsetX = autoScrolls[this.layer].vx ? autoScrolls[this.layer].vx * speed : 0;\n          if (typeof scrollCustomFn === 'function') {\n            if (scrollCustomFn.call(Sortable.dragged.parentNode[expando], scrollOffsetX, scrollOffsetY, evt, touchEvt$1, autoScrolls[this.layer].el) !== 'continue') {\n              return;\n            }\n          }\n          scrollBy(autoScrolls[this.layer].el, scrollOffsetX, scrollOffsetY);\n        }.bind({\n          layer: layersOut\n        }), 24);\n      }\n    }\n    layersOut++;\n  } while (options.bubbleScroll && currentParent !== winScroller && (currentParent = getParentAutoScrollElement(currentParent, false)));\n  scrolling = scrollThisInstance; // in case another function catches scrolling as false in between when it is not\n}, 30);\n\nvar drop = function drop(_ref) {\n  var originalEvent = _ref.originalEvent,\n    putSortable = _ref.putSortable,\n    dragEl = _ref.dragEl,\n    activeSortable = _ref.activeSortable,\n    dispatchSortableEvent = _ref.dispatchSortableEvent,\n    hideGhostForTarget = _ref.hideGhostForTarget,\n    unhideGhostForTarget = _ref.unhideGhostForTarget;\n  if (!originalEvent) return;\n  var toSortable = putSortable || activeSortable;\n  hideGhostForTarget();\n  var touch = originalEvent.changedTouches && originalEvent.changedTouches.length ? originalEvent.changedTouches[0] : originalEvent;\n  var target = document.elementFromPoint(touch.clientX, touch.clientY);\n  unhideGhostForTarget();\n  if (toSortable && !toSortable.el.contains(target)) {\n    dispatchSortableEvent('spill');\n    this.onSpill({\n      dragEl: dragEl,\n      putSortable: putSortable\n    });\n  }\n};\nfunction Revert() {}\nRevert.prototype = {\n  startIndex: null,\n  dragStart: function dragStart(_ref2) {\n    var oldDraggableIndex = _ref2.oldDraggableIndex;\n    this.startIndex = oldDraggableIndex;\n  },\n  onSpill: function onSpill(_ref3) {\n    var dragEl = _ref3.dragEl,\n      putSortable = _ref3.putSortable;\n    this.sortable.captureAnimationState();\n    if (putSortable) {\n      putSortable.captureAnimationState();\n    }\n    var nextSibling = getChild(this.sortable.el, this.startIndex, this.options);\n    if (nextSibling) {\n      this.sortable.el.insertBefore(dragEl, nextSibling);\n    } else {\n      this.sortable.el.appendChild(dragEl);\n    }\n    this.sortable.animateAll();\n    if (putSortable) {\n      putSortable.animateAll();\n    }\n  },\n  drop: drop\n};\n_extends(Revert, {\n  pluginName: 'revertOnSpill'\n});\nfunction Remove() {}\nRemove.prototype = {\n  onSpill: function onSpill(_ref4) {\n    var dragEl = _ref4.dragEl,\n      putSortable = _ref4.putSortable;\n    var parentSortable = putSortable || this.sortable;\n    parentSortable.captureAnimationState();\n    dragEl.parentNode && dragEl.parentNode.removeChild(dragEl);\n    parentSortable.animateAll();\n  },\n  drop: drop\n};\n_extends(Remove, {\n  pluginName: 'removeOnSpill'\n});\n\nvar lastSwapEl;\nfunction SwapPlugin() {\n  function Swap() {\n    this.defaults = {\n      swapClass: 'sortable-swap-highlight'\n    };\n  }\n  Swap.prototype = {\n    dragStart: function dragStart(_ref) {\n      var dragEl = _ref.dragEl;\n      lastSwapEl = dragEl;\n    },\n    dragOverValid: function dragOverValid(_ref2) {\n      var completed = _ref2.completed,\n        target = _ref2.target,\n        onMove = _ref2.onMove,\n        activeSortable = _ref2.activeSortable,\n        changed = _ref2.changed,\n        cancel = _ref2.cancel;\n      if (!activeSortable.options.swap) return;\n      var el = this.sortable.el,\n        options = this.options;\n      if (target && target !== el) {\n        var prevSwapEl = lastSwapEl;\n        if (onMove(target) !== false) {\n          toggleClass(target, options.swapClass, true);\n          lastSwapEl = target;\n        } else {\n          lastSwapEl = null;\n        }\n        if (prevSwapEl && prevSwapEl !== lastSwapEl) {\n          toggleClass(prevSwapEl, options.swapClass, false);\n        }\n      }\n      changed();\n      completed(true);\n      cancel();\n    },\n    drop: function drop(_ref3) {\n      var activeSortable = _ref3.activeSortable,\n        putSortable = _ref3.putSortable,\n        dragEl = _ref3.dragEl;\n      var toSortable = putSortable || this.sortable;\n      var options = this.options;\n      lastSwapEl && toggleClass(lastSwapEl, options.swapClass, false);\n      if (lastSwapEl && (options.swap || putSortable && putSortable.options.swap)) {\n        if (dragEl !== lastSwapEl) {\n          toSortable.captureAnimationState();\n          if (toSortable !== activeSortable) activeSortable.captureAnimationState();\n          swapNodes(dragEl, lastSwapEl);\n          toSortable.animateAll();\n          if (toSortable !== activeSortable) activeSortable.animateAll();\n        }\n      }\n    },\n    nulling: function nulling() {\n      lastSwapEl = null;\n    }\n  };\n  return _extends(Swap, {\n    pluginName: 'swap',\n    eventProperties: function eventProperties() {\n      return {\n        swapItem: lastSwapEl\n      };\n    }\n  });\n}\nfunction swapNodes(n1, n2) {\n  var p1 = n1.parentNode,\n    p2 = n2.parentNode,\n    i1,\n    i2;\n  if (!p1 || !p2 || p1.isEqualNode(n2) || p2.isEqualNode(n1)) return;\n  i1 = index(n1);\n  i2 = index(n2);\n  if (p1.isEqualNode(p2) && i1 < i2) {\n    i2++;\n  }\n  p1.insertBefore(n2, p1.children[i1]);\n  p2.insertBefore(n1, p2.children[i2]);\n}\n\nvar multiDragElements = [],\n  multiDragClones = [],\n  lastMultiDragSelect,\n  // for selection with modifier key down (SHIFT)\n  multiDragSortable,\n  initialFolding = false,\n  // Initial multi-drag fold when drag started\n  folding = false,\n  // Folding any other time\n  dragStarted = false,\n  dragEl$1,\n  clonesFromRect,\n  clonesHidden;\nfunction MultiDragPlugin() {\n  function MultiDrag(sortable) {\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n    if (!sortable.options.avoidImplicitDeselect) {\n      if (sortable.options.supportPointer) {\n        on(document, 'pointerup', this._deselectMultiDrag);\n      } else {\n        on(document, 'mouseup', this._deselectMultiDrag);\n        on(document, 'touchend', this._deselectMultiDrag);\n      }\n    }\n    on(document, 'keydown', this._checkKeyDown);\n    on(document, 'keyup', this._checkKeyUp);\n    this.defaults = {\n      selectedClass: 'sortable-selected',\n      multiDragKey: null,\n      avoidImplicitDeselect: false,\n      setData: function setData(dataTransfer, dragEl) {\n        var data = '';\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          multiDragElements.forEach(function (multiDragElement, i) {\n            data += (!i ? '' : ', ') + multiDragElement.textContent;\n          });\n        } else {\n          data = dragEl.textContent;\n        }\n        dataTransfer.setData('Text', data);\n      }\n    };\n  }\n  MultiDrag.prototype = {\n    multiDragKeyDown: false,\n    isMultiDrag: false,\n    delayStartGlobal: function delayStartGlobal(_ref) {\n      var dragged = _ref.dragEl;\n      dragEl$1 = dragged;\n    },\n    delayEnded: function delayEnded() {\n      this.isMultiDrag = ~multiDragElements.indexOf(dragEl$1);\n    },\n    setupClone: function setupClone(_ref2) {\n      var sortable = _ref2.sortable,\n        cancel = _ref2.cancel;\n      if (!this.isMultiDrag) return;\n      for (var i = 0; i < multiDragElements.length; i++) {\n        multiDragClones.push(clone(multiDragElements[i]));\n        multiDragClones[i].sortableIndex = multiDragElements[i].sortableIndex;\n        multiDragClones[i].draggable = false;\n        multiDragClones[i].style['will-change'] = '';\n        toggleClass(multiDragClones[i], this.options.selectedClass, false);\n        multiDragElements[i] === dragEl$1 && toggleClass(multiDragClones[i], this.options.chosenClass, false);\n      }\n      sortable._hideClone();\n      cancel();\n    },\n    clone: function clone(_ref3) {\n      var sortable = _ref3.sortable,\n        rootEl = _ref3.rootEl,\n        dispatchSortableEvent = _ref3.dispatchSortableEvent,\n        cancel = _ref3.cancel;\n      if (!this.isMultiDrag) return;\n      if (!this.options.removeCloneOnHide) {\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          insertMultiDragClones(true, rootEl);\n          dispatchSortableEvent('clone');\n          cancel();\n        }\n      }\n    },\n    showClone: function showClone(_ref4) {\n      var cloneNowShown = _ref4.cloneNowShown,\n        rootEl = _ref4.rootEl,\n        cancel = _ref4.cancel;\n      if (!this.isMultiDrag) return;\n      insertMultiDragClones(false, rootEl);\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', '');\n      });\n      cloneNowShown();\n      clonesHidden = false;\n      cancel();\n    },\n    hideClone: function hideClone(_ref5) {\n      var _this = this;\n      var sortable = _ref5.sortable,\n        cloneNowHidden = _ref5.cloneNowHidden,\n        cancel = _ref5.cancel;\n      if (!this.isMultiDrag) return;\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', 'none');\n        if (_this.options.removeCloneOnHide && clone.parentNode) {\n          clone.parentNode.removeChild(clone);\n        }\n      });\n      cloneNowHidden();\n      clonesHidden = true;\n      cancel();\n    },\n    dragStartGlobal: function dragStartGlobal(_ref6) {\n      var sortable = _ref6.sortable;\n      if (!this.isMultiDrag && multiDragSortable) {\n        multiDragSortable.multiDrag._deselectMultiDrag();\n      }\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.sortableIndex = index(multiDragElement);\n      });\n\n      // Sort multi-drag elements\n      multiDragElements = multiDragElements.sort(function (a, b) {\n        return a.sortableIndex - b.sortableIndex;\n      });\n      dragStarted = true;\n    },\n    dragStarted: function dragStarted(_ref7) {\n      var _this2 = this;\n      var sortable = _ref7.sortable;\n      if (!this.isMultiDrag) return;\n      if (this.options.sort) {\n        // Capture rects,\n        // hide multi drag elements (by positioning them absolute),\n        // set multi drag elements rects to dragRect,\n        // show multi drag elements,\n        // animate to rects,\n        // unset rects & remove from DOM\n\n        sortable.captureAnimationState();\n        if (this.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            css(multiDragElement, 'position', 'absolute');\n          });\n          var dragRect = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRect);\n          });\n          folding = true;\n          initialFolding = true;\n        }\n      }\n      sortable.animateAll(function () {\n        folding = false;\n        initialFolding = false;\n        if (_this2.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n        }\n\n        // Remove all auxiliary multidrag items from el, if sorting enabled\n        if (_this2.options.sort) {\n          removeMultiDragElements();\n        }\n      });\n    },\n    dragOver: function dragOver(_ref8) {\n      var target = _ref8.target,\n        completed = _ref8.completed,\n        cancel = _ref8.cancel;\n      if (folding && ~multiDragElements.indexOf(target)) {\n        completed(false);\n        cancel();\n      }\n    },\n    revert: function revert(_ref9) {\n      var fromSortable = _ref9.fromSortable,\n        rootEl = _ref9.rootEl,\n        sortable = _ref9.sortable,\n        dragRect = _ref9.dragRect;\n      if (multiDragElements.length > 1) {\n        // Setup unfold animation\n        multiDragElements.forEach(function (multiDragElement) {\n          sortable.addAnimationState({\n            target: multiDragElement,\n            rect: folding ? getRect(multiDragElement) : dragRect\n          });\n          unsetRect(multiDragElement);\n          multiDragElement.fromRect = dragRect;\n          fromSortable.removeAnimationState(multiDragElement);\n        });\n        folding = false;\n        insertMultiDragElements(!this.options.removeCloneOnHide, rootEl);\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref10) {\n      var sortable = _ref10.sortable,\n        isOwner = _ref10.isOwner,\n        insertion = _ref10.insertion,\n        activeSortable = _ref10.activeSortable,\n        parentEl = _ref10.parentEl,\n        putSortable = _ref10.putSortable;\n      var options = this.options;\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        }\n        initialFolding = false;\n        // If leaving sort:false root, or already folding - Fold to new location\n        if (options.animation && multiDragElements.length > 1 && (folding || !isOwner && !activeSortable.options.sort && !putSortable)) {\n          // Fold: Set all multi drag elements's rects to dragEl's rect when multi-drag elements are invisible\n          var dragRectAbsolute = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRectAbsolute);\n\n            // Move element(s) to end of parentEl so that it does not interfere with multi-drag clones insertion if they are inserted\n            // while folding, and so that we can capture them again because old sortable will no longer be fromSortable\n            parentEl.appendChild(multiDragElement);\n          });\n          folding = true;\n        }\n\n        // Clones must be shown (and check to remove multi drags) after folding when interfering multiDragElements are moved out\n        if (!isOwner) {\n          // Only remove if not folding (folding will remove them anyways)\n          if (!folding) {\n            removeMultiDragElements();\n          }\n          if (multiDragElements.length > 1) {\n            var clonesHiddenBefore = clonesHidden;\n            activeSortable._showClone(sortable);\n\n            // Unfold animation for clones if showing from hidden\n            if (activeSortable.options.animation && !clonesHidden && clonesHiddenBefore) {\n              multiDragClones.forEach(function (clone) {\n                activeSortable.addAnimationState({\n                  target: clone,\n                  rect: clonesFromRect\n                });\n                clone.fromRect = clonesFromRect;\n                clone.thisAnimationDuration = null;\n              });\n            }\n          } else {\n            activeSortable._showClone(sortable);\n          }\n        }\n      }\n    },\n    dragOverAnimationCapture: function dragOverAnimationCapture(_ref11) {\n      var dragRect = _ref11.dragRect,\n        isOwner = _ref11.isOwner,\n        activeSortable = _ref11.activeSortable;\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.thisAnimationDuration = null;\n      });\n      if (activeSortable.options.animation && !isOwner && activeSortable.multiDrag.isMultiDrag) {\n        clonesFromRect = _extends({}, dragRect);\n        var dragMatrix = matrix(dragEl$1, true);\n        clonesFromRect.top -= dragMatrix.f;\n        clonesFromRect.left -= dragMatrix.e;\n      }\n    },\n    dragOverAnimationComplete: function dragOverAnimationComplete() {\n      if (folding) {\n        folding = false;\n        removeMultiDragElements();\n      }\n    },\n    drop: function drop(_ref12) {\n      var evt = _ref12.originalEvent,\n        rootEl = _ref12.rootEl,\n        parentEl = _ref12.parentEl,\n        sortable = _ref12.sortable,\n        dispatchSortableEvent = _ref12.dispatchSortableEvent,\n        oldIndex = _ref12.oldIndex,\n        putSortable = _ref12.putSortable;\n      var toSortable = putSortable || this.sortable;\n      if (!evt) return;\n      var options = this.options,\n        children = parentEl.children;\n\n      // Multi-drag selection\n      if (!dragStarted) {\n        if (options.multiDragKey && !this.multiDragKeyDown) {\n          this._deselectMultiDrag();\n        }\n        toggleClass(dragEl$1, options.selectedClass, !~multiDragElements.indexOf(dragEl$1));\n        if (!~multiDragElements.indexOf(dragEl$1)) {\n          multiDragElements.push(dragEl$1);\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'select',\n            targetEl: dragEl$1,\n            originalEvent: evt\n          });\n\n          // Modifier activated, select from last to dragEl\n          if (evt.shiftKey && lastMultiDragSelect && sortable.el.contains(lastMultiDragSelect)) {\n            var lastIndex = index(lastMultiDragSelect),\n              currentIndex = index(dragEl$1);\n            if (~lastIndex && ~currentIndex && lastIndex !== currentIndex) {\n              (function () {\n                // Must include lastMultiDragSelect (select it), in case modified selection from no selection\n                // (but previous selection existed)\n                var n, i;\n                if (currentIndex > lastIndex) {\n                  i = lastIndex;\n                  n = currentIndex;\n                } else {\n                  i = currentIndex;\n                  n = lastIndex + 1;\n                }\n                var filter = options.filter;\n                for (; i < n; i++) {\n                  if (~multiDragElements.indexOf(children[i])) continue;\n                  // Check if element is draggable\n                  if (!closest(children[i], options.draggable, parentEl, false)) continue;\n                  // Check if element is filtered\n                  var filtered = filter && (typeof filter === 'function' ? filter.call(sortable, evt, children[i], sortable) : filter.split(',').some(function (criteria) {\n                    return closest(children[i], criteria.trim(), parentEl, false);\n                  }));\n                  if (filtered) continue;\n                  toggleClass(children[i], options.selectedClass, true);\n                  multiDragElements.push(children[i]);\n                  dispatchEvent({\n                    sortable: sortable,\n                    rootEl: rootEl,\n                    name: 'select',\n                    targetEl: children[i],\n                    originalEvent: evt\n                  });\n                }\n              })();\n            }\n          } else {\n            lastMultiDragSelect = dragEl$1;\n          }\n          multiDragSortable = toSortable;\n        } else {\n          multiDragElements.splice(multiDragElements.indexOf(dragEl$1), 1);\n          lastMultiDragSelect = null;\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'deselect',\n            targetEl: dragEl$1,\n            originalEvent: evt\n          });\n        }\n      }\n\n      // Multi-drag drop\n      if (dragStarted && this.isMultiDrag) {\n        folding = false;\n        // Do not \"unfold\" after around dragEl if reverted\n        if ((parentEl[expando].options.sort || parentEl !== rootEl) && multiDragElements.length > 1) {\n          var dragRect = getRect(dragEl$1),\n            multiDragIndex = index(dragEl$1, ':not(.' + this.options.selectedClass + ')');\n          if (!initialFolding && options.animation) dragEl$1.thisAnimationDuration = null;\n          toSortable.captureAnimationState();\n          if (!initialFolding) {\n            if (options.animation) {\n              dragEl$1.fromRect = dragRect;\n              multiDragElements.forEach(function (multiDragElement) {\n                multiDragElement.thisAnimationDuration = null;\n                if (multiDragElement !== dragEl$1) {\n                  var rect = folding ? getRect(multiDragElement) : dragRect;\n                  multiDragElement.fromRect = rect;\n\n                  // Prepare unfold animation\n                  toSortable.addAnimationState({\n                    target: multiDragElement,\n                    rect: rect\n                  });\n                }\n              });\n            }\n\n            // Multi drag elements are not necessarily removed from the DOM on drop, so to reinsert\n            // properly they must all be removed\n            removeMultiDragElements();\n            multiDragElements.forEach(function (multiDragElement) {\n              if (children[multiDragIndex]) {\n                parentEl.insertBefore(multiDragElement, children[multiDragIndex]);\n              } else {\n                parentEl.appendChild(multiDragElement);\n              }\n              multiDragIndex++;\n            });\n\n            // If initial folding is done, the elements may have changed position because they are now\n            // unfolding around dragEl, even though dragEl may not have his index changed, so update event\n            // must be fired here as Sortable will not.\n            if (oldIndex === index(dragEl$1)) {\n              var update = false;\n              multiDragElements.forEach(function (multiDragElement) {\n                if (multiDragElement.sortableIndex !== index(multiDragElement)) {\n                  update = true;\n                  return;\n                }\n              });\n              if (update) {\n                dispatchSortableEvent('update');\n                dispatchSortableEvent('sort');\n              }\n            }\n          }\n\n          // Must be done after capturing individual rects (scroll bar)\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n          toSortable.animateAll();\n        }\n        multiDragSortable = toSortable;\n      }\n\n      // Remove clones if necessary\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        multiDragClones.forEach(function (clone) {\n          clone.parentNode && clone.parentNode.removeChild(clone);\n        });\n      }\n    },\n    nullingGlobal: function nullingGlobal() {\n      this.isMultiDrag = dragStarted = false;\n      multiDragClones.length = 0;\n    },\n    destroyGlobal: function destroyGlobal() {\n      this._deselectMultiDrag();\n      off(document, 'pointerup', this._deselectMultiDrag);\n      off(document, 'mouseup', this._deselectMultiDrag);\n      off(document, 'touchend', this._deselectMultiDrag);\n      off(document, 'keydown', this._checkKeyDown);\n      off(document, 'keyup', this._checkKeyUp);\n    },\n    _deselectMultiDrag: function _deselectMultiDrag(evt) {\n      if (typeof dragStarted !== \"undefined\" && dragStarted) return;\n\n      // Only deselect if selection is in this sortable\n      if (multiDragSortable !== this.sortable) return;\n\n      // Only deselect if target is not item in this sortable\n      if (evt && closest(evt.target, this.options.draggable, this.sortable.el, false)) return;\n\n      // Only deselect if left click\n      if (evt && evt.button !== 0) return;\n      while (multiDragElements.length) {\n        var el = multiDragElements[0];\n        toggleClass(el, this.options.selectedClass, false);\n        multiDragElements.shift();\n        dispatchEvent({\n          sortable: this.sortable,\n          rootEl: this.sortable.el,\n          name: 'deselect',\n          targetEl: el,\n          originalEvent: evt\n        });\n      }\n    },\n    _checkKeyDown: function _checkKeyDown(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = true;\n      }\n    },\n    _checkKeyUp: function _checkKeyUp(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = false;\n      }\n    }\n  };\n  return _extends(MultiDrag, {\n    // Static methods & properties\n    pluginName: 'multiDrag',\n    utils: {\n      /**\r\n       * Selects the provided multi-drag item\r\n       * @param  {HTMLElement} el    The element to be selected\r\n       */\n      select: function select(el) {\n        var sortable = el.parentNode[expando];\n        if (!sortable || !sortable.options.multiDrag || ~multiDragElements.indexOf(el)) return;\n        if (multiDragSortable && multiDragSortable !== sortable) {\n          multiDragSortable.multiDrag._deselectMultiDrag();\n          multiDragSortable = sortable;\n        }\n        toggleClass(el, sortable.options.selectedClass, true);\n        multiDragElements.push(el);\n      },\n      /**\r\n       * Deselects the provided multi-drag item\r\n       * @param  {HTMLElement} el    The element to be deselected\r\n       */\n      deselect: function deselect(el) {\n        var sortable = el.parentNode[expando],\n          index = multiDragElements.indexOf(el);\n        if (!sortable || !sortable.options.multiDrag || !~index) return;\n        toggleClass(el, sortable.options.selectedClass, false);\n        multiDragElements.splice(index, 1);\n      }\n    },\n    eventProperties: function eventProperties() {\n      var _this3 = this;\n      var oldIndicies = [],\n        newIndicies = [];\n      multiDragElements.forEach(function (multiDragElement) {\n        oldIndicies.push({\n          multiDragElement: multiDragElement,\n          index: multiDragElement.sortableIndex\n        });\n\n        // multiDragElements will already be sorted if folding\n        var newIndex;\n        if (folding && multiDragElement !== dragEl$1) {\n          newIndex = -1;\n        } else if (folding) {\n          newIndex = index(multiDragElement, ':not(.' + _this3.options.selectedClass + ')');\n        } else {\n          newIndex = index(multiDragElement);\n        }\n        newIndicies.push({\n          multiDragElement: multiDragElement,\n          index: newIndex\n        });\n      });\n      return {\n        items: _toConsumableArray(multiDragElements),\n        clones: [].concat(multiDragClones),\n        oldIndicies: oldIndicies,\n        newIndicies: newIndicies\n      };\n    },\n    optionListeners: {\n      multiDragKey: function multiDragKey(key) {\n        key = key.toLowerCase();\n        if (key === 'ctrl') {\n          key = 'Control';\n        } else if (key.length > 1) {\n          key = key.charAt(0).toUpperCase() + key.substr(1);\n        }\n        return key;\n      }\n    }\n  });\n}\nfunction insertMultiDragElements(clonesInserted, rootEl) {\n  multiDragElements.forEach(function (multiDragElement, i) {\n    var target = rootEl.children[multiDragElement.sortableIndex + (clonesInserted ? Number(i) : 0)];\n    if (target) {\n      rootEl.insertBefore(multiDragElement, target);\n    } else {\n      rootEl.appendChild(multiDragElement);\n    }\n  });\n}\n\n/**\r\n * Insert multi-drag clones\r\n * @param  {[Boolean]} elementsInserted  Whether the multi-drag elements are inserted\r\n * @param  {HTMLElement} rootEl\r\n */\nfunction insertMultiDragClones(elementsInserted, rootEl) {\n  multiDragClones.forEach(function (clone, i) {\n    var target = rootEl.children[clone.sortableIndex + (elementsInserted ? Number(i) : 0)];\n    if (target) {\n      rootEl.insertBefore(clone, target);\n    } else {\n      rootEl.appendChild(clone);\n    }\n  });\n}\nfunction removeMultiDragElements() {\n  multiDragElements.forEach(function (multiDragElement) {\n    if (multiDragElement === dragEl$1) return;\n    multiDragElement.parentNode && multiDragElement.parentNode.removeChild(multiDragElement);\n  });\n}\n\nSortable.mount(new AutoScrollPlugin());\nSortable.mount(Remove, Revert);\n\nexport default Sortable;\nexport { MultiDragPlugin as MultiDrag, Sortable, SwapPlugin as Swap };\n", "import ExtensionPage from 'flarum/components/ExtensionPage';\nimport LoadingIndicator from 'flarum/components/LoadingIndicator';\nimport Button from 'flarum/components/Button';\nimport LinksQueueAddModal from './LinksQueueAddModal';\nimport LinksQueueListItem from './LinksQueueListItem';\n\nimport Sortable from 'sortablejs';\n\nexport default class SettingsPage extends ExtensionPage {\n  oninit(attrs) {\n    super.oninit(attrs);\n    this.loading = false;\n    this.linksQueueList = [];\n    this.loadResults();\n  }\n\n  initSort(){\n    let el = document.getElementById('linksQueueSortableItems');\n    let sortable = Sortable.create(el,{\n          animation: 150,\n          swapThreshold: 0.65,\n          onEnd: (e) => this.updateSort(e),\n        });\n  }\n\n  content() {\n    return (\n      <div className=\"ExtensionPage-settings FlarumBadgesPage\">\n        <div className=\"container\">\n\n          <div style={{paddingBottom: '10px'}}>\n            <Button className={'Button'} onclick={() => app.modal.show(LinksQueueAddModal)}>\n              {app.translator.trans('wusong8899-links-queue.admin.link-add')}\n            </Button>\n          </div>\n\n          <ul id=\"linksQueueSortableItems\" style={{padding: '0px', listStyleType: 'none'}} oncreate={this.initSort.bind(this)}>\n            {this.linksQueueList.map((LinksQueueItemData) => {\n              return (\n                <li itemID={LinksQueueItemData.id()} style={{marginTop: '5px', background: 'var(--body-bg)'}}>\n                  {LinksQueueListItem.component({ LinksQueueItemData })}\n                </li>\n              );\n            })}\n          </ul>\n\n        </div>\n      </div>\n    );\n  }\n\n  updateSort(e){\n    const newIndex = e.newIndex;\n    const oldIndex = e.oldIndex;\n\n    if(newIndex!==oldIndex){\n      const children = e.from.children;\n      const linkQueueOrder = {};\n\n      for(let i=0;i<children.length;i++){\n        const child = children[i];\n        const itemID = $(child).attr(\"itemID\");\n\n        linkQueueOrder[itemID] = i;\n      }\n\n      app.request({\n        url: `${app.forum.attribute('apiUrl')}/linksQueueList/order`,\n        method: 'POST',\n        body: { linkQueueOrder },\n      });\n    }\n  }\n\n  parseResults(results) {\n    [].push.apply(this.linksQueueList, results);\n    m.redraw();\n    return results;\n  }\n\n  loadResults() {\n    return app.store\n      .find(\"linksQueueList\")\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Model'];", "import Model from \"flarum/Model\";\n\nexport default class LinksQueue extends Model {}\nObject.assign(LinksQueue.prototype, {\n  id: Model.attribute(\"id\"),\n  name: Model.attribute(\"name\"),\n  links: Model.attribute(\"links\"),\n  sort: Model.attribute(\"sort\"),\n});\n", "import {extend, override} from 'flarum/extend';\nimport SettingsPage from './components/SettingsPage';\nimport LinksQueue from \"../forum/model/LinksQueue\";\n\napp.initializers.add('wusong8899-client1-links-queue', () => {\n  app.store.models.linksQueueList = LinksQueue;\n  app.extensionData.for('wusong8899-client1-links-queue').registerPage(SettingsPage);\n});\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "_setPrototypeOf", "t", "e", "setPrototypeOf", "bind", "__proto__", "_inherits<PERSON><PERSON>e", "create", "constructor", "flarum", "core", "compat", "LinksQueueAddModal", "_Modal", "apply", "arguments", "_proto", "oninit", "vnode", "this", "LinksQueueItemData", "attrs", "settingType", "itemName", "Stream", "name", "itemUrl", "links", "className", "title", "app", "translator", "trans", "content", "_this", "m", "style", "class", "maxlength", "required", "bidi", "<PERSON><PERSON>", "type", "loading", "onclick", "hide", "onsubmit", "_this2", "preventDefault", "save", "url", "then", "response", "handleErrors", "store", "createRecord", "linksQueueList", "location", "reload", "Modal", "isDismissible", "LinksQueueDeleteModal", "LinksQueueListItem", "_Component", "view", "linkID", "id", "linkName", "linkUrl", "sort", "editItem", "deleteItem", "modal", "show", "Component", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "_objectSpread2", "target", "i", "length", "source", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_typeof", "Symbol", "iterator", "value", "configurable", "writable", "_extends", "assign", "userAgent", "pattern", "window", "navigator", "match", "IE11OrLess", "Edge", "FireFox", "Safari", "IOS", "ChromeForAndroid", "captureMode", "capture", "passive", "on", "el", "event", "fn", "addEventListener", "off", "removeEventListener", "matches", "selector", "substring", "msMatchesSelector", "webkitMatchesSelector", "_", "getParentOrHost", "host", "document", "nodeType", "parentNode", "closest", "ctx", "includeCTX", "_throttleTimeout", "R_SPACE", "toggleClass", "state", "classList", "replace", "css", "val", "defaultView", "getComputedStyle", "currentStyle", "indexOf", "matrix", "selfOnly", "appliedTransforms", "transform", "matrixFn", "DOMMatrix", "WebKitCSSMatrix", "CSSMatrix", "MSCSSMatrix", "find", "tagName", "list", "getElementsByTagName", "n", "getWindowScrollingElement", "scrollingElement", "documentElement", "getRect", "relativeToContainingBlock", "relativeToNonStaticParent", "undoScale", "container", "getBoundingClientRect", "elRect", "top", "left", "bottom", "right", "height", "width", "innerHeight", "innerWidth", "containerRect", "parseInt", "elMatrix", "scaleX", "scaleY", "isScrolledPast", "elSide", "parentSide", "parent", "getParentAutoScrollElement", "elSideVal", "parentSideVal", "<PERSON><PERSON><PERSON><PERSON>", "childNum", "options", "includeDragEl", "<PERSON><PERSON><PERSON><PERSON>", "children", "display", "Sortable", "ghost", "dragged", "draggable", "<PERSON><PERSON><PERSON><PERSON>", "last", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "previousElementSibling", "index", "nodeName", "toUpperCase", "clone", "getRelativeScrollOffset", "offsetLeft", "offsetTop", "winScroller", "scrollLeft", "scrollTop", "includeSelf", "elem", "gotSelf", "clientWidth", "scrollWidth", "clientHeight", "scrollHeight", "elemCSS", "overflowX", "overflowY", "body", "isRectEqual", "rect1", "rect2", "Math", "round", "throttle", "callback", "ms", "args", "setTimeout", "scrollBy", "x", "y", "Polymer", "$", "j<PERSON><PERSON><PERSON>", "Zepto", "dom", "cloneNode", "getChildContainingRectFromElement", "ghostEl", "rect", "Array", "from", "child", "_rect$left", "_rect$top", "_rect$right", "_rect$bottom", "animated", "childRect", "min", "Infinity", "max", "expando", "Date", "getTime", "plugins", "defaults", "initializeByDefault", "Plugin<PERSON>anager", "mount", "plugin", "option", "p", "pluginName", "concat", "pluginEvent", "eventName", "sortable", "evt", "eventCanceled", "cancel", "eventNameGlobal", "initializePlugins", "initialized", "modified", "modifyOption", "getEventProperties", "eventProperties", "modifiedValue", "optionListeners", "_excluded", "_ref", "undefined", "originalEvent", "data", "excluded", "sourceKeys", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "propertyIsEnumerable", "_objectWithoutProperties", "dragEl", "parentEl", "rootEl", "nextEl", "lastDownEl", "cloneEl", "cloneHidden", "dragStarted", "moved", "putSortable", "activeSortable", "active", "oldIndex", "oldDraggableIndex", "newIndex", "newDraggableIndex", "hideGhostForTarget", "_hideGhostForTarget", "unhideGhostForTarget", "_unhideGhostForTarget", "cloneNowHidden", "cloneNowShown", "dispatchSortableEvent", "_dispatchEvent", "info", "targetEl", "toEl", "fromEl", "extraEventProperties", "onName", "char<PERSON>t", "substr", "CustomEvent", "createEvent", "initEvent", "bubbles", "cancelable", "to", "item", "pullMode", "lastPutMode", "allEventProperties", "dispatchEvent", "activeGroup", "tapEvt", "touchEvt", "lastDx", "lastDy", "tapDistanceLeft", "tapDistanceTop", "last<PERSON><PERSON><PERSON>", "lastDirection", "targetMoveDistance", "ghostRelativeParent", "awaitingDragStarted", "ignoreNextClick", "sortables", "pastFirstInvertThresh", "isCircumstantialInvert", "ghostRelativeParentInitialScroll", "_silent", "savedInputChecked", "documentExists", "PositionGhostAbsolutely", "CSSFloatProperty", "supportDraggable", "createElement", "supportCssPointerEvents", "cssText", "pointerEvents", "_detectDirection", "elCSS", "<PERSON><PERSON><PERSON><PERSON>", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "child1", "child2", "firstChildCSS", "secondChildCSS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marginLeft", "marginRight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flexDirection", "gridTemplateColumns", "split", "touchingSideChild2", "clear", "_prepareGroup", "toFn", "pull", "sameGroup", "group", "otherGroup", "join", "originalGroup", "checkPull", "checkPut", "put", "revertClone", "stopPropagation", "stopImmediatePropagation", "nearestEmptyInsertDetectEvent", "touches", "nearest", "clientX", "clientY", "some", "threshold", "emptyInsertThreshold", "insideHorizontally", "insideVertically", "ret", "_onDragOver", "_checkOutsideTargetEl", "_isOutsideThisEl", "toString", "animationCallbackId", "animationStates", "disabled", "handle", "test", "swapThreshold", "invertSwap", "invertedSwapThreshold", "removeCloneOnHide", "direction", "ghostClass", "chosenClass", "dragClass", "ignore", "preventOnFilter", "animation", "easing", "setData", "dataTransfer", "textContent", "dropBubble", "dragoverBubble", "dataIdAttr", "delay", "delayOnTouchOnly", "touchStartThreshold", "Number", "devicePixelRatio", "force<PERSON><PERSON><PERSON>", "fallbackClass", "fallbackOnBody", "fallbackTolerance", "fallbackOffset", "supportPointer", "nativeDraggable", "_onTapStart", "captureAnimationState", "slice", "fromRect", "thisAnimationDuration", "childMatrix", "f", "addAnimationState", "removeAnimationState", "splice", "arr", "indexOfObject", "animateAll", "clearTimeout", "animating", "animationTime", "time", "toRect", "prevFromRect", "prevToRect", "animatingRect", "targetMatrix", "sqrt", "pow", "calculateRealTime", "animate", "animationResetTimer", "currentRect", "duration", "translateX", "translateY", "animatingX", "animatingY", "forRepaintDummy", "offsetWidth", "repaint", "_onMove", "dragRect", "targetRect", "willInsertAfter", "retVal", "onMoveFn", "onMove", "draggedRect", "related", "relatedRect", "_disableDraggable", "_unsilent", "_generateId", "str", "src", "href", "sum", "charCodeAt", "_nextTick", "_cancelNextTick", "contains", "_getDirection", "touch", "pointerType", "originalTarget", "shadowRoot", "path", "<PERSON><PERSON><PERSON>", "root", "inputs", "idx", "checked", "_saveInputCheckedState", "button", "isContentEditable", "criteria", "trim", "_prepareDragStart", "dragStartFn", "ownerDocument", "nextS<PERSON>ling", "_lastX", "_lastY", "_onDrop", "_disableDelayedDragEvents", "_triggerDragStart", "_disableDelayedDrag", "_delayedDragTouchMoveHandler", "_dragStartTimer", "abs", "floor", "_onTouchMove", "_onDragStart", "selection", "empty", "getSelection", "removeAllRanges", "err", "_dragStarted", "fallback", "_appendGhost", "_nulling", "_emulateDragOver", "elementFromPoint", "ghostMatrix", "relativeScrollOffset", "dx", "dy", "b", "c", "cssMatrix", "append<PERSON><PERSON><PERSON>", "removeAttribute", "_hideClone", "cloneId", "insertBefore", "_loopId", "setInterval", "effectAllowed", "_dragStartId", "revert", "vertical", "isOwner", "canSort", "fromSortable", "completedFired", "dragOverEvent", "_ignoreWhileAnimating", "completed", "el<PERSON>ast<PERSON><PERSON><PERSON>", "lastElRect", "childContainingRect", "_ghostIsLast", "changed", "firstElRect", "_ghost<PERSON>sFirst", "<PERSON><PERSON><PERSON><PERSON>", "targetBeforeFirstSwap", "sibling", "differentLevel", "differentRowCol", "dragElS1Opp", "dragElS2Opp", "dragElOppLength", "targetS1Opp", "targetS2Opp", "targetOppLength", "_dragElInRowColumn", "side1", "scrolledPastTop", "scrollBefore", "isLastTarget", "mouseOnAxis", "targetLength", "targetS1", "targetS2", "invert", "_getInsertDirection", "_getSwapDirection", "dragIndex", "nextElement<PERSON><PERSON>ling", "after", "moveVector", "extra", "axis", "insertion", "_showClone", "_offMoveEvents", "_offUpEvents", "clearInterval", "<PERSON><PERSON><PERSON><PERSON>", "handleEvent", "dropEffect", "_globalDragOver", "toArray", "order", "getAttribute", "useAnimation", "items", "set", "destroy", "querySelectorAll", "utils", "is", "extend", "dst", "nextTick", "cancelNextTick", "detectDirection", "element", "_len", "_key", "version", "scrollEl", "scrollRootEl", "lastAutoScrollX", "lastAutoScrollY", "touchEvt$1", "pointerElemChangedInterval", "autoScrolls", "scrolling", "clearAutoScrolls", "autoScroll", "pid", "clearPointerElemChangedInterval", "<PERSON><PERSON><PERSON><PERSON>", "scroll", "scrollCustomFn", "sens", "scrollSensitivity", "speed", "scrollSpeed", "scrollThisInstance", "scrollFn", "layersOut", "currentParent", "canScrollX", "canScrollY", "scrollPosX", "scrollPosY", "vx", "vy", "layer", "scrollOffsetY", "scrollOffsetX", "bubbleScroll", "drop", "toSortable", "changedTouches", "onSpill", "<PERSON><PERSON>", "Remove", "startIndex", "dragStart", "_ref2", "_ref3", "_ref4", "parentSortable", "AutoScroll", "forceAutoScrollFallback", "_handleAutoScroll", "_handleFallbackAutoScroll", "dragOverCompleted", "dragOverBubble", "nulling", "ogElemScroller", "newElem", "SettingsPage", "_ExtensionPage", "loadResults", "initSort", "getElementById", "onEnd", "updateSort", "paddingBottom", "padding", "listStyleType", "oncreate", "map", "itemID", "marginTop", "background", "component", "linkQueueOrder", "attr", "request", "forum", "attribute", "method", "parseResults", "results", "redraw", "ExtensionPage", "LinksQueue", "_Model", "Model", "initializers", "add", "models", "extensionData", "registerPage"], "sourceRoot": ""}