{"private": true, "name": "@wusong8899/flarum-daily-check-in", "devDependencies": {"@types/sortablejs": "^1.15.8", "flarum-tsconfig": "^1.0.3", "flarum-webpack-config": "^2.0.2", "oxlint": "^1.11.2", "typescript": "^5.9.2", "vite": "^7.1.2"}, "scripts": {"dev": "vite -c vite.config.admin.ts build --mode development && vite -c vite.config.forum.ts build --mode development", "dev:admin": "vite -c vite.config.admin.ts build --mode development", "dev:forum": "vite -c vite.config.forum.ts build --mode development", "dev:watch": "vite -c vite.config.admin.ts build --watch --mode development && vite -c vite.config.forum.ts build --watch --mode development", "build": "vite -c vite.config.admin.ts build --mode production && vite -c vite.config.forum.ts build --mode production", "lint": "oxlint .", "lint:fix": "oxlint . --fix"}, "dependencies": {"sortablejs": "^1.15.0"}}