{"version": 3, "file": "admin.js", "sources": ["../src/admin/SettingsGenerator.js", "../src/admin/index.js"], "sourcesContent": ["import app from 'flarum/admin/app';\n\n/**\n * Settings generator utility for admin interface\n */\nexport class SettingsGenerator {\n    constructor(extensionId) {\n        this.extensionId = extensionId;\n        this.extensionData = app.extensionData.for(extensionId);\n    }\n\n    /**\n     * Register transition time setting\n     */\n    registerTransitionTimeSetting() {\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.TransitionTime`,\n            type: 'number',\n            label: app.translator.trans('wusong8899-client1.admin.TransitionTime'),\n        });\n        return this;\n    }\n\n    /**\n     * Register settings for advertisement slides\n     * @param {number} maxSlides - Maximum number of slides to configure\n     */\n    registerSlideSettings(maxSlides = 30) {\n        for (let i = 1; i <= maxSlides; i++) {\n            // Register link setting\n            this.extensionData.registerSetting({\n                setting: `${this.extensionId}.Link${i}`,\n                type: 'URL',\n                label: app.translator.trans(`wusong8899-client1.admin.Link${i}`),\n            });\n\n            // Register image setting\n            this.extensionData.registerSetting({\n                setting: `${this.extensionId}.Image${i}`,\n                type: 'URL',\n                label: app.translator.trans(`wusong8899-client1.admin.Image${i}`),\n            });\n        }\n        return this;\n    }\n\n    /**\n     * Register all settings for the extension\n     * @param {number} maxSlides - Maximum number of slides to configure\n     */\n    registerAllSettings(maxSlides = 30) {\n        return this\n            .registerTransitionTimeSetting()\n            .registerSlideSettings(maxSlides);\n    }\n}\n\n/**\n * Configuration constants\n */\n// Centralized config is in js/src/common/config.\n// Kept for backward compatibility; prefer importing from '../../common/config'.\nexport const EXTENSION_CONFIG = {\n    EXTENSION_ID: 'wusong8899-client1-header-adv',\n    MAX_SLIDES: 30,\n    DEFAULT_TRANSITION_TIME: 5000,\n};\n\n/**\n * Initialize admin settings\n * @param {string} extensionId - The extension identifier\n * @param {number} maxSlides - Maximum number of slides\n */\nexport function initializeAdminSettings(\n    extensionId = EXTENSION_CONFIG.EXTENSION_ID, \n    maxSlides = EXTENSION_CONFIG.MAX_SLIDES\n) {\n    const generator = new SettingsGenerator(extensionId);\n    generator.registerAllSettings(maxSlides);\n}\n", "import app from 'flarum/admin/app';\r\nimport { initializeAdminSettings } from './SettingsGenerator';\r\n\r\napp.initializers.add('wusong8899/client1-header-adv', () => {\r\n    initializeAdminSettings();\r\n});\r\n"], "names": ["SettingsGenerator", "extensionId", "app", "maxSlides", "i", "EXTENSION_CONFIG", "initializeAdminSettings"], "mappings": "0BAKO,MAAMA,CAAkB,CAC3B,YAAYC,EAAa,CACrB,KAAK,YAAcA,EACnB,KAAK,cAAgBC,EAAI,cAAc,IAAID,CAAW,CAC1D,CAKA,+BAAgC,CAC5B,YAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,kBAC5B,KAAM,SACN,MAAOC,EAAI,WAAW,MAAM,yCAAyC,CACjF,CAAS,EACM,IACX,CAMA,sBAAsBC,EAAY,GAAI,CAClC,QAASC,EAAI,EAAGA,GAAKD,EAAWC,IAE5B,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,QAAQA,CAAC,GACrC,KAAM,MACN,MAAOF,EAAI,WAAW,MAAM,gCAAgCE,CAAC,EAAE,CAC/E,CAAa,EAGD,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,SAASA,CAAC,GACtC,KAAM,MACN,MAAOF,EAAI,WAAW,MAAM,iCAAiCE,CAAC,EAAE,CAChF,CAAa,EAEL,OAAO,IACX,CAMA,oBAAoBD,EAAY,GAAI,CAChC,OAAO,KACF,8BAA6B,EAC7B,sBAAsBA,CAAS,CACxC,CACJ,CAOO,MAAME,EAAmB,CAC5B,aAAc,gCACd,WAAY,EAEhB,EAOO,SAASC,EACZL,EAAcI,EAAiB,aAC/BF,EAAYE,EAAiB,WAC/B,CACoB,IAAIL,EAAkBC,CAAW,EACzC,oBAAoBE,CAAS,CAC3C,CC5EAD,EAAI,aAAa,IAAI,gCAAiC,IAAM,CACxDI,GACJ,CAAC"}