{"version": 3, "file": "admin.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFf,EAAyBM,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,M,wBCLxC,SAASC,EAAgBb,EAAGc,GAMzC,OALAD,EAAkBZ,OAAOc,gBAAkB,SAAyBf,EAAGc,GAErE,OADAd,EAAEgB,UAAYF,EACPd,GAGFa,EAAgBb,EAAGc,G,OCNSG,OAAOC,KAAKC,OAAe,OCAhE,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAnBF,OAAOC,KAAKC,OAAO,qB,ICGnCC,EAAAA,SAAAA,GCFN,IAAwBC,EAAUC,E,kDAAAA,E,GAAVD,E,GAC5Bd,UAAYN,OAAOsB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCN,EAAeM,EAAUC,G,2BDAzBG,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,I,EAGfC,QAAA,WACE,OACE,SAAKC,UAAU,0BACb,SAAKA,UAAU,aACZC,KAAKC,sBAAsB,CAC1BC,KAAM,SACNC,QAAS,kDACTC,MAAOC,IAAIC,WAAWC,MAAM,kEAC5BC,KAAKH,IAAIC,WAAWC,MAAM,yEAG3BP,KAAKC,sBAAsB,CAC1BC,KAAM,SACNC,QAAS,sCACTC,MAAOC,IAAIC,WAAWC,MAAM,sDAC5BC,KAAMH,IAAIC,WAAWC,MAAM,2DAC3BE,YAAYJ,IAAIC,WAAWC,MAAM,gEAGnC,SAAKR,UAAU,cAAcC,KAAKU,mB,EAxBvBnB,CAAqBoB,KEA1CN,IAAIO,aAAaC,IAAI,wBAAwB,WAC3CR,IAAIS,cAAJ,IAAsB,yBACrBC,aAAaxB,GACbyB,mBACC,CACEC,KAAM,sBACNb,MAAOC,IAAIC,WAAWC,MAAM,kEAC5BW,WAAY,sCAEd,WACA,Q", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/ExtensionPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/components/SettingsPage.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/ExtensionPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "import ExtensionPage from 'flarum/components/ExtensionPage';\nimport Button from 'flarum/components/Button';\n\nexport default class SettingsPage extends ExtensionPage {\n  oninit(attrs) {\n    super.oninit(attrs);\n  }\n\n  content() {\n    return (\n      <div className=\"ExtensionPage-settings\">\n        <div className=\"container\">\n          {this.buildSettingComponent({\n            type: 'switch',\n            setting: 'moneyTransfer.moneyTransferClient1Customization',\n            label: app.translator.trans('wusong8899-transfer-money.admin.transfer-money-client-customization'),\n            help:app.translator.trans('wusong8899-transfer-money.admin.transfer-money-client-customization-help')\n          })}\n\n          {this.buildSettingComponent({\n            type: 'string',\n            setting: 'moneyTransfer.moneyTransferTimeZone',\n            label: app.translator.trans('wusong8899-transfer-money.admin.transfer-money-timezone'),\n            help: app.translator.trans('wusong8899-transfer-money.admin.transfer-money-timezone-help'),\n            placeholder:app.translator.trans('wusong8899-transfer-money.admin.transfer-money-timezone-default')\n          })}\n\n          <div className=\"Form-group\">{this.submitButton()}</div>\n        </div>\n      </div>\n    );\n  }\n\n}\n", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "import {extend, override} from 'flarum/extend';\nimport SettingsPage from './components/SettingsPage';\n\napp.initializers.add('wusong8899-money-transfer', () => {\n  app.extensionData.for('wusong8899-money-transfer')\n  .registerPage(SettingsPage)\n  .registerPermission(\n    {\n      icon: 'fas fa-exchange-alt',\n      label: app.translator.trans('wusong8899-transfer-money.admin.permission.allow_use_transfer_money'),\n      permission: 'transferMoney.allowUseTranferMoney',\n    },\n    'moderate',\n    90\n  )\n});\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "flarum", "core", "compat", "SettingsPage", "subClass", "superClass", "create", "constructor", "oninit", "attrs", "content", "className", "this", "buildSettingComponent", "type", "setting", "label", "app", "translator", "trans", "help", "placeholder", "submitButton", "ExtensionPage", "initializers", "add", "extensionData", "registerPage", "registerPermission", "icon", "permission"], "sourceRoot": ""}