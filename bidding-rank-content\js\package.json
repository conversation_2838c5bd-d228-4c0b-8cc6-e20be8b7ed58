{"private": true, "name": "@wusong8899/bidding-rank-content", "devDependencies": {"flarum-tsconfig": "^1.0.3", "flarum-webpack-config": "^2.0.2", "oxlint": "^1.11.2", "vite": "^7.1.2", "webpack-cli": "^6.0.1"}, "scripts": {"dev": "vite -c vite.config.admin.ts build --mode development && vite -c vite.config.forum.ts build --mode development", "dev:admin": "vite -c vite.config.admin.ts build --mode development", "dev:forum": "vite -c vite.config.forum.ts build --mode development", "dev:watch": "vite -c vite.config.admin.ts build --watch --mode development && vite -c vite.config.forum.ts build --watch --mode development", "build": "vite -c vite.config.admin.ts build --mode production && vite -c vite.config.forum.ts build --mode production", "lint": "oxlint .", "lint:fix": "oxlint . --fix"}}