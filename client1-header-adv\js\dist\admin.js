(function(i){"use strict";class r{constructor(t){this.extensionId=t,this.extensionData=i.extensionData.for(t)}registerTransitionTimeSetting(){return this.extensionData.registerSetting({setting:`${this.extensionId}.TransitionTime`,type:"number",label:i.translator.trans("wusong8899-client1.admin.TransitionTime")}),this}registerSlideSettings(t=30){for(let e=1;e<=t;e++)this.extensionData.registerSetting({setting:`${this.extensionId}.Link${e}`,type:"URL",label:i.translator.trans(`wusong8899-client1.admin.Link${e}`)}),this.extensionData.registerSetting({setting:`${this.extensionId}.Image${e}`,type:"URL",label:i.translator.trans(`wusong8899-client1.admin.Image${e}`)});return this}registerAllSettings(t=30){return this.registerTransitionTimeSetting().registerSlideSettings(t)}}const n={EXTENSION_ID:"wusong8899-client1-header-adv",MAX_SLIDES:30};function a(s=n.EXTENSION_ID,t=n.MAX_SLIDES){new r(s).registerAllSettings(t)}i.initializers.add("wusong8899/client1-header-adv",()=>{a()})})(flarum.core.compat["admin/app"]);
//# sourceMappingURL=admin.js.map

module.exports={};