(function(u,c,g){"use strict";function d(n){if(!n)return;n.querySelectorAll("li.TagTile").forEach(y=>{const t=y,f=t.querySelector("a.TagTile-info"),l=t.querySelector(".TagTile-name");if(!f)return;let o=null;const e=new URL(f.href,window.location.origin).pathname.split("/").filter(Boolean),a=e.indexOf("t"),i=e.indexOf("tags");if(a!==-1&&e[a+1]?o=e[a+1]:i!==-1&&e[i+1]?o=e[i+1]:e.length>0&&(o=e[e.length-1]),!o)return;const s=u.store.all("tags").find(r=>(typeof r.slug=="function"?r.slug():r.attribute&&r.attribute("slug"))===o);if(!s)return;const p=s.attribute?s.attribute("wusong8899BackgroundURL"):null;p?(t.style.background=`url(${p})`,t.style.backgroundSize="cover",t.style.backgroundPosition="center",t.style.backgroundRepeat="no-repeat",l&&(l.style.display="none")):(t.style.background="",t.style.backgroundSize="",t.style.backgroundPosition="",t.style.backgroundRepeat="",l&&(l.style.display=""))})}u.initializers.add("wusong8899-tag-background",()=>{c.extend(g.prototype,"oncreate",function(n){d(n.dom)}),c.extend(g.prototype,"onupdate",function(n){d(n.dom)})})})(flarum.core.compat["forum/app"],flarum.core.compat.extend,flarum.core.compat["tags/components/TagsPage"]);
//# sourceMappingURL=forum.js.map

module.exports={};