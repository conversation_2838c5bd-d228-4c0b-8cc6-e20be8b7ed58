(function(p,g,w,c){"use strict";class h extends c{}Object.assign(h.prototype,{id:c.attribute("id"),name:c.attribute("name"),url:c.attribute("url"),img:c.attribute("img"),desc:c.attribute("desc"),valueUsd:c.attribute("valueUsd"),sort:c.attribute("sort"),updateTime:c.attribute("updateTime")});const i={app:{extensionId:"wusong8899-client1-sync-tronscan",name:"Client1 Sync Tronscan",version:"1.0.0"},api:{endpoints:{tronscanList:"/api/syncTronscanList",tronscanAdd:"/api/syncTronscanList",tronscanUpdate:"/api/syncTronscanList/{id}",tronscanDelete:"/api/syncTronscanList/{id}",tronscanSort:"/api/syncTronscanList/order",tronscanValueUpdate:"/api/syncTronscanValueUsd"}},data:{apiResources:{tronscanList:"syncTronscanList"}},ui:{containerIds:{tronscanText:"TronscanTextContainer",swiperTag:"swiperTagContainer"},classes:{tronscanSwiper:"tronscanSwiper",tronscanSlide:"swiper-slide swiper-slide-tag",tronscanMask:"tronscanMask",tronscanIcon:"TronscanTextIcon",tronscanContainer:"TronscanTextContainer"}},swiper:{autoplay:{delay:3e3,disableOnInteraction:!1},speed:800,effect:"slide",grabCursor:!0,centeredSlides:!1,loop:!0},text:{containerText:"知名博彩公司USDT/TRC公开链钱包额度"}};class o{constructor(){this.tronscanListLoading=!1,this.tronscanList=null}static getInstance(){return o.instance||(o.instance=new o),o.instance}async loadTronscanList(){if(this.tronscanListLoading)return this.waitForTronscanList();if(this.tronscanList!==null)return this.tronscanList;this.tronscanListLoading=!0;try{const e=await p.store.find(i.data.apiResources.tronscanList).catch(()=>[]);return this.tronscanList=[],Array.isArray(e)&&this.tronscanList.push(...e),this.tronscanList}catch{return this.tronscanList=[],this.tronscanList}finally{this.tronscanListLoading=!1}}getTronscanList(){return this.tronscanList}clearCache(){this.tronscanList=null}async refreshTronscanList(){return this.clearCache(),this.loadTronscanList()}async waitForTronscanList(){return new Promise(e=>{const t=setInterval(()=>{!this.tronscanListLoading&&this.tronscanList!==null&&(clearInterval(t),e(this.tronscanList))},100)})}}class r{static createElement(e,t={},n=""){const s=document.createElement(e);return Object.entries(t).forEach(([a,l])=>{a==="className"?s.className=l:a==="style"?s.setAttribute("style",l):s.setAttribute(a,l)}),n&&(s.innerHTML=n),s}static getElementById(e){return document.getElementById(e)}static querySelector(e,t=document){try{return t.querySelector(e)}catch(n){return console.warn(`Invalid selector: ${e}`,n),null}}static querySelectorAll(e,t=document){try{return t.querySelectorAll(e)}catch(n){return console.warn(`Invalid selector: ${e}`,n),document.querySelectorAll("")}}static addEventListener(e,t,n,s=!1){try{e.addEventListener(t,n,s)}catch(a){console.error("Failed to add event listener:",a)}}static removeEventListener(e,t,n,s=!1){try{e.removeEventListener(t,n,s)}catch(a){console.error("Failed to remove event listener:",a)}}static setStyles(e,t){Object.entries(t).forEach(([n,s])=>{try{e.style.setProperty(n,s)}catch(a){console.warn(`Failed to set style ${n}: ${s}`,a)}})}static appendChild(e,t){try{e.appendChild(t)}catch(n){console.error("Failed to append child element:",n)}}static prependChild(e,t){try{e.insertBefore(t,e.firstChild)}catch(n){console.error("Failed to prepend child element:",n)}}static removeElement(e){try{e&&e.parentNode&&e.parentNode.removeChild(e)}catch(t){console.error("Failed to remove element:",t)}}}const u=class u{static isMobileDevice(){if(this.isMobile!==null)return this.isMobile;let e=!1;const t=navigator.userAgent||navigator.vendor||window.opera,n=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,s=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i;return(n.test(t)||s.test(t.substr(0,4)))&&(e=!0),this.isMobile=e,e}static getEventType(){return this.isMobileDevice()?"touchend":"click"}static getSwiperConfig(){const e=this.isMobileDevice();return{spaceBetween:e?90:10,slidesPerView:e?2:7}}static getTagSwiperConfig(){const e=this.isMobileDevice();return{spaceBetween:e?80:10,slidesPerView:e?4:7,breakpoints:{320:{slidesPerView:2,spaceBetween:60},480:{slidesPerView:3,spaceBetween:70},768:{slidesPerView:4,spaceBetween:80},1024:{slidesPerView:6,spaceBetween:10},1200:{slidesPerView:7,spaceBetween:10}}}}};u.isMobile=null;let m=u;class v{constructor(){this.dataLoader=o.getInstance()}async initialize(){await this.addTronscan()}async addTronscan(){const e=await this.dataLoader.loadTronscanList();if(r.getElementById(i.ui.containerIds.tronscanText)||e.length===0)return;this.createTronscanContainer();const t=this.createTronscanSwiper(),n=this.createTronscanWrapper(t);this.populateTronscanSlides(n,e),this.initializeTronscanSwiper()}createTronscanContainer(){const e=r.createElement("div",{id:i.ui.containerIds.tronscanText,className:i.ui.classes.tronscanContainer},`<div class='${i.ui.classes.tronscanIcon}'></div>${i.text.containerText}`),t=r.getElementById(i.ui.containerIds.swiperTag);return t&&r.appendChild(t,e),e}createTronscanSwiper(){const e=r.createElement("div",{className:`swiper ${i.ui.classes.tronscanSwiper}`}),t=r.getElementById(i.ui.containerIds.swiperTag);return t&&r.appendChild(t,e),e}createTronscanWrapper(e){const t=r.createElement("div",{className:"swiper-wrapper"});return r.appendChild(e,t),t}populateTronscanSlides(e,t){t.forEach(n=>{const s=this.createTronscanSlide(n);r.appendChild(e,s)})}createTronscanSlide(e){const t=r.createElement("div",{className:i.ui.classes.tronscanSlide}),n=e.name?e.name():"Unknown",s=e.valueUsd?e.valueUsd():"0",a=e.url?e.url():"#",l=e.img?e.img():"";return t.innerHTML=`
            <a href="${a}" target="_blank" class="tronscan-link">
                <div class="tronscan-content">
                    <img src="${l}" alt="${n}" class="tronscan-image" />
                    <div class="tronscan-info">
                        <div class="tronscan-name">${n}</div>
                        <div class="tronscan-value">$${s}</div>
                    </div>
                </div>
            </a>
        `,t}initializeTronscanSwiper(){try{const e=m.getTagSwiperConfig(),t={...i.swiper,spaceBetween:e.spaceBetween,slidesPerView:e.slidesPerView,breakpoints:e.breakpoints},n=new Swiper(`.${i.ui.classes.tronscanSwiper}`,t)}catch{}}}p.initializers.add("wusong8899-client1-sync-tronscan",()=>{p.store.models.syncTronscanList=h;const d=new v;g.extend(w.prototype,"view",function(e){this.isTagsPage()&&setTimeout(()=>{d.initialize().catch(()=>{})},100)}),w.prototype.isTagsPage=function(){const e=window.location.pathname;return e.includes("/tags")||e==="/"}})})(flarum.core.compat["forum/app"],flarum.core.compat["common/extend"],flarum.core.compat["forum/components/HeaderPrimary"],flarum.core.compat["common/Model"]);
//# sourceMappingURL=forum.js.map

module.exports={};