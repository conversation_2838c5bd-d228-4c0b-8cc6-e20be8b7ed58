{"version": 3, "file": "forum.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFf,EAAyBM,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,M,+BCLvD,MAAM,EAA+BC,OAAOC,KAAKC,OAAO,a,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAe,OCA1D,EAA+BF,OAAOC,KAAKC,OAAO,+B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,kC,aCAzC,SAASC,EAAgBhB,EAAGiB,GAMzC,OALAD,EAAkBf,OAAOiB,gBAAkB,SAAyBlB,EAAGiB,GAErE,OADAjB,EAAEmB,UAAYF,EACPjB,GAGFgB,EAAgBhB,EAAGiB,GCLb,SAASG,EAAeC,EAAUC,GAC/CD,EAASd,UAAYN,OAAOsB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCH,EAAeG,EAAUC,GCJ3B,MAAM,EAA+BT,OAAOC,KAAKC,OAAO,oB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,gB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,qB,aCAnBF,OAAOC,KAAKC,OAAO,2B,ICInCU,EAAAA,SAAAA,G,oFAInBC,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,I,EAGfC,UAAA,WACE,MAAO,gB,EAGTC,MAAA,WACE,OAAOC,IAAAA,WAAAA,MAAqB,+C,EAG9BC,QAAA,WAAU,WACR,OACE,SAAKH,UAAU,cACb,SAAKA,UAAU,QACb,SAAKA,UAAU,aAAaI,MAAM,uBAC/BC,IAAAA,UACC,CACEL,UAAW,yBACXM,QAASC,KAAKD,QACdE,QAAS,WACP,EAAKC,SAGTP,IAAAA,WAAAA,MAAqB,qC,EA7BdL,CAAoDa,KAApDb,EACZc,+BAAgC,EADpBd,EAEZe,6BAA8B,E,ICClBC,EAAAA,SAAAA,G,oFAInBf,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbQ,KAAKD,SAAU,EAEfC,KAAKO,OAASC,GAAAA,CAAO,IACrBR,KAAKS,SAAWD,GAAAA,CAAO,IACvBR,KAAKU,gBAAkBF,GAAAA,CAAO,IAC9BR,KAAKW,YAAcH,GAAAA,CAAO,K,EAG5Bf,UAAA,WACE,MAAO,gB,EAGTC,MAAA,WACE,OAAOC,IAAAA,WAAAA,MAAqB,4C,EAG9BC,QAAA,WAAU,WACR,OACE,SAAKH,UAAU,cACb,SAAKA,UAAU,QACb,SAAKA,UAAU,aAAaI,MAAM,uBAC9B,aACE,SAAKJ,UAAU,2BAA2BE,IAAAA,WAAAA,MAAqB,0CAC/D,WAAOiB,KAAK,SAASC,IAAI,IAAIC,SAAUd,KAAKD,QAASgB,UAAQ,EAACtB,UAAU,cAAcuB,KAAMhB,KAAKO,UAGnG,SAAKd,UAAU,2BAA2BE,IAAAA,WAAAA,MAAqB,4CAC/D,WAAOsB,UAAU,MAAMH,SAAUd,KAAKD,QAASgB,UAAQ,EAACtB,UAAU,cAAcuB,KAAMhB,KAAKS,WAE3F,SAAKhB,UAAU,2BAA2BE,IAAAA,WAAAA,MAAqB,mDAC/D,WAAOsB,UAAU,MAAMH,SAAUd,KAAKD,QAASgB,UAAQ,EAACtB,UAAU,cAAcuB,KAAMhB,KAAKU,kBAE3F,SAAKjB,UAAU,2BAA2BE,IAAAA,WAAAA,MAAqB,mDAC/D,WAAOsB,UAAU,MAAMH,SAAUd,KAAKD,QAASgB,UAAQ,EAACtB,UAAU,cAAcuB,KAAMhB,KAAKW,eAG/F,SAAKlB,UAAU,aAAaI,MAAM,uBAC/BC,IAAAA,UACC,CACED,MAAM,kBACNJ,UAAW,yBACXmB,KAAM,SACNb,QAASC,KAAKD,SAEhBJ,IAAAA,WAAAA,MAAqB,iCARzB,IAUGG,IAAAA,UACC,CACED,MAAM,kBACNJ,UAAW,SACXM,QAASC,KAAKD,QACdE,QAAS,WACP,EAAKC,SAGTP,IAAAA,WAAAA,MAAqB,yC,EASjCuB,SAAA,SAASC,GAAG,WACVA,EAAEC,iBAEFpB,KAAKD,SAAU,EAEfJ,IAAAA,MAAAA,aACc,sBACb0B,KAAK,CACJd,OAAOP,KAAKO,SACZE,SAAST,KAAKS,WACdC,gBAAgBV,KAAKU,kBACrBC,YAAYX,KAAKW,gBAElBW,MACC,SAACC,GACC5B,IAAAA,MAAAA,YAAsB4B,GACtB,EAAKxB,SAAU,EACfJ,IAAAA,MAAAA,KAAeL,MAZnB,OAeO,SAAC6B,GACN,EAAKpB,SAAU,M,EA3FAO,CAAuCH,KAAvCG,EACZF,+BAAgC,EADpBE,EAEZD,6BAA8B,ECTvC,MAAM,EAA+B3B,OAAOC,KAAKC,OAAO,yB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAc,M,aCE1C4C,EAAAA,SAAAA,G,kEAAAA,CAAuBC,KAC5C3D,OAAO4D,OAAOF,EAAepD,UAAW,CACtCuD,GAAIF,IAAAA,UAAgB,MACpBlB,OAAQkB,IAAAA,UAAgB,UACxBhB,SAAUgB,IAAAA,UAAgB,YAC1BG,iBAAkBH,IAAAA,UAAgB,oBAClCI,aAAcJ,IAAAA,UAAgB,gBAC9BK,mBAAoBL,IAAAA,UAAgB,sBACpCM,eAAgBN,IAAAA,UAAgB,kBAChCO,cAAeP,IAAAA,UAAgB,iBAC/BQ,YAAaR,IAAAA,UAAgB,eAC7BS,YAAaT,IAAAA,UAAgB,eAC7BU,SAAUV,IAAAA,OAAa,YACvBW,WAAYX,IAAAA,OAAa,gBCf3B,MAAM,EAA+B/C,OAAOC,KAAKC,OAAO,uB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,yB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAkB,U,aCAnE,MAAM,EAA+BF,OAAOC,KAAKC,OAAY,I,aCA7D,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,+B,aCAnBF,OAAOC,KAAKC,OAAO,oB,ICInCyD,EAAAA,SAAAA,G,4EAEnBC,KAAA,WACE,IAAOC,EAAYvC,KAAKwC,MAAjBD,SAGDhC,GAFYZ,IAAI8C,MAAMC,UAAU,6BAEvBH,EAAShC,UAClBoB,EAAKY,EAASZ,KACdlB,EAAW8B,EAAS9B,WACpBC,EAAkB6B,EAASX,mBAC3BjB,EAAc4B,EAASV,eAGvBc,GAFWJ,EAASJ,WACPI,EAASH,aACPG,EAASP,iBACxBY,EAAmBjD,IAAIkD,WAAWC,MAAqB,IAAfH,EAAiB,mDAAmD,qDAC5GI,EAAaR,EAASN,cACtBe,EAAaT,EAASL,cACxBe,EAAqB,sCAYzB,OACE,SAAKxD,UAVLwD,GADc,OAAbD,EACmB,qCAEF,IAAfL,EACmB,oCAEA,qCAMpB,aACE,WAAIhD,IAAIkD,WAAWC,MAAM,qCAAzB,MACCnB,EAFH,MAGE,WAAIhC,IAAIkD,WAAWC,MAAM,6CAAzB,MACCC,GAEH,aACE,WAAIpD,IAAIkD,WAAWC,MAAM,yCAAzB,MACCvC,EAFH,MAGE,WAAIZ,IAAIkD,WAAWC,MAAM,2CAAzB,MACCrC,EAJH,MAKE,WAAId,IAAIkD,WAAWC,MAAM,kDAAzB,MACCpC,EANH,MAOE,WAAIf,IAAIkD,WAAWC,MAAM,8CAAzB,MACCnC,GAEFqC,GACC,aACE,WAAIrD,IAAIkD,WAAWC,MAAM,+CAAzB,MACgB,IAAfH,GACC,UAAM9C,MAAM,aAAa+C,EAAzB,OAEc,IAAfD,GACC,UAAM9C,MAAM,eAAe+C,EAA3B,OAEF,WAAIjD,IAAIkD,WAAWC,MAAM,2CAAzB,MACCE,IAGHA,GACA,aACE,WAAIrD,IAAIkD,WAAWC,MAAM,+CAAzB,MACA,UAAMjD,MAAM,cAAcF,IAAIkD,WAAWC,MAAM,2D,EA/DtCT,CAA0Ca,KCG1CC,EAAAA,SAAAA,G,oFACnB5D,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbQ,KAAKD,SAAU,EACfC,KAAKoD,aAAc,EACnBpD,KAAKqD,mBAAqB,GAC1BrD,KAAKsD,e,EAGPhB,KAAA,WAAO,IACDvC,EADC,OAOL,OAJGC,KAAKD,UACNA,EAAUwD,IAAAA,UAA2B,CAAEC,KAAM,WAI7C,aACE,QAAI3D,MAAM,sCACPG,KAAKqD,mBAAmBI,KAAI,SAAClB,GAC5B,OACE,QAAImB,OAAQnB,EAASZ,KAAM9B,MAAM,8CAC9BwC,EAAkCsB,UAAU,CAAEpB,SAAAA,UAMrDvC,KAAKD,SAA4C,IAAjCC,KAAKqD,mBAAmBO,QACxC,aACE,SAAK/D,MAAM,yFAAyFF,IAAAA,WAAAA,MAAqB,4CAI3HI,GAAWC,KAAK6D,kBAChB,SAAKhE,MAAM,kCACT,EAAC,IAAD,CAAQJ,UAAW,yBAA0BqB,SAAUd,KAAKD,QAASA,QAASC,KAAKD,QAASE,QAAS,kBAAM,EAAK6D,aAC7GnE,IAAAA,WAAAA,MAAqB,8CAK3BI,GAAW,SAAKN,UAAU,2BAA2BM,K,EAM5D8D,eAAA,WACE,OAAO7D,KAAKoD,a,EAGdU,SAAA,WACE9D,KAAKD,SAAU,EACfC,KAAKsD,YAAYtD,KAAKqD,mBAAmBO,S,EAG3CG,aAAA,SAAaC,GAOX,OANAhE,KAAKoD,cAAgBY,EAAQC,QAAQC,SAAWF,EAAQC,QAAQC,MAAMC,KACtE,GAAGC,KAAKC,MAAMrE,KAAKqD,mBAAoBW,GAEvChE,KAAKD,SAAU,EACfuE,EAAEC,SAEKP,G,EAGTV,YAAA,SAAYkB,GACV,YADsB,IAAZA,IAAAA,EAAS,GACZ7E,IAAAA,MAAAA,KACC,gCAAiC,CACrC8E,KAAM,CACJD,OAAAA,KAHC,OAME,eACNlD,KAAKtB,KAAK+D,aAAaW,KAAK1E,Q,EA3EdmD,CAA0CD,KCJ1CyB,EAAAA,SAAAA,G,oFACnBpF,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbQ,KAAK4E,SAASN,EAAEO,MAAMC,MAAM,c,EAG9BlF,QAAA,WACE,GAAGD,IAAIoF,QAAQC,MACSrF,IAAIoF,QAAQC,KAAKrD,OAClB3B,KAAKgF,KAAKrD,KAG7B,OACE,aACGwB,EAAkCQ,UAAU,CAC3CsB,OAAQ,CACND,KAAMhF,KAAKgF,U,EAhBNL,CAAsCO,KCH3D,MAAM,EAA+BxG,OAAOC,KAAKC,OAAO,2B,ICGnCuG,EAAAA,SAAAA,G,oFACnBC,KAAA,WACE,OAAO,M,EAGTC,KAAA,WACE,OAAO1F,IAAAA,MAAU,iCAAkC,CACjD2F,SAAU3F,IAAAA,QAAAA,KAAAA,c,EAIdC,QAAA,WAEE,OADqBI,KAAKwC,MAAM+C,aAAaC,UACtC7F,IAAAA,WAAAA,MAAqB,qE,EAG9B8F,QAAA,WACE,IAAMF,EAAevF,KAAKwC,MAAM+C,aAAaC,UACvC7C,EAAe4C,EAAavD,gBAC5B0D,EAAeH,EAAa5D,KAElC,OAAkB,IAAfgB,EACMhD,IAAAA,WAAAA,MAAqB,qEAAqE,CAACgC,GAAG+D,IAC/E,IAAf/C,EACAhD,IAAAA,WAAAA,MAAqB,oEAAoE,CAACgC,GAAG+D,SADhG,G,EAvBWP,C,MAAmCQ,ICHxD,MAAM,EAA+BjH,OAAOC,KAAKC,OAAO,mB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,wB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCQnCgH,EAAAA,SAAAA,G,oFACnBrG,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,I,EAGf8C,KAAA,WAAO,WACL,OACE,SAAK7C,UAAU,wBACZoG,IAAAA,UAAAA,OAED,SAAKpG,UAAU,aACb,SAAKA,UAAU,oBACb,SAAKA,UAAU,yBACb,YAAKqG,GAAAA,CAAUD,IAAAA,UAAAA,eAAmCE,aAEpD,SAAKlG,MAAM,0FACT,SAAKA,MAAM,cAAcmG,IAAI,uDAD/B,IAC6FrG,IAAIkD,WAAWC,MAAM,4CAElH,SAAKjD,MAAM,uCACT,SAAKA,MAAM,0DAA0DI,QAAS,kBAAM,EAAKgG,eACzF,SAAKpG,MAAM,aAAaqG,MAAM,gBAC5B,WAAOpF,UAAQ,EAACjB,MAAM,8BAA8BqG,MAAM,cAActF,KAAK,SAASuF,YAAaxG,IAAIkD,WAAWC,MAAM,8D,EAStImD,UAAA,WACKtG,IAAIoF,QAAQC,KACbrF,IAAIyG,MAAMC,KAAK/F,GAEfX,IAAIyG,MAAMC,KAAKC,M,EAlCAV,CAAgCW,KCIrD5G,IAAAA,aAAAA,IAAqB,yBAAyB,WAC5CA,IAAAA,OAAAA,eAA+B,CAC7B6G,KAAM,kBACN7C,UAAWiC,GAGbjG,IAAAA,MAAAA,OAAAA,mBAAsC6B,EACtC7B,IAAAA,uBAAAA,mBAAgD8G,GAEhDC,EAAAA,EAAAA,QAAOC,IAAAA,UAAyB,QAAQ,SAAUnH,GAC9C,IAAMoH,EAAYjH,IAAAA,QAAAA,IAAgB,aAElC,GAAGiH,EACC,GAAe,SAAZA,QAED,IAAIC,EAAOC,aAAY,WACnB,GAAGC,EAAE,uBAAuBnD,OAAO,IAC/BoD,cAAcH,IAEVE,EAAE,uBAAuBE,SAAS,8BAA6B,CACjE,IAAIC,EAAO,uLACwGvH,IAAAA,WAAAA,MAAqB,2CAD7H,oUAM6GA,IAAAA,WAAAA,MAAqB,sDANlI,qBAUXoH,EAAEG,GAAMC,YAAY,uBACpBJ,EAAE,uBAAuBK,SAAS,6BAElCL,EAAE,mCAAmCM,GAAG,SAAQ,WAC3C1H,IAAAA,QAAAA,KACDA,IAAAA,MAAAA,KAAeW,GAEfX,IAAAA,MAAAA,KAAe2G,WAK3B,QAKZI,EAAAA,EAAAA,QAAOY,IAAAA,UAA4B,qBAAqB,SAAUC,GAChEA,EAAMC,IAAI,qBAAsB,CAC9BC,KAAM,qBACNrC,KAAM,wBACNsC,MAAO/H,IAAAA,WAAAA,MACL,mECzDNA,IAAIgI,OAAO,kCAAoC,CAC7CnB,KAAM,yCACN7C,UAAWgB,IAGb+B,EAAAA,EAAAA,QAAOxB,IAAAA,UAAoB,YAAY,SAAUqC,EAAMvC,GAChDrF,IAAIoF,QAAQC,MACSrF,IAAIoF,QAAQC,KAAKrD,MAClB3B,KAAKgF,KAAKrD,MAG7B4F,EAAMC,IACJ,4BACAI,IAAAA,UAAqB,CACjBvC,KAAM1F,IAAIkF,MAAM,iCAAkC,CAChDS,SAAUtF,KAAKgF,KAAKM,aAEtBF,KAAM,yBAER,CACEzF,IAAIkD,WAAWC,MACb,wDAIN,W", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/app']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/NotificationGrid']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/components/HeaderPrimary']\"", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Modal']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['utils/Stream']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Alert']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/userSubmissionApplicationSubmitSuccessModal.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/userSubmissionApplicationModal.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LogInModal']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Model']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/UserSubmission.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/UserPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LinkButton']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Component']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['app']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LoadingIndicator']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['helpers/username']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/userSubmissionApplicationListItem.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/userSubmissionApplicationListPage.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/userSubmissionApplicationPage.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Notification']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/userSubmissionNotification.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Page']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/IndexPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/listItems']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/userSubmissionIndexPage.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/index.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addUserPage.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/NotificationGrid'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/components/HeaderPrimary'];", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Modal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['utils/Stream'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Alert'];", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class userSubmissionApplicationSubmitSuccessModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-user-submission.forum.submit-success');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n                },\n              },\n              app.translator.trans('wusong8899-user-submission.lib.ok')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Stream from 'flarum/utils/Stream';\nimport Button from 'flarum/components/Button';\nimport Alert from 'flarum/common/components/Alert';\nimport userSubmissionApplicationSubmitSuccessModal from './userSubmissionApplicationSubmitSuccessModal';\n\nexport default class userSubmissionApplicationModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loading = false;\n\n    this.amount = Stream(\"\");\n    this.platform = Stream(\"\");\n    this.platformAccount = Stream(\"\");\n    this.userAccount = Stream(\"\");\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-user-submission.forum.item-header');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n              <div>\n                <div className=\"userSubmissionDataLabel\">{app.translator.trans('wusong8899-user-submission.lib.list-amount')}</div>\n                <input type=\"number\" min=\"0\" disabled={this.loading} required className=\"FormControl\" bidi={this.amount} />\n              </div>\n\n              <div className=\"userSubmissionDataLabel\">{app.translator.trans('wusong8899-user-submission.lib.list-platform')}</div>\n              <input maxlength=\"500\" disabled={this.loading} required className=\"FormControl\" bidi={this.platform} />\n\n              <div className=\"userSubmissionDataLabel\">{app.translator.trans('wusong8899-user-submission.lib.list-platformAccount')}</div>\n              <input maxlength=\"500\" disabled={this.loading} required className=\"FormControl\" bidi={this.platformAccount} />\n\n              <div className=\"userSubmissionDataLabel\">{app.translator.trans('wusong8899-user-submission.lib.list-userAccountFull')}</div>\n              <input maxlength=\"500\" disabled={this.loading} required className=\"FormControl\" bidi={this.userAccount} />\n          </div>\n\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                style:'min-width:66px;',\n                className: 'Button Button--primary',\n                type: 'submit',\n                loading: this.loading,\n              },\n              app.translator.trans('wusong8899-user-submission.lib.ok')\n            )}&nbsp;\n            {Button.component(\n              {\n                style:'min-width:66px;',\n                className: 'Button',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n                }\n              },\n              app.translator.trans('wusong8899-user-submission.lib.cancel')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    this.loading = true;\n\n    app.store\n    .createRecord(\"userSubmissionList\")\n    .save({\n      amount:this.amount(),\n      platform:this.platform(),\n      platformAccount:this.platformAccount(),\n      userAccount:this.userAccount()\n    })\n    .then(\n      (result) => {\n        app.store.pushPayload(result);\n        this.loading = false;\n        app.modal.show(userSubmissionApplicationSubmitSuccessModal);\n      }\n    )\n    .catch((e) => {\n      this.loading = false;\n    });\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LogInModal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Model'];", "import Model from \"flarum/Model\";\n\nexport default class UserSubmission extends Model {}\nObject.assign(UserSubmission.prototype, {\n  id: Model.attribute(\"id\"),\n  amount: Model.attribute(\"amount\"),\n  platform: Model.attribute(\"platform\"),\n  platform_account: Model.attribute(\"platform_account\"),\n  user_account: Model.attribute(\"user_account\"),\n  submission_user_id: Model.attribute(\"submission_user_id\"),\n  review_user_id: Model.attribute(\"review_user_id\"),\n  review_result: Model.attribute(\"review_result\"),\n  assigned_at: Model.attribute(\"assigned_at\"),\n  reviewed_at: Model.attribute(\"reviewed_at\"),\n  fromUser: Model.hasOne(\"fromUser\"),\n  reviewUser: Model.hasOne(\"reviewUser\"),\n});\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/UserPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LinkButton'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Component'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['helpers/username'];", "import Component from \"flarum/Component\";\nimport Button from 'flarum/components/Button';\nimport username from \"flarum/helpers/username\";\n\nexport default class userSubmissionApplicationListItem extends Component {\n\n  view() {\n    const {itemData} = this.attrs;\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n\n    const amount = itemData.amount();\n    const id = itemData.id();\n    const platform = itemData.platform();\n    const platformAccount = itemData.platform_account();\n    const userAccount = itemData.user_account();\n    const fromUser = itemData.fromUser();\n    const reviewUser = itemData.reviewUser();\n    const reviewResult = itemData.review_result();\n    const reviewResultText = app.translator.trans(reviewResult===1?'wusong8899-user-submission.lib.list-submission-accept':'wusong8899-user-submission.lib.list-submission-decline');\n    const assignedAt = itemData.assigned_at();\n    const reviewedAt = itemData.reviewed_at();\n    let containerClassName = \"userSubmissionApplicationContainer \";\n\n    if(reviewedAt===null){\n      containerClassName+=\"userSubmissionApplicationReviewing\";\n    }else{\n      if(reviewResult===1){\n        containerClassName+=\"userSubmissionApplicationAccepted\";\n      }else{\n        containerClassName+=\"userSubmissionApplicationDeclined\";\n      }\n    }\n\n    return (\n      <div className={containerClassName}>\n        <div>\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-id')}: </b>\n          {id}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-assignedAt')}: </b>\n          {assignedAt}\n        </div>\n        <div>\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-amount')}: </b>\n          {amount}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-platform')}: </b>\n          {platform}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-platformAccount')}: </b>\n          {platformAccount}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-userAccount')}: </b>\n          {userAccount}\n        </div>\n        {reviewedAt && (\n          <div>\n            <b>{app.translator.trans('wusong8899-user-submission.lib.list-reviewResult')}: </b>\n            {reviewResult===0 && (\n              <span style=\"color:red\">{reviewResultText}&nbsp;|&nbsp;</span>\n            )}\n            {reviewResult===1 && (\n              <span style=\"color:green\">{reviewResultText}&nbsp;|&nbsp;</span>\n            )}\n            <b>{app.translator.trans('wusong8899-user-submission.lib.list-reviewAt')}: </b>\n            {reviewedAt}\n          </div>\n        )}\n        {!reviewedAt && (\n          <div>\n            <b>{app.translator.trans('wusong8899-user-submission.lib.list-reviewResult')}: </b>\n            <span style=\"color:grey\">{app.translator.trans('wusong8899-user-submission.lib.list-submission-reviewing')}</span>\n          </div>\n        )}\n      </div>\n    );\n  }\n}\n", "import Component from \"flarum/Component\";\nimport app from \"flarum/app\";\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\nimport Button from \"flarum/components/Button\";\n\nimport userSubmissionApplicationListItem from \"./userSubmissionApplicationListItem\";\n\nexport default class userSubmissionApplicationListPage extends Component {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loading = true;\n    this.moreResults = false;\n    this.userSubmissionList = [];\n    this.loadResults();\n  }\n\n  view() {\n    let loading;\n\n    if(this.loading){\n      loading = LoadingIndicator.component({ size: \"large\" });\n    }\n\n    return (\n      <div>\n        <ul style=\"padding:0px;list-style-type: none;\">\n          {this.userSubmissionList.map((itemData) => {\n            return (\n              <li itemID={itemData.id()} style=\"margin-top:5px;background: var(--body-bg);\">\n                {userSubmissionApplicationListItem.component({ itemData })}\n              </li>\n            );\n          })}\n        </ul>\n\n        {!this.loading && this.userSubmissionList.length===0 && (\n          <div>\n            <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;line-height: 100px;\">{app.translator.trans(\"wusong8899-decoration-store.lib.list-empty\")}</div>\n          </div>\n        )}\n\n        {!loading && this.hasMoreResults() && (\n          <div style=\"text-align:center;padding:20px\">\n            <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n              {app.translator.trans('wusong8899-user-submission.lib.list-load-more')}\n            </Button>\n          </div>\n        )}\n\n        {loading && <div className=\"UserSubmission-loadMore\">{loading}</div>}\n\n      </div>\n    );\n  }\n  \n  hasMoreResults() {\n    return this.moreResults;\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.userSubmissionList.length);\n  }\n\n  parseResults(results) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.userSubmissionList, results);\n\n    this.loading = false;\n    m.redraw();\n\n    return results;\n  }\n\n  loadResults(offset = 0) {\n    return app.store\n      .find(\"userSubmissionApplicationList\", {\n        page: {\n          offset\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n\n}\n", "import UserPage from \"flarum/components/UserPage\";\nimport userSubmissionApplicationListPage from \"./userSubmissionApplicationListPage\";\n\nexport default class userSubmissionApplicationPage extends UserPage {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loadUser(m.route.param(\"username\"));\n  }\n\n  content() {\n    if(app.session.user){\n      const currentUserID = app.session.user.id();\n      const targetUserID = this.user.id();\n\n      if(currentUserID===targetUserID){\n        return (\n          <div>\n            {userSubmissionApplicationListPage.component({\n              params: {\n                user: this.user,\n              },\n            })}\n          </div>\n        );\n      }\n    }\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Notification'];", "import app from 'flarum/forum/app';\nimport Notification from \"flarum/components/Notification\";\n\nexport default class userSubmissionNotification extends Notification {\n  icon() {\n    return null;\n  }\n\n  href() {\n    return app.route(\"user.userSubmissionApplication\", {\n      username: app.session.user.username(),\n    });\n  }\n\n  content() {\n    const notification = this.attrs.notification.subject();\n    return app.translator.trans('wusong8899-user-submission.forum.notification-submission-result-title');\n  }\n\n  excerpt() {\n    const notification = this.attrs.notification.subject();\n    const reviewResult = notification.review_result();\n    const submissionId = notification.id();\n\n    if(reviewResult===1){\n      return app.translator.trans('wusong8899-user-submission.forum.notification-submission-result-success',{id:submissionId});\n    }else if(reviewResult===0){\n      return app.translator.trans('wusong8899-user-submission.forum.notification-submission-result-failed',{id:submissionId});\n    }\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Page'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/IndexPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/listItems'];", "import Page from 'flarum/components/Page';\nimport IndexPage from 'flarum/components/IndexPage';\nimport listItems from 'flarum/common/helpers/listItems';\nimport Button from 'flarum/components/Button';\n\nimport userSubmissionApplicationModal from './userSubmissionApplicationModal';\nimport LogInModal from \"flarum/components/LogInModal\";\n\nexport default class userSubmissionIndexPage extends Page {\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  view() {\n    return (\n      <div className=\"MoneyLeaderboardPage\">\n        {IndexPage.prototype.hero()}\n\n        <div className=\"container\">\n          <div className=\"sideNavContainer\">\n            <nav className=\"IndexPage-nav sideNav\">\n              <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\n            </nav>\n            <div style=\"display: flex;align-items: center;padding-top: 10px;font-weight: bold;font-size: 14px;\">\n              <img style=\"width:24px;\" src=\"https://mutluresim.com/images/2023/03/26/ViOux.png\" />&nbsp;{app.translator.trans(\"wusong8899-user-submission.forum.item-header\")}\n            </div>\n            <div style=\"padding-top: 10px;position:relative\">\n              <div style=\"position: absolute;height: 37px;width: 100%;z-index: 1;\" onclick={() => this.openModal()}></div>\n              <div style=\"width:100%\" class=\"Search-input\">\n                <input disabled style=\"width: 100%;font-size:12px;\" class=\"FormControl\" type=\"search\" placeholder={app.translator.trans('wusong8899-user-submission.forum.item-input-placeholder')} />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  openModal(){\n    if(app.session.user){\n      app.modal.show(userSubmissionApplicationModal);\n    }else{\n      app.modal.show(LogInModal);\n    }\n  }\n}\n", "import app from 'flarum/forum/app';\r\nimport { extend } from 'flarum/extend';\r\nimport NotificationGrid from \"flarum/components/NotificationGrid\";\r\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\r\nimport userSubmissionApplicationModal from './components/userSubmissionApplicationModal';\r\nimport LogInModal from \"flarum/components/LogInModal\";\r\n\r\nimport UserSubmission from \"./model/UserSubmission\";\r\nimport addUserPage from './addUserPage';\r\nimport UserSubmissionNotification from \"./components/userSubmissionNotification\";\r\nimport userSubmissionIndexPage from './components/userSubmissionIndexPage';\r\n\r\napp.initializers.add('wusong8899-user-submission', () => {\r\n  app.routes['userSubmission'] = {\r\n    path: '/userSubmission',\r\n    component: userSubmissionIndexPage,\r\n  };\r\n\r\n  app.store.models.userSubmissionList = UserSubmission;\r\n  app.notificationComponents.userSubmissionList = UserSubmissionNotification;\r\n\r\n  extend(HeaderPrimary.prototype, 'view', function (vnode) {\r\n      const routeName = app.current.get('routeName');\r\n\r\n      if(routeName){\r\n          if(routeName!==\"tags\"){\r\n          }else{\r\n            let task = setInterval(function(){\r\n                if($(\".swiperTagContainer\").length>0){\r\n                    clearInterval(task);\r\n\r\n                    if(!$(\".swiperTagContainer\").hasClass(\"UserSubmissionApplication\")){\r\n                      let html = '<div style=\"display: flex;align-items: center;font-weight: bold;font-size: 14px;\">'+\r\n                                    '  <img style=\"width:22px;\" src=\"https://mutluresim.com/images/2023/04/04/jyzez.png\" />&nbsp;&nbsp;'+app.translator.trans(\"wusong8899-user-submission.forum.item-header\")+\r\n                                    '</div>'+\r\n                                    '<div style=\"padding-top: 10px;position:relative\">'+\r\n                                    '  <div class=\"UserSubmissionApplicationInput\" style=\"position: absolute;height: 37px;width: 100%;z-index: 1;\"></div>'+\r\n                                    '  <div style=\"width:100%\" class=\"Search-input\">'+\r\n                                    '    <input disabled style=\"width: 100%;font-size:12px;\" class=\"FormControl\" type=\"search\" placeholder=\"'+app.translator.trans('wusong8899-user-submission.forum.item-input-placeholder')+'\" />'+\r\n                                    '  </div>'+\r\n                                    '</div>';\r\n\r\n                      $(html).insertAfter(\".swiperTagContainer\");\r\n                      $(\".swiperTagContainer\").addClass(\"UserSubmissionApplication\");\r\n\r\n                      $(\".UserSubmissionApplicationInput\").on(\"click\",function(){\r\n                        if(app.session.user){\r\n                          app.modal.show(userSubmissionApplicationModal);\r\n                        }else{\r\n                          app.modal.show(LogInModal);\r\n                        }\r\n                      });\r\n                    }\r\n                }\r\n            },10);\r\n          }\r\n      }\r\n  });\r\n\r\n  extend(NotificationGrid.prototype, \"notificationTypes\", function (items) {\r\n    items.add(\"userSubmissionList\", {\r\n      name: \"userSubmissionList\",\r\n      icon: \"fas fa-file-signature\",\r\n      label: app.translator.trans(\r\n        \"wusong8899-user-submission.forum.notification-submission-result\"\r\n      ),\r\n    });\r\n  });\r\n\r\n  addUserPage();\r\n});\r\n\r\n", "import { extend } from \"flarum/extend\";\nimport UserPage from \"flarum/components/UserPage\";\nimport LinkButton from \"flarum/components/LinkButton\";\n\nimport userSubmissionApplicationPage from './components/userSubmissionApplicationPage';\n\nexport default function () {\n  app.routes[\"user.userSubmissionApplication\"] = {\n    path: \"/u/:username/userSubmissionApplication\",\n    component: userSubmissionApplicationPage,\n  };\n\n  extend(UserPage.prototype, \"navItems\", function (items,user) {\n      if(app.session.user){\n        const currentUserID = app.session.user.id();\n        const targetUserID = this.user.id();\n\n        if(currentUserID==targetUserID){\n          items.add(\n            \"userSubmissionApplication\",\n            LinkButton.component({\n                href: app.route(\"user.userSubmissionApplication\", {\n                  username: this.user.username(),\n                }),\n                icon: \"fas fa-file-signature\",\n              },\n              [\n                app.translator.trans(\n                  \"wusong8899-user-submission.forum.application-list-title\"\n                )\n              ]\n            ),\n            10\n          );\n        }\n      }\n  });\n}\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "flarum", "core", "compat", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "userSubmissionApplicationSubmitSuccessModal", "oninit", "vnode", "className", "title", "app", "content", "style", "<PERSON><PERSON>", "loading", "this", "onclick", "hide", "Modal", "isDismissibleViaBackdropClick", "isDismissibleViaCloseButton", "userSubmissionApplicationModal", "amount", "Stream", "platform", "platformAccount", "userAccount", "type", "min", "disabled", "required", "bidi", "maxlength", "onsubmit", "e", "preventDefault", "save", "then", "result", "UserSubmission", "Model", "assign", "id", "platform_account", "user_account", "submission_user_id", "review_user_id", "review_result", "assigned_at", "reviewed_at", "fromUser", "reviewUser", "userSubmissionApplicationListItem", "view", "itemData", "attrs", "forum", "attribute", "reviewResult", "reviewResultText", "translator", "trans", "assignedAt", "reviewedAt", "containerClassName", "Component", "userSubmissionApplicationListPage", "moreResults", "userSubmissionList", "loadResults", "LoadingIndicator", "size", "map", "itemID", "component", "length", "hasMoreResults", "loadMore", "parseResults", "results", "payload", "links", "next", "push", "apply", "m", "redraw", "offset", "page", "bind", "userSubmissionApplicationPage", "loadUser", "route", "param", "session", "user", "params", "UserPage", "userSubmissionNotification", "icon", "href", "username", "notification", "subject", "excerpt", "submissionId", "Notification", "userSubmissionIndexPage", "IndexPage", "listItems", "toArray", "src", "openModal", "class", "placeholder", "modal", "show", "LogInModal", "Page", "path", "UserSubmissionNotification", "extend", "HeaderPrimary", "routeName", "task", "setInterval", "$", "clearInterval", "hasClass", "html", "insertAfter", "addClass", "on", "NotificationGrid", "items", "add", "name", "label", "routes", "LinkButton"], "sourceRoot": ""}