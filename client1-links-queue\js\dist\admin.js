/*! For license information please see admin.js.LICENSE.txt */
(()=>{var t={n:e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},d:(e,n)=>{for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};(()=>{"use strict";function e(t,n){return e=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},e(t,n)}function n(t,n){t.prototype=Object.create(n.prototype),t.prototype.constructor=t,e(t,n)}flarum.core.compat.extend;const o=flarum.core.compat["components/ExtensionPage"];var i=t.n(o);flarum.core.compat["components/LoadingIndicator"];const r=flarum.core.compat["components/Button"];var a=t.n(r);const l=flarum.core.compat["components/Modal"];var s=t.n(l);const u=flarum.core.compat["utils/Stream"];var c=t.n(u),d=function(t){function e(){return t.apply(this,arguments)||this}n(e,t);var o=e.prototype;return o.oninit=function(e){t.prototype.oninit.call(this,e),this.LinksQueueItemData=this.attrs.LinksQueueItemData,this.settingType="add",this.LinksQueueItemData?(this.settingType="edit",this.itemName=c()(this.LinksQueueItemData.name()),this.itemUrl=c()(this.LinksQueueItemData.links())):(this.itemName=c()(""),this.itemUrl=c()(""))},o.className=function(){return"Modal--Medium"},o.title=function(){return"add"===this.settingType?app.translator.trans("wusong8899-links-queue.admin.settings.item-add"):app.translator.trans("wusong8899-links-queue.admin.settings.item-edit")},o.content=function(){var t=this;return m("div",{className:"Modal-body"},m("div",{className:"Form"},m("div",{className:"Form-group",style:"text-align: center;"},m("div",null,m("div",{class:"GuaGuaLeSettingsLabel"},app.translator.trans("wusong8899-links-queue.admin.settings.item-name")),m("input",{maxlength:"255",required:!0,className:"FormControl",bidi:this.itemName}),m("div",{class:"GuaGuaLeSettingsLabel"},app.translator.trans("wusong8899-links-queue.admin.settings.item-url")),m("input",{maxlength:"500",required:!0,className:"FormControl",bidi:this.itemUrl}))),m("div",{className:"Form-group",style:"text-align: center;"},a().component({className:"Button Button--primary",type:"submit",loading:this.loading},"add"===this.settingType?app.translator.trans("wusong8899-links-queue.admin.data-add"):app.translator.trans("wusong8899-links-queue.admin.data-save"))," ",a().component({className:"Button guagualeButton--gray",loading:this.loading,onclick:function(){t.hide()}},app.translator.trans("wusong8899-links-queue.admin.cancel")))))},o.onsubmit=function(t){var e=this;t.preventDefault(),this.loading=!0,"edit"===this.settingType?this.LinksQueueItemData.save({name:this.itemName(),url:this.itemUrl()}).then(function(){return e.hide()},function(t){e.loading=!1,e.handleErrors(t)}):app.store.createRecord("linksQueueList").save({name:this.itemName(),url:this.itemUrl()}).then(function(t){location.reload()}).catch(function(t){e.loading=!1,e.handleErrors(linksQueueList)})},e}(s());d.isDismissible=!1;const h=flarum.core.compat.Component;var p=t.n(h),f=function(t){function e(){return t.apply(this,arguments)||this}n(e,t);var o=e.prototype;return o.oninit=function(e){t.prototype.oninit.call(this,e),this.LinksQueueItemData=this.attrs.LinksQueueItemData,this.loading=!1},o.className=function(){return"Modal--small"},o.title=function(){return app.translator.trans("wusong8899-links-queue.admin.settings.item-delete-confirmation")},o.content=function(){var t=this;return m("div",{className:"Modal-body"},m("div",{className:"Form-group",style:"text-align: center;"},a().component({className:"Button Button--primary",type:"submit",loading:this.loading},app.translator.trans("wusong8899-links-queue.admin.confirm"))," ",a().component({className:"Button guagualeButton--gray",loading:this.loading,onclick:function(){t.hide()}},app.translator.trans("wusong8899-links-queue.admin.cancel"))))},o.onsubmit=function(t){t.preventDefault(),this.loading=!0,this.LinksQueueItemData.delete().then(function(t){location.reload()})},e}(s());f.isDismissible=!1;var g=function(t){function e(){return t.apply(this,arguments)||this}n(e,t);var o=e.prototype;return o.view=function(){var t=this,e=this.attrs.LinksQueueItemData,n=e.id(),o=e.name(),i=e.links();return e.sort(),m("div",{style:"border: 1px dotted var(--control-color);padding: 10px;border-radius: 4px;"},m("div",null,m("div",{style:"padding-top: 5px;"},m(a(),{className:"Button Button--primary",onclick:function(){return t.editItem(e)}},app.translator.trans("wusong8899-links-queue.admin.settings.item-edit"))," ",m(a(),{style:"font-weight:bold;width:66px;",className:"Button Button--danger",onclick:function(){return t.deleteItem(e)}},app.translator.trans("wusong8899-links-queue.admin.settings.item-delete")),"  ",m("b",null,app.translator.trans("wusong8899-links-queue.admin.settings.item-id"),": "),n," | ",m("b",null,app.translator.trans("wusong8899-links-queue.admin.settings.item-name"),": "),o," | ",m("b",null,app.translator.trans("wusong8899-links-queue.admin.settings.item-url"),": "),i," ")))},o.editItem=function(t){app.modal.show(d,{LinksQueueItemData:t})},o.deleteItem=function(t){app.modal.show(f,{LinksQueueItemData:t})},e}(p());function v(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,o)}return n}function b(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?v(Object(n),!0).forEach(function(e){w(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}function w(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function D(){return D=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},D.apply(this,arguments)}function E(t){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(t)}var S=E(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),_=E(/Edge/i),T=E(/firefox/i),x=E(/safari/i)&&!E(/chrome/i)&&!E(/android/i),k=E(/iP(ad|od|hone)/i),C=E(/chrome/i)&&E(/android/i),N={capture:!1,passive:!1};function I(t,e,n){t.addEventListener(e,n,!S&&N)}function O(t,e,n){t.removeEventListener(e,n,!S&&N)}function M(t,e){if(e){if(">"===e[0]&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch(t){return!1}return!1}}function P(t){return t.host&&t!==document&&t.host.nodeType?t.host:t.parentNode}function A(t,e,n,o){if(t){n=n||document;do{if(null!=e&&(">"===e[0]?t.parentNode===n&&M(t,e):M(t,e))||o&&t===n)return t;if(t===n)break}while(t=P(t))}return null}var B,L=/\s+/g;function R(t,e,n){if(t&&e)if(t.classList)t.classList[n?"add":"remove"](e);else{var o=(" "+t.className+" ").replace(L," ").replace(" "+e+" "," ");t.className=(o+(n?" "+e:"")).replace(L," ")}}function X(t,e,n){var o=t&&t.style;if(o){if(void 0===n)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(n=t.currentStyle),void 0===e?n:n[e];e in o||-1!==e.indexOf("webkit")||(e="-webkit-"+e),o[e]=n+("string"==typeof n?"":"px")}}function Y(t,e){var n="";if("string"==typeof t)n=t;else do{var o=X(t,"transform");o&&"none"!==o&&(n=o+" "+n)}while(!e&&(t=t.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(n)}function F(t,e,n){if(t){var o=t.getElementsByTagName(e),i=0,r=o.length;if(n)for(;i<r;i++)n(o[i],i);return o}return[]}function j(){return document.scrollingElement||document.documentElement}function Q(t,e,n,o,i){if(t.getBoundingClientRect||t===window){var r,a,l,s,u,c,d;if(t!==window&&t.parentNode&&t!==j()?(a=(r=t.getBoundingClientRect()).top,l=r.left,s=r.bottom,u=r.right,c=r.height,d=r.width):(a=0,l=0,s=window.innerHeight,u=window.innerWidth,c=window.innerHeight,d=window.innerWidth),(e||n)&&t!==window&&(i=i||t.parentNode,!S))do{if(i&&i.getBoundingClientRect&&("none"!==X(i,"transform")||n&&"static"!==X(i,"position"))){var h=i.getBoundingClientRect();a-=h.top+parseInt(X(i,"border-top-width")),l-=h.left+parseInt(X(i,"border-left-width")),s=a+r.height,u=l+r.width;break}}while(i=i.parentNode);if(o&&t!==window){var p=Y(i||t),f=p&&p.a,m=p&&p.d;p&&(s=(a/=m)+(c/=m),u=(l/=f)+(d/=f))}return{top:a,left:l,bottom:s,right:u,width:d,height:c}}}function q(t,e,n){for(var o=U(t,!0),i=Q(t)[e];o;){var r=Q(o)[n];if(!("top"===n||"left"===n?i>=r:i<=r))return o;if(o===j())break;o=U(o,!1)}return!1}function H(t,e,n,o){for(var i=0,r=0,a=t.children;r<a.length;){if("none"!==a[r].style.display&&a[r]!==Kt.ghost&&(o||a[r]!==Kt.dragged)&&A(a[r],n.draggable,t,!1)){if(i===e)return a[r];i++}r++}return null}function W(t,e){for(var n=t.lastElementChild;n&&(n===Kt.ghost||"none"===X(n,"display")||e&&!M(n,e));)n=n.previousElementSibling;return n||null}function z(t,e){var n=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)"TEMPLATE"===t.nodeName.toUpperCase()||t===Kt.clone||e&&!M(t,e)||n++;return n}function G(t){var e=0,n=0,o=j();if(t)do{var i=Y(t),r=i.a,a=i.d;e+=t.scrollLeft*r,n+=t.scrollTop*a}while(t!==o&&(t=t.parentNode));return[e,n]}function U(t,e){if(!t||!t.getBoundingClientRect)return j();var n=t,o=!1;do{if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var i=X(n);if(n.clientWidth<n.scrollWidth&&("auto"==i.overflowX||"scroll"==i.overflowX)||n.clientHeight<n.scrollHeight&&("auto"==i.overflowY||"scroll"==i.overflowY)){if(!n.getBoundingClientRect||n===document.body)return j();if(o||e)return n;o=!0}}}while(n=n.parentNode);return j()}function V(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}function Z(t,e){return function(){if(!B){var n=arguments;1===n.length?t.call(this,n[0]):t.apply(this,n),B=setTimeout(function(){B=void 0},e)}}}function K(t,e,n){t.scrollLeft+=e,t.scrollTop+=n}function J(t){var e=window.Polymer,n=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):n?n(t).clone(!0)[0]:t.cloneNode(!0)}function tt(t,e,n){var o={};return Array.from(t.children).forEach(function(i){var r,a,l,s;if(A(i,e.draggable,t,!1)&&!i.animated&&i!==n){var u=Q(i);o.left=Math.min(null!==(r=o.left)&&void 0!==r?r:1/0,u.left),o.top=Math.min(null!==(a=o.top)&&void 0!==a?a:1/0,u.top),o.right=Math.max(null!==(l=o.right)&&void 0!==l?l:-1/0,u.right),o.bottom=Math.max(null!==(s=o.bottom)&&void 0!==s?s:-1/0,u.bottom)}}),o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}var et="Sortable"+(new Date).getTime();var nt=[],ot={initializeByDefault:!0},it={mount:function(t){for(var e in ot)ot.hasOwnProperty(e)&&!(e in t)&&(t[e]=ot[e]);nt.forEach(function(e){if(e.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")}),nt.push(t)},pluginEvent:function(t,e,n){var o=this;this.eventCanceled=!1,n.cancel=function(){o.eventCanceled=!0};var i=t+"Global";nt.forEach(function(o){e[o.pluginName]&&(e[o.pluginName][i]&&e[o.pluginName][i](b({sortable:e},n)),e.options[o.pluginName]&&e[o.pluginName][t]&&e[o.pluginName][t](b({sortable:e},n)))})},initializePlugins:function(t,e,n,o){for(var i in nt.forEach(function(o){var i=o.pluginName;if(t.options[i]||o.initializeByDefault){var r=new o(t,e,t.options);r.sortable=t,r.options=t.options,t[i]=r,D(n,r.defaults)}}),t.options)if(t.options.hasOwnProperty(i)){var r=this.modifyOption(t,i,t.options[i]);void 0!==r&&(t.options[i]=r)}},getEventProperties:function(t,e){var n={};return nt.forEach(function(o){"function"==typeof o.eventProperties&&D(n,o.eventProperties.call(e[o.pluginName],t))}),n},modifyOption:function(t,e,n){var o;return nt.forEach(function(i){t[i.pluginName]&&i.optionListeners&&"function"==typeof i.optionListeners[e]&&(o=i.optionListeners[e].call(t[i.pluginName],n))}),o}};var rt=["evt"],at=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.evt,i=function(t,e){if(null==t)return{};var n,o,i=function(t,e){if(null==t)return{};var n,o,i={},r=Object.keys(t);for(o=0;o<r.length;o++)n=r[o],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(o=0;o<r.length;o++)n=r[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}(n,rt);it.pluginEvent.bind(Kt)(t,e,b({dragEl:st,parentEl:ut,ghostEl:ct,rootEl:dt,nextEl:ht,lastDownEl:pt,cloneEl:ft,cloneHidden:mt,dragStarted:Ct,putSortable:Dt,activeSortable:Kt.active,originalEvent:o,oldIndex:gt,oldDraggableIndex:bt,newIndex:vt,newDraggableIndex:yt,hideGhostForTarget:Ut,unhideGhostForTarget:Vt,cloneNowHidden:function(){mt=!0},cloneNowShown:function(){mt=!1},dispatchSortableEvent:function(t){lt({sortable:e,name:t,originalEvent:o})}},i))};function lt(t){!function(t){var e=t.sortable,n=t.rootEl,o=t.name,i=t.targetEl,r=t.cloneEl,a=t.toEl,l=t.fromEl,s=t.oldIndex,u=t.newIndex,c=t.oldDraggableIndex,d=t.newDraggableIndex,h=t.originalEvent,p=t.putSortable,f=t.extraEventProperties;if(e=e||n&&n[et]){var m,g=e.options,v="on"+o.charAt(0).toUpperCase()+o.substr(1);!window.CustomEvent||S||_?(m=document.createEvent("Event")).initEvent(o,!0,!0):m=new CustomEvent(o,{bubbles:!0,cancelable:!0}),m.to=a||n,m.from=l||n,m.item=i||n,m.clone=r,m.oldIndex=s,m.newIndex=u,m.oldDraggableIndex=c,m.newDraggableIndex=d,m.originalEvent=h,m.pullMode=p?p.lastPutMode:void 0;var y=b(b({},f),it.getEventProperties(o,e));for(var w in y)m[w]=y[w];n&&n.dispatchEvent(m),g[v]&&g[v].call(e,m)}}(b({putSortable:Dt,cloneEl:ft,targetEl:st,rootEl:dt,oldIndex:gt,oldDraggableIndex:bt,newIndex:vt,newDraggableIndex:yt},t))}var st,ut,ct,dt,ht,pt,ft,mt,gt,vt,bt,yt,wt,Dt,Et,St,_t,Tt,xt,kt,Ct,Nt,It,Ot,Mt,Pt=!1,At=!1,Bt=[],Lt=!1,Rt=!1,Xt=[],Yt=!1,Ft=[],jt="undefined"!=typeof document,Qt=k,qt=_||S?"cssFloat":"float",Ht=jt&&!C&&!k&&"draggable"in document.createElement("div"),Wt=function(){if(jt){if(S)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto","auto"===t.style.pointerEvents}}(),zt=function(t,e){var n=X(t),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),i=H(t,0,e),r=H(t,1,e),a=i&&X(i),l=r&&X(r),s=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+Q(i).width,u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+Q(r).width;if("flex"===n.display)return"column"===n.flexDirection||"column-reverse"===n.flexDirection?"vertical":"horizontal";if("grid"===n.display)return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&a.float&&"none"!==a.float){var c="left"===a.float?"left":"right";return!r||"both"!==l.clear&&l.clear!==c?"horizontal":"vertical"}return i&&("block"===a.display||"flex"===a.display||"table"===a.display||"grid"===a.display||s>=o&&"none"===n[qt]||r&&"none"===n[qt]&&s+u>o)?"vertical":"horizontal"},Gt=function(t){function e(t,n){return function(o,i,r,a){var l=o.options.group.name&&i.options.group.name&&o.options.group.name===i.options.group.name;if(null==t&&(n||l))return!0;if(null==t||!1===t)return!1;if(n&&"clone"===t)return t;if("function"==typeof t)return e(t(o,i,r,a),n)(o,i,r,a);var s=(n?o:i).options.group.name;return!0===t||"string"==typeof t&&t===s||t.join&&t.indexOf(s)>-1}}var n={},o=t.group;o&&"object"==y(o)||(o={name:o}),n.name=o.name,n.checkPull=e(o.pull,!0),n.checkPut=e(o.put),n.revertClone=o.revertClone,t.group=n},Ut=function(){!Wt&&ct&&X(ct,"display","none")},Vt=function(){!Wt&&ct&&X(ct,"display","")};jt&&!C&&document.addEventListener("click",function(t){if(At)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),At=!1,!1},!0);var Zt=function(t){if(st){t=t.touches?t.touches[0]:t;var e=(i=t.clientX,r=t.clientY,Bt.some(function(t){var e=t[et].options.emptyInsertThreshold;if(e&&!W(t)){var n=Q(t),o=i>=n.left-e&&i<=n.right+e,l=r>=n.top-e&&r<=n.bottom+e;return o&&l?a=t:void 0}}),a);if(e){var n={};for(var o in t)t.hasOwnProperty(o)&&(n[o]=t[o]);n.target=n.rootEl=e,n.preventDefault=void 0,n.stopPropagation=void 0,e[et]._onDragOver(n)}}var i,r,a},$t=function(t){st&&st.parentNode[et]._isOutsideThisEl(t.target)};function Kt(t,e){if(!t||!t.nodeType||1!==t.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=D({},e),t[et]=this;var n,o,i={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return zt(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(t,e){t.setData("Text",e.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Kt.supportPointer&&"PointerEvent"in window&&(!x||k),emptyInsertThreshold:5};for(var r in it.initializePlugins(this,t,i),i)!(r in e)&&(e[r]=i[r]);for(var a in Gt(e),this)"_"===a.charAt(0)&&"function"==typeof this[a]&&(this[a]=this[a].bind(this));this.nativeDraggable=!e.forceFallback&&Ht,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?I(t,"pointerdown",this._onTapStart):(I(t,"mousedown",this._onTapStart),I(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(I(t,"dragover",this),I(t,"dragenter",this)),Bt.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),D(this,(o=[],{captureAnimationState:function(){o=[],this.options.animation&&[].slice.call(this.el.children).forEach(function(t){if("none"!==X(t,"display")&&t!==Kt.ghost){o.push({target:t,rect:Q(t)});var e=b({},o[o.length-1].rect);if(t.thisAnimationDuration){var n=Y(t,!0);n&&(e.top-=n.f,e.left-=n.e)}t.fromRect=e}})},addAnimationState:function(t){o.push(t)},removeAnimationState:function(t){o.splice(function(t,e){for(var n in t)if(t.hasOwnProperty(n))for(var o in e)if(e.hasOwnProperty(o)&&e[o]===t[n][o])return Number(n);return-1}(o,{target:t}),1)},animateAll:function(t){var e=this;if(!this.options.animation)return clearTimeout(n),void("function"==typeof t&&t());var i=!1,r=0;o.forEach(function(t){var n=0,o=t.target,a=o.fromRect,l=Q(o),s=o.prevFromRect,u=o.prevToRect,c=t.rect,d=Y(o,!0);d&&(l.top-=d.f,l.left-=d.e),o.toRect=l,o.thisAnimationDuration&&V(s,l)&&!V(a,l)&&(c.top-l.top)/(c.left-l.left)===(a.top-l.top)/(a.left-l.left)&&(n=function(t,e,n,o){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))*o.animation}(c,s,u,e.options)),V(l,a)||(o.prevFromRect=a,o.prevToRect=l,n||(n=e.options.animation),e.animate(o,c,l,n)),n&&(i=!0,r=Math.max(r,n),clearTimeout(o.animationResetTimer),o.animationResetTimer=setTimeout(function(){o.animationTime=0,o.prevFromRect=null,o.fromRect=null,o.prevToRect=null,o.thisAnimationDuration=null},n),o.thisAnimationDuration=n)}),clearTimeout(n),i?n=setTimeout(function(){"function"==typeof t&&t()},r):"function"==typeof t&&t(),o=[]},animate:function(t,e,n,o){if(o){X(t,"transition",""),X(t,"transform","");var i=Y(this.el),r=i&&i.a,a=i&&i.d,l=(e.left-n.left)/(r||1),s=(e.top-n.top)/(a||1);t.animatingX=!!l,t.animatingY=!!s,X(t,"transform","translate3d("+l+"px,"+s+"px,0)"),this.forRepaintDummy=function(t){return t.offsetWidth}(t),X(t,"transition","transform "+o+"ms"+(this.options.easing?" "+this.options.easing:"")),X(t,"transform","translate3d(0,0,0)"),"number"==typeof t.animated&&clearTimeout(t.animated),t.animated=setTimeout(function(){X(t,"transition",""),X(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1},o)}}}))}function Jt(t,e,n,o,i,r,a,l){var s,u,c=t[et],d=c.options.onMove;return!window.CustomEvent||S||_?(s=document.createEvent("Event")).initEvent("move",!0,!0):s=new CustomEvent("move",{bubbles:!0,cancelable:!0}),s.to=e,s.from=t,s.dragged=n,s.draggedRect=o,s.related=i||e,s.relatedRect=r||Q(e),s.willInsertAfter=l,s.originalEvent=a,t.dispatchEvent(s),d&&(u=d.call(c,s,a)),u}function te(t){t.draggable=!1}function ee(){Yt=!1}function ne(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,n=e.length,o=0;n--;)o+=e.charCodeAt(n);return o.toString(36)}function oe(t){return setTimeout(t,0)}function ie(t){return clearTimeout(t)}Kt.prototype={constructor:Kt,_isOutsideThisEl:function(t){this.el.contains(t)||t===this.el||(Nt=null)},_getDirection:function(t,e){return"function"==typeof this.options.direction?this.options.direction.call(this,t,e,st):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,n=this.el,o=this.options,i=o.preventOnFilter,r=t.type,a=t.touches&&t.touches[0]||t.pointerType&&"touch"===t.pointerType&&t,l=(a||t).target,s=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||l,u=o.filter;if(function(t){Ft.length=0;for(var e=t.getElementsByTagName("input"),n=e.length;n--;){var o=e[n];o.checked&&Ft.push(o)}}(n),!st&&!(/mousedown|pointerdown/.test(r)&&0!==t.button||o.disabled)&&!s.isContentEditable&&(this.nativeDraggable||!x||!l||"SELECT"!==l.tagName.toUpperCase())&&!((l=A(l,o.draggable,n,!1))&&l.animated||pt===l)){if(gt=z(l),bt=z(l,o.draggable),"function"==typeof u){if(u.call(this,t,l,this))return lt({sortable:e,rootEl:s,name:"filter",targetEl:l,toEl:n,fromEl:n}),at("filter",e,{evt:t}),void(i&&t.preventDefault())}else if(u&&(u=u.split(",").some(function(o){if(o=A(s,o.trim(),n,!1))return lt({sortable:e,rootEl:o,name:"filter",targetEl:l,fromEl:n,toEl:n}),at("filter",e,{evt:t}),!0})))return void(i&&t.preventDefault());o.handle&&!A(s,o.handle,n,!1)||this._prepareDragStart(t,a,l)}}},_prepareDragStart:function(t,e,n){var o,i=this,r=i.el,a=i.options,l=r.ownerDocument;if(n&&!st&&n.parentNode===r){var s=Q(n);if(dt=r,ut=(st=n).parentNode,ht=st.nextSibling,pt=n,wt=a.group,Kt.dragged=st,Et={target:st,clientX:(e||t).clientX,clientY:(e||t).clientY},xt=Et.clientX-s.left,kt=Et.clientY-s.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,st.style["will-change"]="all",o=function(){at("delayEnded",i,{evt:t}),Kt.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!T&&i.nativeDraggable&&(st.draggable=!0),i._triggerDragStart(t,e),lt({sortable:i,name:"choose",originalEvent:t}),R(st,a.chosenClass,!0))},a.ignore.split(",").forEach(function(t){F(st,t.trim(),te)}),I(l,"dragover",Zt),I(l,"mousemove",Zt),I(l,"touchmove",Zt),a.supportPointer?(I(l,"pointerup",i._onDrop),!this.nativeDraggable&&I(l,"pointercancel",i._onDrop)):(I(l,"mouseup",i._onDrop),I(l,"touchend",i._onDrop),I(l,"touchcancel",i._onDrop)),T&&this.nativeDraggable&&(this.options.touchStartThreshold=4,st.draggable=!0),at("delayStart",this,{evt:t}),!a.delay||a.delayOnTouchOnly&&!e||this.nativeDraggable&&(_||S))o();else{if(Kt.eventCanceled)return void this._onDrop();a.supportPointer?(I(l,"pointerup",i._disableDelayedDrag),I(l,"pointercancel",i._disableDelayedDrag)):(I(l,"mouseup",i._disableDelayedDrag),I(l,"touchend",i._disableDelayedDrag),I(l,"touchcancel",i._disableDelayedDrag)),I(l,"mousemove",i._delayedDragTouchMoveHandler),I(l,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&I(l,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(o,a.delay)}}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){st&&te(st),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;O(t,"mouseup",this._disableDelayedDrag),O(t,"touchend",this._disableDelayedDrag),O(t,"touchcancel",this._disableDelayedDrag),O(t,"pointerup",this._disableDelayedDrag),O(t,"pointercancel",this._disableDelayedDrag),O(t,"mousemove",this._delayedDragTouchMoveHandler),O(t,"touchmove",this._delayedDragTouchMoveHandler),O(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||"touch"==t.pointerType&&t,!this.nativeDraggable||e?this.options.supportPointer?I(document,"pointermove",this._onTouchMove):I(document,e?"touchmove":"mousemove",this._onTouchMove):(I(st,"dragend",this),I(dt,"dragstart",this._onDragStart));try{document.selection?oe(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(t){}},_dragStarted:function(t,e){if(Pt=!1,dt&&st){at("dragStarted",this,{evt:e}),this.nativeDraggable&&I(document,"dragover",$t);var n=this.options;!t&&R(st,n.dragClass,!1),R(st,n.ghostClass,!0),Kt.active=this,t&&this._appendGhost(),lt({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(St){this._lastX=St.clientX,this._lastY=St.clientY,Ut();for(var t=document.elementFromPoint(St.clientX,St.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(St.clientX,St.clientY))!==e;)e=t;if(st.parentNode[et]._isOutsideThisEl(t),e)do{if(e[et]&&e[et]._onDragOver({clientX:St.clientX,clientY:St.clientY,target:t,rootEl:e})&&!this.options.dragoverBubble)break;t=e}while(e=P(e));Vt()}},_onTouchMove:function(t){if(Et){var e=this.options,n=e.fallbackTolerance,o=e.fallbackOffset,i=t.touches?t.touches[0]:t,r=ct&&Y(ct,!0),a=ct&&r&&r.a,l=ct&&r&&r.d,s=Qt&&Mt&&G(Mt),u=(i.clientX-Et.clientX+o.x)/(a||1)+(s?s[0]-Xt[0]:0)/(a||1),c=(i.clientY-Et.clientY+o.y)/(l||1)+(s?s[1]-Xt[1]:0)/(l||1);if(!Kt.active&&!Pt){if(n&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}if(ct){r?(r.e+=u-(_t||0),r.f+=c-(Tt||0)):r={a:1,b:0,c:0,d:1,e:u,f:c};var d="matrix(".concat(r.a,",").concat(r.b,",").concat(r.c,",").concat(r.d,",").concat(r.e,",").concat(r.f,")");X(ct,"webkitTransform",d),X(ct,"mozTransform",d),X(ct,"msTransform",d),X(ct,"transform",d),_t=u,Tt=c,St=i}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!ct){var t=this.options.fallbackOnBody?document.body:dt,e=Q(st,!0,Qt,!0,t),n=this.options;if(Qt){for(Mt=t;"static"===X(Mt,"position")&&"none"===X(Mt,"transform")&&Mt!==document;)Mt=Mt.parentNode;Mt!==document.body&&Mt!==document.documentElement?(Mt===document&&(Mt=j()),e.top+=Mt.scrollTop,e.left+=Mt.scrollLeft):Mt=j(),Xt=G(Mt)}R(ct=st.cloneNode(!0),n.ghostClass,!1),R(ct,n.fallbackClass,!0),R(ct,n.dragClass,!0),X(ct,"transition",""),X(ct,"transform",""),X(ct,"box-sizing","border-box"),X(ct,"margin",0),X(ct,"top",e.top),X(ct,"left",e.left),X(ct,"width",e.width),X(ct,"height",e.height),X(ct,"opacity","0.8"),X(ct,"position",Qt?"absolute":"fixed"),X(ct,"zIndex","100000"),X(ct,"pointerEvents","none"),Kt.ghost=ct,t.appendChild(ct),X(ct,"transform-origin",xt/parseInt(ct.style.width)*100+"% "+kt/parseInt(ct.style.height)*100+"%")}},_onDragStart:function(t,e){var n=this,o=t.dataTransfer,i=n.options;at("dragStart",this,{evt:t}),Kt.eventCanceled?this._onDrop():(at("setupClone",this),Kt.eventCanceled||((ft=J(st)).removeAttribute("id"),ft.draggable=!1,ft.style["will-change"]="",this._hideClone(),R(ft,this.options.chosenClass,!1),Kt.clone=ft),n.cloneId=oe(function(){at("clone",n),Kt.eventCanceled||(n.options.removeCloneOnHide||dt.insertBefore(ft,st),n._hideClone(),lt({sortable:n,name:"clone"}))}),!e&&R(st,i.dragClass,!0),e?(At=!0,n._loopId=setInterval(n._emulateDragOver,50)):(O(document,"mouseup",n._onDrop),O(document,"touchend",n._onDrop),O(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",i.setData&&i.setData.call(n,o,st)),I(document,"drop",n),X(st,"transform","translateZ(0)")),Pt=!0,n._dragStartId=oe(n._dragStarted.bind(n,e,t)),I(document,"selectstart",n),Ct=!0,window.getSelection().removeAllRanges(),x&&X(document.body,"user-select","none"))},_onDragOver:function(t){var e,n,o,i,r=this.el,a=t.target,l=this.options,s=l.group,u=Kt.active,c=wt===s,d=l.sort,h=Dt||u,p=this,f=!1;if(!Yt){if(void 0!==t.preventDefault&&t.cancelable&&t.preventDefault(),a=A(a,l.draggable,r,!0),I("dragOver"),Kt.eventCanceled)return f;if(st.contains(t.target)||a.animated&&a.animatingX&&a.animatingY||p._ignoreWhileAnimating===a)return M(!1);if(At=!1,u&&!l.disabled&&(c?d||(o=ut!==dt):Dt===this||(this.lastPutMode=wt.checkPull(this,u,st,t))&&s.checkPut(this,u,st,t))){if(i="vertical"===this._getDirection(t,a),e=Q(st),I("dragOverValid"),Kt.eventCanceled)return f;if(o)return ut=dt,O(),this._hideClone(),I("revert"),Kt.eventCanceled||(ht?dt.insertBefore(st,ht):dt.appendChild(st)),M(!0);var m=W(r,l.draggable);if(!m||function(t,e,n){var o=Q(W(n.el,n.options.draggable)),i=tt(n.el,n.options,ct);return e?t.clientX>i.right+10||t.clientY>o.bottom&&t.clientX>o.left:t.clientY>i.bottom+10||t.clientX>o.right&&t.clientY>o.top}(t,i,this)&&!m.animated){if(m===st)return M(!1);if(m&&r===t.target&&(a=m),a&&(n=Q(a)),!1!==Jt(dt,r,st,e,a,n,t,!!a))return O(),m&&m.nextSibling?r.insertBefore(st,m.nextSibling):r.appendChild(st),ut=r,P(),M(!0)}else if(m&&function(t,e,n){var o=Q(H(n.el,0,n.options,!0)),i=tt(n.el,n.options,ct);return e?t.clientX<i.left-10||t.clientY<o.top&&t.clientX<o.right:t.clientY<i.top-10||t.clientY<o.bottom&&t.clientX<o.left}(t,i,this)){var g=H(r,0,l,!0);if(g===st)return M(!1);if(n=Q(a=g),!1!==Jt(dt,r,st,e,a,n,t,!1))return O(),r.insertBefore(st,g),ut=r,P(),M(!0)}else if(a.parentNode===r){n=Q(a);var v,y,w,D=st.parentNode!==r,E=!function(t,e,n){var o=n?t.left:t.top,i=n?t.right:t.bottom,r=n?t.width:t.height,a=n?e.left:e.top,l=n?e.right:e.bottom,s=n?e.width:e.height;return o===a||i===l||o+r/2===a+s/2}(st.animated&&st.toRect||e,a.animated&&a.toRect||n,i),S=i?"top":"left",_=q(a,"top","top")||q(st,"top","top"),T=_?_.scrollTop:void 0;if(Nt!==a&&(y=n[S],Lt=!1,Rt=!E&&l.invertSwap||D),v=function(t,e,n,o,i,r,a,l){var s=o?t.clientY:t.clientX,u=o?n.height:n.width,c=o?n.top:n.left,d=o?n.bottom:n.right,h=!1;if(!a)if(l&&Ot<u*i){if(!Lt&&(1===It?s>c+u*r/2:s<d-u*r/2)&&(Lt=!0),Lt)h=!0;else if(1===It?s<c+Ot:s>d-Ot)return-It}else if(s>c+u*(1-i)/2&&s<d-u*(1-i)/2)return function(t){return z(st)<z(t)?1:-1}(e);return(h=h||a)&&(s<c+u*r/2||s>d-u*r/2)?s>c+u/2?1:-1:0}(t,a,n,i,E?1:l.swapThreshold,null==l.invertedSwapThreshold?l.swapThreshold:l.invertedSwapThreshold,Rt,Nt===a),0!==v){var x=z(st);do{x-=v,w=ut.children[x]}while(w&&("none"===X(w,"display")||w===ct))}if(0===v||w===a)return M(!1);Nt=a,It=v;var k=a.nextElementSibling,C=!1,N=Jt(dt,r,st,e,a,n,t,C=1===v);if(!1!==N)return 1!==N&&-1!==N||(C=1===N),Yt=!0,setTimeout(ee,30),O(),C&&!k?r.appendChild(st):a.parentNode.insertBefore(st,C?k:a),_&&K(_,0,T-_.scrollTop),ut=st.parentNode,void 0===y||Rt||(Ot=Math.abs(y-Q(a)[S])),P(),M(!0)}if(r.contains(st))return M(!1)}return!1}function I(l,s){at(l,p,b({evt:t,isOwner:c,axis:i?"vertical":"horizontal",revert:o,dragRect:e,targetRect:n,canSort:d,fromSortable:h,target:a,completed:M,onMove:function(n,o){return Jt(dt,r,st,e,n,Q(n),t,o)},changed:P},s))}function O(){I("dragOverAnimationCapture"),p.captureAnimationState(),p!==h&&h.captureAnimationState()}function M(e){return I("dragOverCompleted",{insertion:e}),e&&(c?u._hideClone():u._showClone(p),p!==h&&(R(st,Dt?Dt.options.ghostClass:u.options.ghostClass,!1),R(st,l.ghostClass,!0)),Dt!==p&&p!==Kt.active?Dt=p:p===Kt.active&&Dt&&(Dt=null),h===p&&(p._ignoreWhileAnimating=a),p.animateAll(function(){I("dragOverAnimationComplete"),p._ignoreWhileAnimating=null}),p!==h&&(h.animateAll(),h._ignoreWhileAnimating=null)),(a===st&&!st.animated||a===r&&!a.animated)&&(Nt=null),l.dragoverBubble||t.rootEl||a===document||(st.parentNode[et]._isOutsideThisEl(t.target),!e&&Zt(t)),!l.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),f=!0}function P(){vt=z(st),yt=z(st,l.draggable),lt({sortable:p,name:"change",toEl:r,newIndex:vt,newDraggableIndex:yt,originalEvent:t})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){O(document,"mousemove",this._onTouchMove),O(document,"touchmove",this._onTouchMove),O(document,"pointermove",this._onTouchMove),O(document,"dragover",Zt),O(document,"mousemove",Zt),O(document,"touchmove",Zt)},_offUpEvents:function(){var t=this.el.ownerDocument;O(t,"mouseup",this._onDrop),O(t,"touchend",this._onDrop),O(t,"pointerup",this._onDrop),O(t,"pointercancel",this._onDrop),O(t,"touchcancel",this._onDrop),O(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;vt=z(st),yt=z(st,n.draggable),at("drop",this,{evt:t}),ut=st&&st.parentNode,vt=z(st),yt=z(st,n.draggable),Kt.eventCanceled||(Pt=!1,Rt=!1,Lt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ie(this.cloneId),ie(this._dragStartId),this.nativeDraggable&&(O(document,"drop",this),O(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),x&&X(document.body,"user-select",""),X(st,"transform",""),t&&(Ct&&(t.cancelable&&t.preventDefault(),!n.dropBubble&&t.stopPropagation()),ct&&ct.parentNode&&ct.parentNode.removeChild(ct),(dt===ut||Dt&&"clone"!==Dt.lastPutMode)&&ft&&ft.parentNode&&ft.parentNode.removeChild(ft),st&&(this.nativeDraggable&&O(st,"dragend",this),te(st),st.style["will-change"]="",Ct&&!Pt&&R(st,Dt?Dt.options.ghostClass:this.options.ghostClass,!1),R(st,this.options.chosenClass,!1),lt({sortable:this,name:"unchoose",toEl:ut,newIndex:null,newDraggableIndex:null,originalEvent:t}),dt!==ut?(vt>=0&&(lt({rootEl:ut,name:"add",toEl:ut,fromEl:dt,originalEvent:t}),lt({sortable:this,name:"remove",toEl:ut,originalEvent:t}),lt({rootEl:ut,name:"sort",toEl:ut,fromEl:dt,originalEvent:t}),lt({sortable:this,name:"sort",toEl:ut,originalEvent:t})),Dt&&Dt.save()):vt!==gt&&vt>=0&&(lt({sortable:this,name:"update",toEl:ut,originalEvent:t}),lt({sortable:this,name:"sort",toEl:ut,originalEvent:t})),Kt.active&&(null!=vt&&-1!==vt||(vt=gt,yt=bt),lt({sortable:this,name:"end",toEl:ut,originalEvent:t}),this.save())))),this._nulling()},_nulling:function(){at("nulling",this),dt=st=ut=ct=ht=ft=pt=mt=Et=St=Ct=vt=yt=gt=bt=Nt=It=Dt=wt=Kt.dragged=Kt.ghost=Kt.clone=Kt.active=null,Ft.forEach(function(t){t.checked=!0}),Ft.length=_t=Tt=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":st&&(this._onDragOver(t),function(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}(t));break;case"selectstart":t.preventDefault()}},toArray:function(){for(var t,e=[],n=this.el.children,o=0,i=n.length,r=this.options;o<i;o++)A(t=n[o],r.draggable,this.el,!1)&&e.push(t.getAttribute(r.dataIdAttr)||ne(t));return e},sort:function(t,e){var n={},o=this.el;this.toArray().forEach(function(t,e){var i=o.children[e];A(i,this.options.draggable,o,!1)&&(n[t]=i)},this),e&&this.captureAnimationState(),t.forEach(function(t){n[t]&&(o.removeChild(n[t]),o.appendChild(n[t]))}),e&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return A(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(void 0===e)return n[t];var o=it.modifyOption(this,t,e);n[t]=void 0!==o?o:e,"group"===t&&Gt(n)},destroy:function(){at("destroy",this);var t=this.el;t[et]=null,O(t,"mousedown",this._onTapStart),O(t,"touchstart",this._onTapStart),O(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(O(t,"dragover",this),O(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Bt.splice(Bt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!mt){if(at("hideClone",this),Kt.eventCanceled)return;X(ft,"display","none"),this.options.removeCloneOnHide&&ft.parentNode&&ft.parentNode.removeChild(ft),mt=!0}},_showClone:function(t){if("clone"===t.lastPutMode){if(mt){if(at("showClone",this),Kt.eventCanceled)return;st.parentNode!=dt||this.options.group.revertClone?ht?dt.insertBefore(ft,ht):dt.appendChild(ft):dt.insertBefore(ft,st),this.options.group.revertClone&&this.animate(st,ft),X(ft,"display",""),mt=!1}}else this._hideClone()}},jt&&I(document,"touchmove",function(t){(Kt.active||Pt)&&t.cancelable&&t.preventDefault()}),Kt.utils={on:I,off:O,css:X,find:F,is:function(t,e){return!!A(t,e,t,!1)},extend:function(t,e){if(t&&e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},throttle:Z,closest:A,toggleClass:R,clone:J,index:z,nextTick:oe,cancelNextTick:ie,detectDirection:zt,getChild:H,expando:et},Kt.get=function(t){return t[et]},Kt.mount=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e[0].constructor===Array&&(e=e[0]),e.forEach(function(t){if(!t.prototype||!t.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(t));t.utils&&(Kt.utils=b(b({},Kt.utils),t.utils)),it.mount(t)})},Kt.create=function(t,e){return new Kt(t,e)},Kt.version="1.15.6";var re,ae,le,se,ue,ce,de=[],he=!1;function pe(){de.forEach(function(t){clearInterval(t.pid)}),de=[]}function fe(){clearInterval(ce)}var me=Z(function(t,e,n,o){if(e.scroll){var i,r=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,l=e.scrollSensitivity,s=e.scrollSpeed,u=j(),c=!1;ae!==n&&(ae=n,pe(),re=e.scroll,i=e.scrollFn,!0===re&&(re=U(n,!0)));var d=0,h=re;do{var p=h,f=Q(p),m=f.top,g=f.bottom,v=f.left,b=f.right,y=f.width,w=f.height,D=void 0,E=void 0,S=p.scrollWidth,_=p.scrollHeight,T=X(p),x=p.scrollLeft,k=p.scrollTop;p===u?(D=y<S&&("auto"===T.overflowX||"scroll"===T.overflowX||"visible"===T.overflowX),E=w<_&&("auto"===T.overflowY||"scroll"===T.overflowY||"visible"===T.overflowY)):(D=y<S&&("auto"===T.overflowX||"scroll"===T.overflowX),E=w<_&&("auto"===T.overflowY||"scroll"===T.overflowY));var C=D&&(Math.abs(b-r)<=l&&x+y<S)-(Math.abs(v-r)<=l&&!!x),N=E&&(Math.abs(g-a)<=l&&k+w<_)-(Math.abs(m-a)<=l&&!!k);if(!de[d])for(var I=0;I<=d;I++)de[I]||(de[I]={});de[d].vx==C&&de[d].vy==N&&de[d].el===p||(de[d].el=p,de[d].vx=C,de[d].vy=N,clearInterval(de[d].pid),0==C&&0==N||(c=!0,de[d].pid=setInterval(function(){o&&0===this.layer&&Kt.active._onTouchMove(ue);var e=de[this.layer].vy?de[this.layer].vy*s:0,n=de[this.layer].vx?de[this.layer].vx*s:0;"function"==typeof i&&"continue"!==i.call(Kt.dragged.parentNode[et],n,e,t,ue,de[this.layer].el)||K(de[this.layer].el,n,e)}.bind({layer:d}),24))),d++}while(e.bubbleScroll&&h!==u&&(h=U(h,!1)));he=c}},30),ge=function(t){var e=t.originalEvent,n=t.putSortable,o=t.dragEl,i=t.activeSortable,r=t.dispatchSortableEvent,a=t.hideGhostForTarget,l=t.unhideGhostForTarget;if(e){var s=n||i;a();var u=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,c=document.elementFromPoint(u.clientX,u.clientY);l(),s&&!s.el.contains(c)&&(r("spill"),this.onSpill({dragEl:o,putSortable:n}))}};function ve(){}function be(){}ve.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){var e=t.dragEl,n=t.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var o=H(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(e,o):this.sortable.el.appendChild(e),this.sortable.animateAll(),n&&n.animateAll()},drop:ge},D(ve,{pluginName:"revertOnSpill"}),be.prototype={onSpill:function(t){var e=t.dragEl,n=t.putSortable||this.sortable;n.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),n.animateAll()},drop:ge},D(be,{pluginName:"removeOnSpill"}),Kt.mount(new function(){function t(){for(var t in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===t.charAt(0)&&"function"==typeof this[t]&&(this[t]=this[t].bind(this))}return t.prototype={dragStarted:function(t){var e=t.originalEvent;this.sortable.nativeDraggable?I(document,"dragover",this._handleAutoScroll):this.options.supportPointer?I(document,"pointermove",this._handleFallbackAutoScroll):e.touches?I(document,"touchmove",this._handleFallbackAutoScroll):I(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var e=t.originalEvent;this.options.dragOverBubble||e.rootEl||this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?O(document,"dragover",this._handleAutoScroll):(O(document,"pointermove",this._handleFallbackAutoScroll),O(document,"touchmove",this._handleFallbackAutoScroll),O(document,"mousemove",this._handleFallbackAutoScroll)),fe(),pe(),clearTimeout(B),B=void 0},nulling:function(){ue=ae=re=he=ce=le=se=null,de.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,e){var n=this,o=(t.touches?t.touches[0]:t).clientX,i=(t.touches?t.touches[0]:t).clientY,r=document.elementFromPoint(o,i);if(ue=t,e||this.options.forceAutoScrollFallback||_||S||x){me(t,this.options,r,e);var a=U(r,!0);!he||ce&&o===le&&i===se||(ce&&fe(),ce=setInterval(function(){var r=U(document.elementFromPoint(o,i),!0);r!==a&&(a=r,pe()),me(t,n.options,r,e)},10),le=o,se=i)}else{if(!this.options.bubbleScroll||U(r,!0)===j())return void pe();me(t,this.options,U(r,!1),!1)}}},D(t,{pluginName:"scroll",initializeByDefault:!0})}),Kt.mount(be,ve);const ye=Kt;var we=function(t){function e(){return t.apply(this,arguments)||this}n(e,t);var o=e.prototype;return o.oninit=function(e){t.prototype.oninit.call(this,e),this.loading=!1,this.linksQueueList=[],this.loadResults()},o.initSort=function(){var t=this,e=document.getElementById("linksQueueSortableItems");ye.create(e,{animation:150,swapThreshold:.65,onEnd:function(e){return t.updateSort(e)}})},o.content=function(){return m("div",{className:"ExtensionPage-settings FlarumBadgesPage"},m("div",{className:"container"},m("div",{style:{paddingBottom:"10px"}},m(a(),{className:"Button",onclick:function(){return app.modal.show(d)}},app.translator.trans("wusong8899-links-queue.admin.link-add"))),m("ul",{id:"linksQueueSortableItems",style:{padding:"0px",listStyleType:"none"},oncreate:this.initSort.bind(this)},this.linksQueueList.map(function(t){return m("li",{itemID:t.id(),style:{marginTop:"5px",background:"var(--body-bg)"}},g.component({LinksQueueItemData:t}))}))))},o.updateSort=function(t){if(t.newIndex!==t.oldIndex){for(var e=t.from.children,n={},o=0;o<e.length;o++){var i=e[o];n[$(i).attr("itemID")]=o}app.request({url:app.forum.attribute("apiUrl")+"/linksQueueList/order",method:"POST",body:{linkQueueOrder:n}})}},o.parseResults=function(t){return[].push.apply(this.linksQueueList,t),m.redraw(),t},o.loadResults=function(){return app.store.find("linksQueueList").catch(function(){}).then(this.parseResults.bind(this))},e}(i());const De=flarum.core.compat.Model;var Ee=t.n(De),Se=function(t){function e(){return t.apply(this,arguments)||this}return n(e,t),e}(Ee());Object.assign(Se.prototype,{id:Ee().attribute("id"),name:Ee().attribute("name"),links:Ee().attribute("links"),sort:Ee().attribute("sort")}),app.initializers.add("wusong8899-client1-links-queue",function(){app.store.models.linksQueueList=Se,app.extensionData.for("wusong8899-client1-links-queue").registerPage(we)})})(),module.exports={}})();
//# sourceMappingURL=admin.js.map